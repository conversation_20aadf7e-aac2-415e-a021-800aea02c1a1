﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;

namespace RealSoftGames.AdvancedLevelingSystem.Demo
{
    public class AI : MonoBehaviour
    {
        /// <summary>
        /// Event to call when the units currentHealth is modified, used to update the UI Components
        /// </summary>
        public Action<int> OnHealthChange;

        /// <summary>
        /// Used to trigger Death logic as well as turn off components after a unit dies
        /// </summary>
        public Action OnDeath;

        private NavMeshAgent agent;
        [SerializeField] private HealthText healthText;
        [SerializeField] private FloatingText experienceText;
        [SerializeField, Range(1, 10)] private int initializeHealth;
        [SerializeField] private int health = 0;
        [SerializeField, Range(25, 100)] private int initializeExperience = 25;
        [SerializeField] private int experience = 0;
        private Coroutine wander;
        [SerializeField] private Vector3 expTextOffset = new Vector3(0, 0.5f, 0);

        protected virtual void Start()
        {
            //initialize random Health generation for AI and Update the HealthText UI
            health = Random.Range(1, initializeHealth);
            //experience = Random.Range(25, initializeExperience);

            OnHealthChange += healthText.OnHealthChange;
            OnHealthChange?.Invoke(health);

            //NOTE: Subscribe OnDeath event when the unit dies to grant EXP
            OnDeath += Death;

            agent = GetComponent<NavMeshAgent>();
            wander = StartCoroutine(Wander());
        }

        private void OnDestroy()
        {
            OnDeath -= Death;
        }

        private void OnMouseDown()
        {
            TakeDamage(1);
        }

        private void TakeDamage(int value)
        {
            if (health > 0)
            {
                health -= value;
                OnHealthChange?.Invoke(health);
            }

            if (health <= 0)
                OnDeath?.Invoke();
        }

        /// <summary>
        /// Custom Game Death Logic
        /// </summary>
        private void Death()
        {
            //Add custom game logic here
            //For example, have death animation play and audio effects, after a set time have the object fade out with a disolve shader
            OnHealthChange?.Invoke(0);
            agent.isStopped = true;
            GetComponent<Collider>().enabled = false;
            Destroy(gameObject, 10f);
            StopCoroutine(wander);
            transform.localScale = new Vector3(transform.localScale.x, transform.localScale.y / 2, transform.localScale.z);

            //NOTE: Check if we are at max level, do we want to hide the experience bar from the player?
            if (!AdvancedLevelingSystem.Instance.IsMaxLevel)
            {
                transform.AddExperience(experience * Mathf.CeilToInt(Player.EXPModifier), expTextOffset: expTextOffset);
                //Depricated, but will still work as expected. the new method above handles the Text Component from within the Object pool
                //AdvancedLevelingSystem.Instance.AddExperience(experience);
            }
        }

        private IEnumerator Wander()
        {
            while (true)
            {
                float dist = Random.Range(-10f, 10f);
                Vector3 point = transform.position + Random.insideUnitSphere * dist;
                NavMeshHit navHit;

                if (NavMesh.SamplePosition(point, out navHit, Mathf.Abs(dist), NavMesh.AllAreas))
                {
                    point = navHit.position;
                }

                agent.SetDestination(point);

                yield return new WaitForSeconds(3f);
            }
        }
    }
}