using UnityEngine;
using UnityEngine.AI;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class Bullet : MonoBehaviour
    {
        [SerializeField] LayerMask hitmask;
        [SerializeField] private Rigidbody rb;

        public float damage = 2f; // Default damage    
        public float knockback = 0.1f; // Default knockback
        public int penetration = 0; // Default penetration
        [SerializeField, ReadOnly] private int curPenetration = 0;

        private Vector3 bulletDirection;

        private void Awake()
        {
            // Apply initial velocity
            if (rb == null)
                rb = GetComponent<Rigidbody>();

            // Set bullet direction based on initial forward direction
            bulletDirection = transform.forward;
        }

        public void Initialize(float newDamage, float velocity, float newKnockback = 0.1f, int newPenetration = 0)
        {
            damage = newDamage;
            knockback = newKnockback;
            penetration = newPenetration;
            rb.velocity = bulletDirection * velocity;
        }

        private void OnTriggerEnter(Collider col)
        {
            if (col.CompareTag("Enemy"))
            {
                // Apply damage
                ZombieAI zombie = col.GetComponent<ZombieAI>();
                if (zombie != null)
                {
                    zombie.TakeDamage(damage);

                    // Apply knockback to the NavMeshAgent
                    NavMeshAgent agent = col.GetComponent<NavMeshAgent>();
                    if (agent != null)
                    {
                        ApplyKnockbackToNavMeshAgent(agent);
                    }
                }

                // Handle penetration
                curPenetration++;
                if (curPenetration > penetration)
                {
                    Destroy(gameObject);
                }
            }
            else if (((1 << col.gameObject.layer) & hitmask) != 0)
            {
                // If we hit an object in the hitmask, destroy the bullet
                Destroy(gameObject);
            }
        }

        private void ApplyKnockbackToNavMeshAgent(NavMeshAgent agent)
        {
            // Temporarily disable the NavMeshAgent to apply knockback
            agent.isStopped = true;

            // Calculate the knockback position based on bullet direction
            Vector3 knockbackPosition = agent.transform.position + bulletDirection.normalized * knockback;

            // Sample the NavMesh to ensure the position is valid
            NavMeshHit hit;
            if (NavMesh.SamplePosition(knockbackPosition, out hit, 1.0f, NavMesh.AllAreas))
            {
                agent.Warp(hit.position);
            }
            else
            {
                agent.Warp(knockbackPosition); // Use the calculated position if NavMesh sample fails
            }

            // Immediately resume the agent's movement after applying knockback
            agent.isStopped = false;
        }
    }
}
