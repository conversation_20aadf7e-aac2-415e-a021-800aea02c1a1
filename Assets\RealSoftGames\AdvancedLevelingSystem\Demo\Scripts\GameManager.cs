using UnityEngine;
using TMPro;
using RealSoftGames.AdvancedLevelingSystem.Demo;
using UnityEngine.SceneManagement;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class GameManager : MonoBehaviour
    {
        public static bool GameOver = false, IsPaused = false;
        public static Action<bool> PauseGame;
        public static GameManager Instance;
        [SerializeField] private Spawner spawner;
        [SerializeField] private TMP_Text waveText, killsText, remainingText;
        [SerializeField] private GameObject gameOver;
        private int kills;

        private void Awake()
        {
            if (Instance == null || Instance != this)
                Instance = this;
        }

        private void Start()
        {
            GameOver = false;
            Spawner.enemiesRemaining += EnemiesAliveCounter;
            Spawner.OnNextWave += WaveCounter;
            spawner.OnEnemyKilled += KillCounter;
            Player.OnDeath += EndGame;
            PauseGame += Pause;
        }

        private void OnDisable()
        {
            Spawner.enemiesRemaining -= EnemiesAliveCounter;
            Spawner.OnNextWave -= WaveCounter;
            spawner.OnEnemyKilled -= KillCounter;
            Player.OnDeath -= EndGame;
            PauseGame -= Pause;
        }

        private void EnemiesAliveCounter(int enemiesAlive)
        {
            remainingText.text = $"Enemies:{enemiesAlive}";
        }

        private void KillCounter()
        {
            kills++;
            killsText.text = $"Kills: {kills}";
        }

        private void WaveCounter(int wave)
        {
            waveText.text = $"Wave: {wave}";
        }

        private void EndGame()
        {
            GameOver = true;
            gameOver.SetActive(true);
        }

        public void RestartGame()
        {
            SceneManager.LoadScene(SceneManager.GetActiveScene().name);
        }

        private void Pause(bool paused)
        {
            IsPaused = paused;
        }
    }
}