using UnityEngine;
using TMPro;

namespace RealSoftGames.AdvancedLevelingSystem.Demo
{
    public class HealthText : MonoBehaviour
    {
        private Camera cam;
        private TMP_Text text;

        private void Awake()
        {
            cam = Camera.main;
            text = GetComponent<TMP_Text>();
        }

        /// <summary>
        /// Update the currentHealth text UI
        /// </summary>
        /// <param name="value"></param>
        public void OnHealthChange(int value)
        {
            text.text = value.ToString();
            if (value == 0)
                text.gameObject.SetActive(false);
        }

        private void LateUpdate()
        {
            transform.LookAt(transform.position + cam.transform.rotation * Vector3.forward, cam.transform.rotation * Vector3.up);
        }
    }
}