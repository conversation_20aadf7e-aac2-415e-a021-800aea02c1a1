using UnityEngine;
using UnityEngine.AI;
using System.Linq;
using System;
using RealSoftGames.AdvancedLevelingSystem.Demo;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class Player : MonoBehaviour
    {
        public static Player Instance { get; private set; }
        public Action<PowerUp.PowerUpOption, float> OnPowerUp;
        public Action<int> OnhealthChange;
        [SerializeField] private HealthText healthUI;

        public NavMeshAgent agent;
        public GameObject projectilePrefab; // Assign in the Inspector
        public Transform firePoint; // The point from which projectiles are fired

        public LayerMask enemyLayer;
        [SerializeField] private float damage = 3;
        [SerializeField] private float attackCooldown = 2.5f;
        [SerializeField] private float attackRange = 5.0f;
        [SerializeField] private float expModifier = 1.0f;

        private float lastAttackTime = -1f;

        public float maxhealth = 100;
        public float currentHealth = 100f;
        public float movementSpeed = 3.5f;
        public int penetration = 0;
        public float knockback = 0.1f;

        public static float EXPModifier { get => Instance.expModifier; }

        public static Action OnDeath;

        public Collider[] enemiesInRange;

        private void Awake()
        {
            if (Instance != null && Instance != this)
                Destroy(gameObject);
            else
                Instance = this;

            agent.speed = movementSpeed;
            agent.updateRotation = false;
            OnPowerUp += ApplyPowerUp;
        }

        private void Start()
        {
            GameManager.PauseGame += GamePaused;
            currentHealth = maxhealth;
            OnhealthChange += healthUI.OnHealthChange;
            OnhealthChange?.Invoke(Mathf.RoundToInt(currentHealth));
        }

        private void Update()
        {
            if (GameManager.GameOver || GameManager.IsPaused)
            {
                agent.isStopped = true;
                return;
            }
            agent.isStopped = false;

            HandleMovement();
            RotateTowardsClosestEnemy(); // Separate method for rotating towards the closest enemy
            HandleAttacking();
        }

        private void OnDisable()
        {
            OnPowerUp -= ApplyPowerUp;
            OnhealthChange -= healthUI.OnHealthChange;
            GameManager.PauseGame -= GamePaused;
        }

        public void TakeDamage(float amount)
        {
            if (GameManager.GameOver)
                return;

            currentHealth = Mathf.Max(currentHealth - amount, 0);
            OnhealthChange?.Invoke(Mathf.RoundToInt(currentHealth));
            if (currentHealth <= 0)
                OnDeath?.Invoke();
        }

        private void HandleMovement()
        {
            if (Input.GetMouseButtonDown(1))
            {
                Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, 100))
                {
                    agent.SetDestination(hit.point);
                }
            }
        }

        private void RotateTowardsClosestEnemy()
        {
            enemiesInRange = Physics.OverlapSphere(transform.position, attackRange, enemyLayer);
            if (enemiesInRange.Length > 0)
            {
                Collider closestEnemy = enemiesInRange.OrderBy(e => (e.transform.position - transform.position).sqrMagnitude).FirstOrDefault();
                if (closestEnemy != null)
                {
                    Vector3 directionToEnemy = (closestEnemy.transform.position - transform.position).normalized;
                    directionToEnemy.y = 0; // Ensure rotation is only on the horizontal plane

                    if (directionToEnemy != Vector3.zero)
                    {
                        Quaternion lookRotation = Quaternion.LookRotation(directionToEnemy);
                        // Rotate instantly or smoothly over time. Here's the smooth version:
                        transform.rotation = Quaternion.Slerp(transform.rotation, lookRotation, Time.deltaTime * 20f);
                    }
                }
            }
        }

        private void HandleAttacking()
        {
            // Automatically attack the closest enemy if the cooldown has passed
            if (Time.time >= lastAttackTime)
            {
                Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, attackRange, enemyLayer);
                if (enemiesInRange.Length > 0)
                {
                    Collider closestEnemy = enemiesInRange.OrderBy(e => (e.transform.position - transform.position).sqrMagnitude).FirstOrDefault();
                    if (closestEnemy != null)
                    {
                        // Automatically fire a projectile at the closest enemy
                        FireProjectile();
                        lastAttackTime = Time.time + attackCooldown;
                    }
                }
            }
        }

        private void FireProjectile()
        {
            if (projectilePrefab && firePoint)
            {
                // Instantiate the projectile at the fire point position and rotation
                GameObject projectileObject = Instantiate(projectilePrefab, firePoint.position, firePoint.rotation);
                Bullet projectileScript = projectileObject.GetComponent<Bullet>();
                if (projectileScript != null)
                {
                    // Initialize the projectile's damage and speed
                    projectileScript.Initialize(damage, 15f, knockback, penetration); // Example stats, adjust as needed
                }
                else
                {
                    Debug.LogWarning("Projectile prefab does not have a Bullet component.");
                }
            }
        }

        public void ApplyPowerUp(PowerUp.PowerUpOption powerUpType, float value)
        {
            switch (powerUpType)
            {
                case PowerUp.PowerUpOption.Health:
                    float healthIncrease = (maxhealth * value) / 100f;
                    maxhealth += healthIncrease;
                    currentHealth = maxhealth;
                    OnhealthChange?.Invoke(Mathf.RoundToInt(currentHealth));
                    break;

                case PowerUp.PowerUpOption.Damage:
                    float damageIncrease = (damage * value) / 100f;
                    damage += damageIncrease;
                    break;

                case PowerUp.PowerUpOption.MovementSpeed:
                    float percentageIncrease = value / 100f;
                    movementSpeed *= (1 + percentageIncrease);
                    agent.speed = movementSpeed;
                    break;

                case PowerUp.PowerUpOption.AttackSpeed:

                    float attackSpeedIncrease = value / 100f;
                    attackCooldown *= (1 - attackSpeedIncrease);
                    break;

                case PowerUp.PowerUpOption.Heal:
                    currentHealth = maxhealth;
                    OnhealthChange?.Invoke(Mathf.RoundToInt(currentHealth));
                    break;

                case PowerUp.PowerUpOption.AttackRange:
                    float attackRangeIncrease = (attackRange * value) / 100f;
                    attackRange += attackRangeIncrease;
                    break;
                case PowerUp.PowerUpOption.ExpGain:
                    float expMod = (expModifier * value) / 100f;
                    expModifier += expMod;
                    break;
                case PowerUp.PowerUpOption.Penetration:
                    penetration += Mathf.FloorToInt(value);
                    break;
                case PowerUp.PowerUpOption.Knockback:
                    float knockbackMod = (knockback * value) / 100f;
                    knockback += knockbackMod;
                    break;

                default:
                    Debug.LogWarning("Unknown power-up type: " + powerUpType);
                    break;
            }
        }

        private void GamePaused(bool isPaused)
        {
            if (isPaused)
                agent.isStopped = true;
            else
                agent.isStopped = false;
        }

        private void OnDrawGizmosSelected()
        {
            // Visualize attack range in editor
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
        }
    }
}