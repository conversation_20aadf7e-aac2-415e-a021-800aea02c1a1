using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    [CreateAssetMenu(fileName = "New PowerUp", menuName = "RealSoftGames/Advanced Leveling System/Demo/New PowerUp", order = 1)]
    public class PowerUp : ScriptableObject
    {
        public enum PowerUpOption
        {
            Health,
            Damage,
            MovementSpeed,
            AttackSpeed,
            Heal,
            AttackRange,
            ExpGain,
            Knockback,
            Penetration
        }

        [SerializeField] private PowerUpOption powerUpOption;

        [SerializeField, TextArea(3, 10)]
        private string description; // A description for the power-up

        [SerializeField] private float value;

        public PowerUpOption _PowerUpOption { get => powerUpOption; }
        public string Description { get => description; }
        public float Value { get => value; }
    }
}