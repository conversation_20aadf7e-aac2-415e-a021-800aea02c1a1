using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class SmoothCameraFollow : MonoBehaviour
    {
        public Transform target; // Assign the target object to follow in the inspector.
        public float smoothTime = 0.3f; // Adjust the smoothing time for camera movement.
        public Vector3 offset = new Vector3(0, 5, -10); // Define an offset relative to the target.

        private Vector3 velocity = Vector3.zero; // Internally used by SmoothDamp for smooth following.

        private void LateUpdate()
        {
            if (target == null) return;

            // Calculate the target position by applying the offset
            Vector3 targetPosition = target.position + offset;

            // Smoothly move the camera towards the target position
            transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
        }
    }
}