using System;
using System.Collections;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;

namespace RealSoftGames.AdvancedLevelingSystem.Demo
{
    public class Spawner : MonoBehaviour
    {
        public GameObject enemyPrefab;
        public int numberOfEnemies = 10;
        public float spawnRadius = 10f;
        private int enemiesAlive = 0;
        private int waveNumber = 1;

        public int WaveNumber { get => waveNumber; }

        public static Action<int> OnNextWave;
        public Action OnEnemyKilled;
        public static Action<int> enemiesRemaining;

        private void Start()
        {
            StartCoroutine(SpawnWave());
        }

        private IEnumerator SpawnWave()
        {
            while (true) // Infinite loop to keep spawning waves
            {
                yield return StartCoroutine(SpawnEnemies(numberOfEnemies));
                OnNextWave?.Invoke(waveNumber);
                while (enemiesAlive > 0) // Wait for all enemies to be killed
                    yield return null;

                yield return new WaitForSeconds(5f); // Wait 5 seconds after all enemies are killed before starting the next wave
                numberOfEnemies += 5; // Increase the number of enemies for the next wave
                waveNumber++;
            }
        }

        private IEnumerator SpawnEnemies(int enemiesToSpawn)
        {
            for (int i = 0; i < enemiesToSpawn; i++)
            {
                SpawnEnemy();
                yield return new WaitForSeconds(1f); // Adjust the delay between spawns if needed
            }
        }

        private void SpawnEnemy()
        {
            Vector3 randomPoint = RandomNavMeshPoint(transform.position, spawnRadius, -1);

            // Ensure the point is on the NavMesh and not too far from the ground
            if (NavMesh.SamplePosition(randomPoint, out NavMeshHit navHit, spawnRadius, NavMesh.AllAreas))
            {
                GameObject newEnemy = Instantiate(enemyPrefab, navHit.position, Quaternion.identity);
                enemiesAlive++;
                enemiesRemaining?.Invoke(enemiesAlive);
                if (newEnemy.TryGetComponent<AI>(out var ai))
                    ai.OnDeath += EnemyDied;
                else if (newEnemy.TryGetComponent<ZombieAI>(out var zombie))
                    zombie.OnDeath += EnemyDied;
            }
            else
            {
                Debug.LogWarning($"Failed to spawn enemy at {randomPoint}. No valid NavMesh position found.");
            }
        }

        private void EnemyDied()
        {
            OnEnemyKilled?.Invoke();
            enemiesAlive--;
            enemiesRemaining?.Invoke(enemiesAlive);
        }

        private Vector3 RandomNavMeshPoint(Vector3 origin, float distance, int areaMask)
        {
            Vector3 randomDirection = Random.insideUnitSphere * distance;
            randomDirection += origin;

            NavMeshHit navHit;
            if (NavMesh.SamplePosition(randomDirection, out navHit, distance, areaMask))
            {
                return navHit.position;
            }

            // If sampling fails, return the origin as a fallback
            Debug.LogWarning("NavMesh sampling failed, returning origin point.");
            return origin;
        }
    }
}
