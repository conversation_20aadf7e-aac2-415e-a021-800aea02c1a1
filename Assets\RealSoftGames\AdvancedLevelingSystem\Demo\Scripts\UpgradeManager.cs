using System.Collections.Generic;
using RealSoftGames.AdvancedLevelingSystem.Demo;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class UpgradeManager : MonoBehaviour
    {
        public static UpgradeManager Instance;

        [SerializeField] private GameObject upgradeOptionsCanvas;
        [SerializeField] private List<PowerUp> powerUps;

        public List<PowerUp> PowerUps { get => powerUps; }

        private void Awake()
        {
            if (Instance == null || Instance != this)
                Instance = this;
        }

        private void Start()
        {
            AdvancedLevelingSystem.OnLevelUp += EnableUpgradeOperations;
            Spawner.OnNextWave += EnableUpgradeOperations;
        }

        private void OnDisable()
        {
            AdvancedLevelingSystem.OnLevelUp -= EnableUpgradeOperations;
            Spawner.OnNextWave -= EnableUpgradeOperations;
        }

        public void DissableUpgradeoptionsCanvas()
        {
            GameManager.PauseGame?.Invoke(false);
            upgradeOptionsCanvas.SetActive(false);
        }

        public void EnableUpgradeOperations(int level)
        {
            GameManager.PauseGame?.Invoke(true);
            upgradeOptionsCanvas.SetActive(true);
        }
    }
}