using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class UpgradeOption : MonoBehaviour
    {
        [SerializeField] private TMP_Text description;
        [SerializeField] private Button onSelectBtn;

        private PowerUp selectedPowerUp;

        private void OnEnable()
        {
            onSelectBtn.onClick.RemoveAllListeners();
            SelectRandomPowerUp();
            onSelectBtn.onClick.AddListener(OnSelect);
        }

        private void SelectRandomPowerUp()
        {
            if (UpgradeManager.Instance.PowerUps.Count == 0)
            {
                Debug.LogWarning("No power-ups are available to select.");
                return;
            }

            // Select a random power-up from the list
            int randomIndex = Random.Range(0, UpgradeManager.Instance.PowerUps.Count);
            selectedPowerUp = UpgradeManager.Instance.PowerUps[randomIndex];

            // Assign the selected power-up's description to the UI text component
            if (description != null && selectedPowerUp != null)
                description.text = selectedPowerUp.Description;
            else
                Debug.LogError("Description Text or Selected PowerUp is null.");
        }

        public void OnSelect()
        {
            Debug.Log($"Apply power Up: {selectedPowerUp._PowerUpOption}");
            Player.Instance.OnPowerUp(selectedPowerUp._PowerUpOption, selectedPowerUp.Value);
            UpgradeManager.Instance.DissableUpgradeoptionsCanvas();
        }
    }
}