using RealSoftGames.AdvancedLevelingSystem.Demo;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class ZombieAI : MonoBehaviour
    {
        public NavMeshAgent agent;
        public Transform target; // Player or target position
        public float health = 100f;
        public float attackRange = 1f;
        public Material zombieMaterial;
        private Color attackColor = Color.yellow; // Color during attack
        private Color defaultColor = Color.red; // Default color
        private Color deathColor = Color.gray; // Color on death
        public float damage = 5f;

        private Vector3 initialScale; // Store the initial scale
        private float lastAttackTime = 0f; // Track the last attack time
        public float attackCooldown = 2f; // Attack cooldown duration in seconds
        [SerializeField, Range(25, 100)] private int initializeExperience = 25;
        [SerializeField] private int experience = 0;
        public Action OnDeath;
        [SerializeField] private HealthText healthText;
        public Action<int> OnHealthChange;

        private void Start()
        {
            OnHealthChange += healthText.OnHealthChange;
            OnHealthChange?.Invoke((int)Math.Ceiling(health));
            experience = initializeExperience;//Random.Range(25, initializeExperience);
            agent = GetComponent<NavMeshAgent>();
            target = Player.Instance.transform;
            initialScale = transform.localScale; // Save the initial scale at start
            agent.speed = Random.Range(0.8f, 2f);

            GameManager.PauseGame += GamePaused;
            if (GameManager.IsPaused)
                agent.isStopped = true;
        }

        private void OnDisable()
        {
            GameManager.PauseGame -= GamePaused;
            OnHealthChange -= healthText.OnHealthChange;
        }

        private void Update()
        {
            if (agent == null || target == null || GameManager.GameOver || GameManager.IsPaused)
                return;

            float distance = Vector3.Distance(target.position, transform.position);
            if (distance <= attackRange && Time.time > lastAttackTime + attackCooldown)
            {
                // Attack if cooldown has passed
                lastAttackTime = Time.time; // Update the last attack time
                StartCoroutine(AttackEffect());
            }
            else
            {
                agent.SetDestination(target.position);
                // Reset to default color when not attacking
                zombieMaterial.color = defaultColor;
            }
        }

        public void TakeDamage(float amount)
        {
            health = Mathf.Max(health - amount, 0);
            OnHealthChange?.Invoke((int)Math.Ceiling(health));
            if (health <= 0)
                StartCoroutine(DeathEffect());
        }

        private IEnumerator AttackEffect()
        {
            // Change color to indicate attack
            zombieMaterial.color = attackColor;

            // Squish and bounce effect based on initial scale
            Vector3 squishScale = new Vector3(initialScale.x, initialScale.y * 0.75f, initialScale.z); // Squish by reducing Y scale to 75%
            Vector3 bounceBackScale = new Vector3(initialScale.x, initialScale.y * 1.2f, initialScale.z); // Bounce back by increasing Y scale to 120%

            transform.localScale = squishScale; // Squish
            yield return new WaitForSeconds(0.1f);
            transform.localScale = bounceBackScale; // Bounce back
            yield return new WaitForSeconds(0.1f);
            transform.localScale = initialScale; // Return to normal

            Player.Instance.TakeDamage(damage); // Damage the player
        }

        private IEnumerator DeathEffect()
        {
            OnDeath?.Invoke();
            agent.isStopped = true;
            GetComponent<Collider>().enabled = false;
            // Optional: Change color to indicate death
            zombieMaterial.color = deathColor;
            transform.AddExperience(Mathf.FloorToInt(experience * Player.EXPModifier));
            // Smoothly squish the zombie towards a flattened scale
            Vector3 squishedScale = new Vector3(initialScale.x, initialScale.y * 0.1f, initialScale.z); // Flatten to 10% of original Y scale

            for (float t = 0; t < 1; t += Time.deltaTime * 2) // Adjust the multiplier for speed
            {
                transform.localScale = Vector3.Lerp(initialScale, squishedScale, t);
                yield return null;
            }

            Destroy(gameObject); // Destroy the zombie
        }

        private void GamePaused(bool isPaused)
        {
            if (isPaused)
                agent.isStopped = true;
            else
                agent.isStopped = false;
        }
    }
}