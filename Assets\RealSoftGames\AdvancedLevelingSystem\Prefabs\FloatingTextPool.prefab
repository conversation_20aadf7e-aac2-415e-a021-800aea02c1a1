%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3438049824171090919
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2958260540896495749}
  - component: {fileID: 5285682910967137933}
  m_Layer: 0
  m_Name: FloatingTextPool
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2958260540896495749
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3438049824171090919}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5285682910967137933
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3438049824171090919}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c45f5812eaac3ec429fb2ecc25a5d3c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  expTextPrefab: {fileID: 3308812638332496581, guid: c66e0513d0ea3094e93be71d3bcc56ab, type: 3}
  DefaultExpTextOffset: {x: 0, y: 1, z: 0}
  prefix: 
  suffix: ' EXP'
