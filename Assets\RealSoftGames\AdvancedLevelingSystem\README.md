# Advanced Leveling System

## Overview
The Advanced Leveling System provides a flexible and powerful leveling system for Unity games. It supports multiple leveling algorithms, difficulty ratings, and experience reward calculations.

## Architecture

### Core Components
- **AdvancedLevelingSystem**: Main component that handles player leveling and experience.
- **LevelingAlgorithmBase**: Unified scriptable object that defines how experience requirements scale with level. Supports both drawn patterns and coded formulas.
- **DifficultyRatings**: Scriptable objects that define difficulty levels with associated colors and descriptions.
- **ExperienceRewards**: System for calculating experience rewards based on configurable categories.

### Algorithm Types
- **Drawn Pattern**: Create algorithms by drawing points on a graph. Points are interpolated to create a smooth curve.
- **Coded Formula**: Create algorithms using mathematical formulas with various modulation options (sine wave, heartbeat, zigzag, random).

### Database Managers
- **DifficultyRatingDatabaseManager**: Manages the creation and initialization of difficulty rating databases.
- **AlgorithmDatabaseManager**: Manages the creation, initialization, and conversion of algorithm databases.

### Editor Windows
- **AdvancedLevelingSystemEditorWindow**: Unified editor window with tabs for all functionality.
  - **DifficultyTabManager**: Manages difficulty rating settings.
  - **ExperienceRewardsTabManager**: Manages experience reward calculations.
  - **AlgorithmDesignerTabManager**: Allows creation and editing of leveling algorithms.
  - **SimulationTabManager**: Simulates player progression over time.
  - **SettingsTabManager**: Manages global settings.
  - **AboutTabManager**: Displays information about the asset.

## Refactoring Notes

### Unified Algorithm System
The algorithm system has been completely refactored to provide a unified approach:

1. **LevelingAlgorithmBase**
   - A single unified class that supports both drawn patterns and coded formulas.
   - Replaces the separate `DrawnPatternAlgorithm` and `CodedFormulaAlgorithm` classes.
   - Provides a consistent interface for all algorithm types.

2. **AlgorithmFactory**
   - Creates algorithms of both types using a unified interface.
   - Provides methods for creating common algorithm patterns (linear, exponential, sinusoidal, etc.).
   - Handles saving algorithms to disk and adding them to the database.

3. **LevelingAlgorithmEditor**
   - A custom editor for the unified algorithm class.
   - Provides a consistent interface for editing both algorithm types.
   - Allows switching between drawn pattern and coded formula modes.

### Consolidated Scripts
The following scripts have been consolidated to reduce redundancy:

1. **DifficultyRatingDatabaseManager.cs**
   - Replaces: CreateDifficultyRatingDatabase.cs, CreateDifficultyRatingDatabaseIfNeeded.cs, CreateDefaultDifficultyRatings.cs
   - Handles all difficulty rating database creation and initialization.

2. **AlgorithmDatabaseManager.cs**
   - Replaces: CreateAlgorithmDatabase.cs, CreateAlgorithmDatabaseIfNeeded.cs, ConvertAlgorithmsToScriptableObjects.cs
   - Handles all algorithm database creation, initialization, and conversion.

### Removed Scripts
The following scripts have been removed as part of the refactoring process:

1. **DrawnPatternAlgorithm.cs** and **CodedFormulaAlgorithm.cs**
   - Replaced by the unified LevelingAlgorithmBase class.

2. **LevelingDifficultyEditorWindow.cs**
   - Replaced by: DifficultyTabManager in the unified editor window.

3. **ExperienceRewardsEditorWindow.cs**
   - Replaced by: ExperienceRewardsTabManager in the unified editor window.

4. **CreateDifficultyRatingDatabase.cs**
   - Replaced by: DifficultyRatingDatabaseManager.cs

5. **CreateAlgorithmDatabase.cs**
   - Replaced by: AlgorithmDatabaseManager.cs

6. **ConvertAlgorithmsToScriptableObjects.cs**
   - Replaced by: AlgorithmDatabaseManager.cs

7. **UnifiedAlgorithmEditor.cs**
   - Replaced by: LevelingAlgorithmEditor.cs

## Best Practices

1. **Use ScriptableObjects for Data**
   - Store all game data in ScriptableObjects rather than MonoBehaviours.
   - Use the Resources folder for data that needs to be loaded at runtime.
   - Use the AlgorithmFactory to create and save algorithms.

2. **Unified Algorithm System**
   - Use the LevelingAlgorithmBase class for all algorithms.
   - Choose the appropriate algorithm type (DrawnPattern or CodedFormula) based on your needs.
   - Use the AlgorithmFactory to create common algorithm patterns.

3. **Naming Conventions**
   - Use clear, descriptive names for classes and methods.
   - Prefer `AlgorithmName` over `Name` for clarity.
   - Use consistent naming for related properties and methods.

4. **Code Organization**
   - Use regions to organize code.
   - Group related functionality together.
   - Keep methods small and focused.
   - Use inheritance and interfaces appropriately.

5. **UI Design**
   - Use consistent padding and spacing.
   - Group related controls with headers.
   - Use appropriate colors for different difficulty levels.
   - Provide clear visual feedback for user actions.

## Future Development

1. **Further Consolidation**
   - Consider consolidating tab managers into a single class with different modes.
   - Remove any remaining references to legacy classes.

2. **Performance Improvements**
   - Optimize graph rendering for large datasets.
   - Cache calculated values where appropriate.
   - Improve algorithm selection performance.

3. **New Features**
   - Add support for custom experience reward formulas.
   - Implement more visualization options for leveling curves.
   - Add export/import functionality for sharing configurations.
   - Add more algorithm templates for users to customize.
   - Enhance the algorithm designer with more editing tools.

4. **Documentation**
   - Create comprehensive documentation for the unified algorithm system.
   - Add more examples of how to create custom algorithms.
   - Create video tutorials for using the algorithm designer.
