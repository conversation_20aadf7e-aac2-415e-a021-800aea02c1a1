%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d8fd92f540ce3be48a955ee8285d549e, type: 3}
  m_Name: Adaptive
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 128877b2-2c3a-41f6-aeeb-fb5683a43caf
  algorithmName: Adaptive
  description: Adjusts difficulty based on current level vs max level, creating a
    custom curve for each max level setting.
  formulaExplanation: 'Formula: Varies based on progress through total levels


    Adjusts
    difficulty based on current level vs max level, creating a custom curve for each
    max level setting.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 254}
  - {x: 6, y: 255}
  - {x: 7, y: 256}
  - {x: 8, y: 257}
  - {x: 9, y: 258}
  - {x: 10, y: 259}
  - {x: 11, y: 313}
  - {x: 12, y: 379}
  - {x: 13, y: 459}
  - {x: 14, y: 555}
  - {x: 15, y: 672}
  - {x: 16, y: 813}
  - {x: 17, y: 984}
  - {x: 18, y: 1191}
  - {x: 19, y: 1441}
  - {x: 20, y: 1744}
  - {x: 21, y: 2110}
  - {x: 22, y: 2553}
  - {x: 23, y: 2808}
  - {x: 24, y: 3089}
  - {x: 25, y: 3398}
  - {x: 26, y: 3738}
  - {x: 27, y: 4112}
  - {x: 28, y: 4523}
  - {x: 29, y: 4975}
  - {x: 30, y: 5472}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.99}
  - {x: 2, y: 0.99}
  - {x: 3, y: 0.99}
  - {x: 4, y: 0.99}
  - {x: 5, y: 0.99}
  - {x: 6, y: 0.99}
  - {x: 7, y: 0.99}
  - {x: 8, y: 0.99}
  - {x: 9, y: 0.99}
  - {x: 10, y: 1.21}
  - {x: 11, y: 1.21}
  - {x: 12, y: 1.21}
  - {x: 13, y: 1.21}
  - {x: 14, y: 1.21}
  - {x: 15, y: 1.21}
  - {x: 16, y: 1.21}
  - {x: 17, y: 1.21}
  - {x: 18, y: 1.21}
  - {x: 19, y: 1.21}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.21}
  - {x: 22, y: 1.1}
  - {x: 23, y: 1.1}
  - {x: 24, y: 1.1}
  - {x: 25, y: 1.1}
  - {x: 26, y: 1.1}
  - {x: 27, y: 1.1}
  - {x: 28, y: 1.1}
  - {x: 29, y: 1.1}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: fb000000fc000000fd000000fe000000ff000000000100000101000002010000030100001d0100003a010000590100007c010000a2010000cc010000fa0100002d02000065020000a2020000e50200002f0300003003000031030000320300003303000034030000350300003603000037030000
  cachedRawFormulaCurve: []
  earlyGameMultiplier: 1.05
  midGameMultiplier: 1.15
  lateGameMultiplier: 1.1
  earlyGameScalingFactor: 0.9
  midGameScalingFactor: 1.1
  lateGameScalingFactor: 1
