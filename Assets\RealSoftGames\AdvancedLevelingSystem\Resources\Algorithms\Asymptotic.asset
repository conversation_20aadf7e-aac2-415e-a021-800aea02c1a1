%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f039d019e8bfb344d9b851e4d1378ab2, type: 3}
  m_Name: Asymptotic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 83c08882-1eae-4849-8c5c-6b86286b451d
  algorithmName: Asymptotic
  description: Approaches a limit as levels increase, creating a ceiling on difficulty
    that prevents excessive grinding.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (maxMultiplier * (1 -
    e^(-rate*level)))


    Approaches a limit as levels increase, creating a ceiling
    on difficulty that prevents excessive grinding.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 255}
  - {x: 3, y: 264}
  - {x: 4, y: 278}
  - {x: 5, y: 296}
  - {x: 6, y: 319}
  - {x: 7, y: 348}
  - {x: 8, y: 383}
  - {x: 9, y: 425}
  - {x: 10, y: 475}
  - {x: 11, y: 535}
  - {x: 12, y: 606}
  - {x: 13, y: 691}
  - {x: 14, y: 792}
  - {x: 15, y: 911}
  - {x: 16, y: 1053}
  - {x: 17, y: 1221}
  - {x: 18, y: 1421}
  - {x: 19, y: 1658}
  - {x: 20, y: 1940}
  - {x: 21, y: 2275}
  - {x: 22, y: 2674}
  - {x: 23, y: 3150}
  - {x: 24, y: 3717}
  - {x: 25, y: 4393}
  - {x: 26, y: 5199}
  - {x: 27, y: 6162}
  - {x: 28, y: 7312}
  - {x: 29, y: 8685}
  - {x: 30, y: 10326}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0190325}
  - {x: 2, y: 1.0362538}
  - {x: 3, y: 1.0518364}
  - {x: 4, y: 1.065936}
  - {x: 5, y: 1.0786939}
  - {x: 6, y: 1.0902377}
  - {x: 7, y: 1.100683}
  - {x: 8, y: 1.1101342}
  - {x: 9, y: 1.1186861}
  - {x: 10, y: 1.1264242}
  - {x: 11, y: 1.1334258}
  - {x: 12, y: 1.1397612}
  - {x: 13, y: 1.1454936}
  - {x: 14, y: 1.1506807}
  - {x: 15, y: 1.155374}
  - {x: 16, y: 1.1596208}
  - {x: 17, y: 1.1634634}
  - {x: 18, y: 1.1669402}
  - {x: 19, y: 1.1700863}
  - {x: 20, y: 1.172933}
  - {x: 21, y: 1.1755087}
  - {x: 22, y: 1.1778394}
  - {x: 23, y: 1.1799483}
  - {x: 24, y: 1.1818565}
  - {x: 25, y: 1.183583}
  - {x: 26, y: 1.1851454}
  - {x: 27, y: 1.186559}
  - {x: 28, y: 1.1878381}
  - {x: 29, y: 1.1889954}
  - {x: 30, y: 1.1900426}
  cachedRequirementCurve: f5010000f6010000f7010000f8010000f9010000fa010000fb010000fc010000fd010000fe010000ff010000000200000102000002020000030200000402000005020000060200000702000008020000090200000a0200000b0200000c0200000d0200000e0200000f020000100200001102000012020000130200001402000015020000160200001702000018020000190200001a0200001b0200001c0200001d0200001e0200001f020000200200002102000022020000230200002402000025020000
  cachedRawFormulaCurve: []
  zeroMaxMultiplier: 1.5
  multiplierScale: 2
  approachRate: 0.1
