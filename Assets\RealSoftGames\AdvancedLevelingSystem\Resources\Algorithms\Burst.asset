%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3736b9263986868468eee42eed0f1b87, type: 3}
  m_Name: Burst
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 92304c4d-b5af-43e1-aab2-d5c45a4cd005
  algorithmName: Burst
  description: Creates sudden bursts of high difficulty followed by periods of easier
    progression, like a series of explosions with calm periods in between.
  formulaExplanation: 'Formula: Uses exponential bursts at regular intervals


    Creates
    a pattern with sudden spikes of high difficulty followed by periods of more gradual
    progression, with bursts becoming more intense as levels increase.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 413}
  - {x: 3, y: 697}
  - {x: 4, y: 704}
  - {x: 5, y: 711}
  - {x: 6, y: 718}
  - {x: 7, y: 725}
  - {x: 8, y: 732}
  - {x: 9, y: 1402}
  - {x: 10, y: 2739}
  - {x: 11, y: 2766}
  - {x: 12, y: 2794}
  - {x: 13, y: 2822}
  - {x: 14, y: 2850}
  - {x: 15, y: 2878}
  - {x: 16, y: 6277}
  - {x: 17, y: 13928}
  - {x: 18, y: 14067}
  - {x: 19, y: 14208}
  - {x: 20, y: 14350}
  - {x: 21, y: 14494}
  - {x: 22, y: 14639}
  - {x: 23, y: 35815}
  - {x: 24, y: 88982}
  - {x: 25, y: 89872}
  - {x: 26, y: 90771}
  - {x: 27, y: 91679}
  - {x: 28, y: 92596}
  - {x: 29, y: 93522}
  - {x: 30, y: 253638}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.6500001}
  - {x: 2, y: 1.6879311}
  - {x: 3, y: 1.01}
  - {x: 4, y: 1.01}
  - {x: 5, y: 1.01}
  - {x: 6, y: 1.01}
  - {x: 7, y: 1.01}
  - {x: 8, y: 1.9155172}
  - {x: 9, y: 1.9534483}
  - {x: 10, y: 1.01}
  - {x: 11, y: 1.01}
  - {x: 12, y: 1.01}
  - {x: 13, y: 1.01}
  - {x: 14, y: 1.01}
  - {x: 15, y: 2.1810346}
  - {x: 16, y: 2.2189658}
  - {x: 17, y: 1.01}
  - {x: 18, y: 1.01}
  - {x: 19, y: 1.01}
  - {x: 20, y: 1.01}
  - {x: 21, y: 1.01}
  - {x: 22, y: 2.4465516}
  - {x: 23, y: 2.4844828}
  - {x: 24, y: 1.01}
  - {x: 25, y: 1.01}
  - {x: 26, y: 1.01}
  - {x: 27, y: 1.01}
  - {x: 28, y: 1.01}
  - {x: 29, y: 2.712069}
  - {x: 30, y: 2.75}
  cachedRequirementCurve: ee020000740400007f0400008b04000097040000a3040000af040000b2070000cd0c0000ee0c00000f0d0000300d0000520d0000740d000006180000642b0000d32b0000432c0000b42c0000262d00009a2d0000f257000067ab00001ead0000d9ae000099b000005db2000026b400002a750100990c030067140300491c03003f2403004a2c0300693403009f18070098db0f0030041000302d1000995610006c801000aaaa1000234927005b675d0078565e00f9475f00e43b600040326100122b6200
  cachedRawFormulaCurve: []
  burstCycle: 7
  burstDuration: 2
  baseBurstIntensity: 1.5
  maxBurstIntensity: 2.5
  zeroBetweenBurstsMultiplier: 1.03
  zeroBurstMultiplier: 1.15
  zeroLevelProgressionFactor: 0.05
  betweenBurstsScale: 0.8
