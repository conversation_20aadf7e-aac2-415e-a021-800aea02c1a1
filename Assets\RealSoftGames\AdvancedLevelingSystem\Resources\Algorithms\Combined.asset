%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Combined
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 2e230e42-64e2-4f5c-bc7c-f2c6ff8a1a70
  algorithmName: Combined
  description: A combined progression with multiple patterns.
  formulaExplanation: "Experience = Previous * (1.1 + 0.01 * Level + 0.1 * sin(Level
    * \u03C0/5) + 0.05 * heartbeat(Level))"
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 1
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 1
  heartbeatAmplitude: 0.05
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 323}
  - {x: 3, y: 439}
  - {x: 4, y: 606}
  - {x: 5, y: 824}
  - {x: 6, y: 1079}
  - {x: 7, y: 1352}
  - {x: 8, y: 1644}
  - {x: 9, y: 1997}
  - {x: 10, y: 2497}
  - {x: 11, y: 3269}
  - {x: 12, y: 4582}
  - {x: 13, y: 6726}
  - {x: 14, y: 10029}
  - {x: 15, y: 14742}
  - {x: 16, y: 20919}
  - {x: 17, y: 28505}
  - {x: 18, y: 37794}
  - {x: 19, y: 50066}
  - {x: 20, y: 68107}
  - {x: 21, y: 96644}
  - {x: 22, y: 146092}
  - {x: 23, y: 230525}
  - {x: 24, y: 369096}
  - {x: 25, y: 583159}
  - {x: 26, y: 891650}
  - {x: 27, y: 1313090}
  - {x: 28, y: 1885439}
  - {x: 29, y: 2705062}
  - {x: 30, y: 3977360}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.2916523}
  - {x: 2, y: 1.3579445}
  - {x: 3, y: 1.3811121}
  - {x: 4, y: 1.3599645}
  - {x: 5, y: 1.309}
  - {x: 6, y: 1.2526517}
  - {x: 7, y: 1.2158797}
  - {x: 8, y: 1.2147121}
  - {x: 9, y: 1.2503396}
  - {x: 10, y: 1.3090001}
  - {x: 11, y: 1.4016523}
  - {x: 12, y: 1.4679445}
  - {x: 13, y: 1.4911121}
  - {x: 14, y: 1.4699645}
  - {x: 15, y: 1.419}
  - {x: 16, y: 1.3626517}
  - {x: 17, y: 1.3258797}
  - {x: 18, y: 1.324712}
  - {x: 19, y: 1.3603395}
  - {x: 20, y: 1.419}
  - {x: 21, y: 1.5116524}
  - {x: 22, y: 1.5779445}
  - {x: 23, y: 1.6011122}
  - {x: 24, y: 1.5799645}
  - {x: 25, y: 1.5289999}
  - {x: 26, y: 1.4726516}
  - {x: 27, y: 1.4358797}
  - {x: 28, y: 1.434712}
  - {x: 29, y: 1.4703395}
  - {x: 30, y: 1.529}
  cachedRequirementCurve: 260100006b010000c8010000340200009f020000fc0200004c030000a403000023040000ec040000460600005f080000590b00002a0f0000901300003c180000361d00002e230000812b00001f380000204d0000a36e00000aa100004ee70000834101006eae0100dc310200d3dc02008cd30300
  cachedRawFormulaCurve: []
