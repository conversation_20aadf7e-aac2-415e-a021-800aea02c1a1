%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: ComplexWave
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: aaba073f-0de4-47d7-b53b-79e935f5894f
  algorithmName: Complex Wave
  description: A complex wave pattern combining sine wave, heartbeat, and random
    fluctuations.
  formulaExplanation: "Experience = Previous * (1.1 + 0.01 * Level + 0.16 * sin(Level
    * \u03C0/15.3) + 0.69 * heartbeat(Level, 8) + 0.89 * random(seed: 12289))"
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 1
  sineAmplitude: 0.16
  sinePeriod: 15.3
  useHeartbeat: 1
  heartbeatAmplitude: 0.69
  heartbeatPeriod: 8
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 1
  randomAmplitude: 0.89
  randomSeed: 12289
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 303}
  - {x: 3, y: 765}
  - {x: 4, y: 1374}
  - {x: 5, y: 4006}
  - {x: 6, y: 7832}
  - {x: 7, y: 22242}
  - {x: 8, y: 37323}
  - {x: 9, y: 90419}
  - {x: 10, y: 161191}
  - {x: 11, y: 175938}
  - {x: 12, y: 399379}
  - {x: 13, y: 550362}
  - {x: 14, y: 1278987}
  - {x: 15, y: 1536882}
  - {x: 16, y: 3002641}
  - {x: 17, y: 3002642}
  - {x: 18, y: 5982794}
  - {x: 19, y: 7671136}
  - {x: 20, y: 18810200}
  - {x: 21, y: 29386946}
  - {x: 22, y: 74085336}
  - {x: 23, y: 105336320}
  - {x: 24, y: 232389570}
  - {x: 25, y: 232389570}
  - {x: 26, y: 541778900}
  - {x: 27, y: 905595700}
  - {x: 28, y: 905595700}
  - {x: 29, y: 1.862525e+9}
  - {x: 30, y: 1.862525e+9}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.2100505}
  - {x: 2, y: 2.524574}
  - {x: 3, y: 1.7964387}
  - {x: 4, y: 2.915276}
  - {x: 5, y: 1.9551495}
  - {x: 6, y: 2.8399415}
  - {x: 7, y: 1.6780523}
  - {x: 8, y: 2.4226155}
  - {x: 9, y: 1.7827166}
  - {x: 10, y: 1.0914862}
  - {x: 11, y: 2.269999}
  - {x: 12, y: 1.3780434}
  - {x: 13, y: 2.3239014}
  - {x: 14, y: 1.2016404}
  - {x: 15, y: 1.9537227}
  - {x: 16, y: 0.70313}
  - {x: 17, y: 1.9925101}
  - {x: 18, y: 1.2821996}
  - {x: 19, y: 2.4520748}
  - {x: 20, y: 1.5622878}
  - {x: 21, y: 2.5210288}
  - {x: 22, y: 1.4218241}
  - {x: 23, y: 2.2061675}
  - {x: 24, y: 0.99568474}
  - {x: 25, y: 2.3313391}
  - {x: 26, y: 1.6715227}
  - {x: 27, y: 2.8939908}
  - {x: 28, y: 2.056685}
  - {x: 29, y: 3.0655913}
  - {x: 30, y: 2.0121284}
  cachedRequirementCurve: 26020000ee0400000d08000056150000ec250000e86100005b950000f048010018150200191502001e4c0400316205009e5f0b00a76c0c003211160033111600cef82700ba972e00ccdc67000c83930006135201dcfbb401ec6a6c03ed6a6c03907a4107d08f060b20e1011d80443c3681443c36003535630135356302353563033535630435356305353563063535630735356308353563093535630a3535630b3535630c3535630d3535630e3535630f35356310353563113535631235356313353563
  cachedRawFormulaCurve: []
