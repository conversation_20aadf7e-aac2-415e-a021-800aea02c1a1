%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: ComplexWave
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: aaba073f-0de4-47d7-b53b-79e935f5894f
  algorithmName: Complex Wave
  description: A complex wave pattern combining sine wave, heartbeat, and random
    fluctuations.
  formulaExplanation: "Experience = Previous * (1.1 + 0.01 * Level + 0.16 * sin(Level
    * \u03C0/15.3) + 0.69 * heartbeat(Level, 8) + 0.89 * random(seed: 12289))"
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 1
  sineAmplitude: 0.16
  sinePeriod: 15.3
  useHeartbeat: 1
  heartbeatAmplitude: 0.69
  heartbeatPeriod: 8
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 1
  randomAmplitude: 0.89
  randomSeed: 12289
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
