%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f99754828fac3834c80362aa3a93aa81, type: 3}
  m_Name: Cosine
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: e112adc9-6084-4d31-8920-db1f5be2632d
  algorithmName: Cosine
  description: Creates a smooth wave pattern that starts high, dips in the middle,
    and rises again, creating a balanced progression with alternating easy and challenging
    phases.
  formulaExplanation: 'Formula: Uses cosine function to create a smooth wave pattern


    Creates
    a progression that starts with higher multipliers, decreases in the middle, and
    increases again toward the end, following a cosine curve.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 60
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 280}
  - {x: 3, y: 313}
  - {x: 4, y: 350}
  - {x: 5, y: 391}
  - {x: 6, y: 435}
  - {x: 7, y: 483}
  - {x: 8, y: 534}
  - {x: 9, y: 588}
  - {x: 10, y: 645}
  - {x: 11, y: 705}
  - {x: 12, y: 768}
  - {x: 13, y: 834}
  - {x: 14, y: 903}
  - {x: 15, y: 976}
  - {x: 16, y: 1054}
  - {x: 17, y: 1138}
  - {x: 18, y: 1230}
  - {x: 19, y: 1332}
  - {x: 20, y: 1446}
  - {x: 21, y: 1574}
  - {x: 22, y: 1720}
  - {x: 23, y: 1886}
  - {x: 24, y: 2077}
  - {x: 25, y: 2296}
  - {x: 26, y: 2547}
  - {x: 27, y: 2835}
  - {x: 28, y: 3164}
  - {x: 29, y: 3538}
  - {x: 30, y: 3961}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.12}
  - {x: 2, y: 1.1195325}
  - {x: 3, y: 1.1181515}
  - {x: 4, y: 1.1159219}
  - {x: 5, y: 1.1129477}
  - {x: 6, y: 1.1093682}
  - {x: 7, y: 1.1053506}
  - {x: 8, y: 1.1010828}
  - {x: 9, y: 1.0967643}
  - {x: 10, y: 1.0925972}
  - {x: 11, y: 1.0887762}
  - {x: 12, y: 1.0854801}
  - {x: 13, y: 1.0828629}
  - {x: 14, y: 1.0810469}
  - {x: 15, y: 1.0801172}
  - {x: 16, y: 1.0801172}
  - {x: 17, y: 1.0810469}
  - {x: 18, y: 1.0828629}
  - {x: 19, y: 1.0854801}
  - {x: 20, y: 1.0887762}
  - {x: 21, y: 1.0925972}
  - {x: 22, y: 1.0967643}
  - {x: 23, y: 1.1010828}
  - {x: 24, y: 1.1053506}
  - {x: 25, y: 1.1093682}
  - {x: 26, y: 1.1129477}
  - {x: 27, y: 1.1159219}
  - {x: 28, y: 1.1181515}
  - {x: 29, y: 1.1195325}
  - {x: 30, y: 1.12}
  cachedRequirementCurve: fc000000ff0000000201000005010000080100000b0100000e0100001101000014010000170100001a0100001d010000200100002301000026010000290100002c0100002f0100003201000035010000380100003b0100003e0100004101000044010000470100004a0100004d010000500100005301000056010000590100005c0100005f01000063010000670100006b0100006f01000073010000770100007b0100007f01000083010000870100008b0100008f01000093010000970100009b0100009f010000a3010000a7010000ab010000af010000b3010000b7010000bb010000bf010000c3010000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  zeroAmplitude: 0.04
  amplitudeScale: 0.2
  cycleCount: 1
