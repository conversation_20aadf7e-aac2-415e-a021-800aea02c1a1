%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7bd3353eaf270347af3ddecfcdabbfe, type: 3}
  m_Name: Crescendo
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 81acca34-7936-4ab2-aeb8-a23378415f64
  algorithmName: Crescendo
  description: Creates a musical crescendo effect where difficulty builds gradually
    then dramatically increases toward the end, like the climax of a symphony.
  formulaExplanation: 'Formula: Uses exponential growth with accelerating rate


    Creates
    a musical crescendo effect where difficulty builds gradually at first, then accelerates
    more dramatically toward the end, like the climax of a symphony building to its
    finale.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 252}
  - {x: 3, y: 255}
  - {x: 4, y: 258}
  - {x: 5, y: 261}
  - {x: 6, y: 264}
  - {x: 7, y: 267}
  - {x: 8, y: 270}
  - {x: 9, y: 273}
  - {x: 10, y: 276}
  - {x: 11, y: 279}
  - {x: 12, y: 282}
  - {x: 13, y: 285}
  - {x: 14, y: 288}
  - {x: 15, y: 291}
  - {x: 16, y: 294}
  - {x: 17, y: 297}
  - {x: 18, y: 300}
  - {x: 19, y: 303}
  - {x: 20, y: 309}
  - {x: 21, y: 318}
  - {x: 22, y: 334}
  - {x: 23, y: 362}
  - {x: 24, y: 397}
  - {x: 25, y: 445}
  - {x: 26, y: 519}
  - {x: 27, y: 609}
  - {x: 28, y: 738}
  - {x: 29, y: 933}
  - {x: 30, y: 1183}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.01}
  - {x: 2, y: 1.01}
  - {x: 3, y: 1.01}
  - {x: 4, y: 1.01}
  - {x: 5, y: 1.01}
  - {x: 6, y: 1.01}
  - {x: 7, y: 1.01}
  - {x: 8, y: 1.01}
  - {x: 9, y: 1.01}
  - {x: 10, y: 1.01}
  - {x: 11, y: 1.01}
  - {x: 12, y: 1.01}
  - {x: 13, y: 1.01}
  - {x: 14, y: 1.01}
  - {x: 15, y: 1.01}
  - {x: 16, y: 1.01}
  - {x: 17, y: 1.01}
  - {x: 18, y: 1.01}
  - {x: 19, y: 1.0193976}
  - {x: 20, y: 1.0306581}
  - {x: 21, y: 1.0490125}
  - {x: 22, y: 1.0852501}
  - {x: 23, y: 1.0953853}
  - {x: 24, y: 1.1217734}
  - {x: 25, y: 1.1664456}
  - {x: 26, y: 1.1741139}
  - {x: 27, y: 1.2116092}
  - {x: 28, y: 1.2635564}
  - {x: 29, y: 1.2678809}
  - {x: 30, y: 1.3200002}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000170200001c02000021020000260200002c02000032020000380200003e020000440200004a02000050020000560200005c02000062020000680200006e020000740200007a02000080020000860200008c020000930200009a020000a1020000a8020000af020000b6020000bd020000c4020000cb020000d2020000d9020000e0020000f1020000180300004c03000084030000c70300002b040000be040000770500004a0600004e070000
  cachedRawFormulaCurve: []
  crescendoExponent: 2.5
  noteFrequency: 20
  noteAmplitude: 0.02
  zeroBaseMultiplier: 1.02
  zeroMaxGrowth: 0.2
  baseMultiplierScale: 0.8
  maxGrowthScale: 0.4
