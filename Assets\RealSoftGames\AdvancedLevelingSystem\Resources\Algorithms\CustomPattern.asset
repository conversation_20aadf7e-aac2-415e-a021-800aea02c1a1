%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: CustomPattern
  m_EditorClassIdentifier: 
  algorithmType: 0
  uniqueID: dbcd648e-5fa5-4413-b76e-d4825808a941
  algorithmName: Custom Pattern
  description: Custom pattern created in the Pattern Creator.
  formulaExplanation: Custom drawn pattern
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 0.25, y: 1.15}
  - {x: 0.5, y: 1.2}
  - {x: 0.75, y: 1.25}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 302}
  - {x: 3, y: 368}
  - {x: 4, y: 451}
  - {x: 5, y: 556}
  - {x: 6, y: 690}
  - {x: 7, y: 861}
  - {x: 8, y: 1081}
  - {x: 9, y: 1365}
  - {x: 10, y: 1734}
  - {x: 11, y: 2217}
  - {x: 12, y: 2851}
  - {x: 13, y: 3688}
  - {x: 14, y: 4798}
  - {x: 15, y: 6279}
  - {x: 16, y: 8264}
  - {x: 17, y: 10940}
  - {x: 18, y: 14565}
  - {x: 19, y: 19502}
  - {x: 20, y: 26260}
  - {x: 21, y: 35560}
  - {x: 22, y: 48423}
  - {x: 23, y: 66306}
  - {x: 24, y: 91297}
  - {x: 25, y: 126399}
  - {x: 26, y: 175956}
  - {x: 27, y: 246278}
  - {x: 28, y: 346573}
  - {x: 29, y: 490341}
  - {x: 30, y: 697468}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.21}
  - {x: 2, y: 1.2175862}
  - {x: 3, y: 1.2251725}
  - {x: 4, y: 1.2327586}
  - {x: 5, y: 1.2403449}
  - {x: 6, y: 1.247931}
  - {x: 7, y: 1.2555172}
  - {x: 8, y: 1.2631035}
  - {x: 9, y: 1.2706896}
  - {x: 10, y: 1.2782758}
  - {x: 11, y: 1.2858622}
  - {x: 12, y: 1.2934483}
  - {x: 13, y: 1.3010346}
  - {x: 14, y: 1.3086207}
  - {x: 15, y: 1.316207}
  - {x: 16, y: 1.3237932}
  - {x: 17, y: 1.3313794}
  - {x: 18, y: 1.3389655}
  - {x: 19, y: 1.3465518}
  - {x: 20, y: 1.354138}
  - {x: 21, y: 1.3617241}
  - {x: 22, y: 1.3693104}
  - {x: 23, y: 1.3768966}
  - {x: 24, y: 1.3844829}
  - {x: 25, y: 1.392069}
  - {x: 26, y: 1.3996551}
  - {x: 27, y: 1.4072415}
  - {x: 28, y: 1.4148276}
  - {x: 29, y: 1.4224138}
  - {x: 30, y: 1.43}
  cachedRequirementCurve: 260200005f020000a1020000ed02000044030000a90300001e040000a604000044050000fc050000d4060000d1070000fb0800005b0a0000fc0b0000eb0d000038100000f712000041160000351a0000f81e0000b8240000b02b0000283400007b3e00001b4b0000965a0000a16d00001f85000031a2000045c600002ff30000442b010080710100bac90100e43802005fc5020070770300d3590400827a0500bdeb060068c50800e0260b006c390e0089331200525d17007a161e0058de2600d55e3200
  cachedRawFormulaCurve: []
