%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ace75f1cf492ad4a9d320be9373a1aa, type: 3}
  m_Name: Cyclical
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 1275eeb9-77a7-48ac-84cc-2168c1a45838
  algorithmName: Cyclical
  description: Creates a repeating pattern of difficulty that rises and falls in
    cycles.
  formulaExplanation: 'Formula: Uses multiple sine waves to create a complex cyclical
    pattern


    Creates a repeating pattern where difficulty increases and decreases
    in cycles, providing periods of challenge followed by relief.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 378}
  - {x: 4, y: 520}
  - {x: 5, y: 715}
  - {x: 6, y: 983}
  - {x: 7, y: 1293}
  - {x: 8, y: 1294}
  - {x: 9, y: 1295}
  - {x: 10, y: 1296}
  - {x: 11, y: 1297}
  - {x: 12, y: 1541}
  - {x: 13, y: 2119}
  - {x: 14, y: 2914}
  - {x: 15, y: 3824}
  - {x: 16, y: 3840}
  - {x: 17, y: 3841}
  - {x: 18, y: 3842}
  - {x: 19, y: 3843}
  - {x: 20, y: 3844}
  - {x: 21, y: 3845}
  - {x: 22, y: 5287}
  - {x: 23, y: 7270}
  - {x: 24, y: 9996}
  - {x: 25, y: 13744}
  - {x: 26, y: 18898}
  - {x: 27, y: 20437}
  - {x: 28, y: 20438}
  - {x: 29, y: 20439}
  - {x: 30, y: 20440}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.375}
  - {x: 3, y: 1.375}
  - {x: 4, y: 1.375}
  - {x: 5, y: 1.375}
  - {x: 6, y: 1.3151312}
  - {x: 7, y: 0.9772338}
  - {x: 8, y: 0.82500005}
  - {x: 9, y: 0.82500005}
  - {x: 10, y: 0.9281779}
  - {x: 11, y: 1.1885011}
  - {x: 12, y: 1.375}
  - {x: 13, y: 1.375}
  - {x: 14, y: 1.312263}
  - {x: 15, y: 1.0042385}
  - {x: 16, y: 0.82500005}
  - {x: 17, y: 0.82500005}
  - {x: 18, y: 0.82500005}
  - {x: 19, y: 0.82500005}
  - {x: 20, y: 0.9520339}
  - {x: 21, y: 1.375}
  - {x: 22, y: 1.375}
  - {x: 23, y: 1.375}
  - {x: 24, y: 1.375}
  - {x: 25, y: 1.375}
  - {x: 26, y: 1.0814219}
  - {x: 27, y: 0.82500005}
  - {x: 28, y: 0.82500005}
  - {x: 29, y: 0.8549622}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: fb0000003a01000088010000ea01000064020000dc020000dd020000de020000df020000e00200001b030000e2030000da040000ca050000cb050000cc050000cd050000ce050000cf050000d005000044070000150900005a0b0000300e0000bc110000bd110000be110000bf110000c0110000
  cachedRawFormulaCurve: []
  primaryFrequency: 6
  secondaryFrequency: 3
  secondaryAmplitudeScale: 0.5
  normalizationFactor: 1.5
  amplificationFactor: 1.5
  maxAmplitude: 0.5
  zeroBaseMultiplier: 1.02
  zeroVariationScale: 0.3
  variationScale: 0.5
