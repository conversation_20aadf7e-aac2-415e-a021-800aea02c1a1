%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6f3885ef017039343a29636b3ae7503b, type: 3}
  m_Name: Decelerating
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 9d4168d8-e2f0-48e9-a89d-e18108ff00cf
  algorithmName: Decelerating
  description: Starts fast, then slows down. Creates a steep early curve that gradually
    flattens out.
  formulaExplanation: 'Formula: requiredExp = requiredExp * max(minMultiplier, initialMultiplier
    - decelerationFactor*level)


    Starts fast, then slows down. Creates a steep
    early curve that gradually flattens out.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 326}
  - {x: 3, y: 421}
  - {x: 4, y: 537}
  - {x: 5, y: 677}
  - {x: 6, y: 844}
  - {x: 7, y: 1040}
  - {x: 8, y: 1266}
  - {x: 9, y: 1523}
  - {x: 10, y: 1809}
  - {x: 11, y: 2123}
  - {x: 12, y: 2460}
  - {x: 13, y: 2814}
  - {x: 14, y: 3178}
  - {x: 15, y: 3542}
  - {x: 16, y: 3896}
  - {x: 17, y: 4228}
  - {x: 18, y: 4527}
  - {x: 19, y: 4781}
  - {x: 20, y: 4979}
  - {x: 21, y: 5112}
  - {x: 22, y: 5173}
  - {x: 23, y: 5174}
  - {x: 24, y: 5175}
  - {x: 25, y: 5176}
  - {x: 26, y: 5177}
  - {x: 27, y: 5178}
  - {x: 28, y: 5179}
  - {x: 29, y: 5180}
  - {x: 30, y: 5181}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.3053334}
  - {x: 2, y: 1.2906667}
  - {x: 3, y: 1.276}
  - {x: 4, y: 1.2613333}
  - {x: 5, y: 1.2466667}
  - {x: 6, y: 1.2320001}
  - {x: 7, y: 1.2173334}
  - {x: 8, y: 1.2026668}
  - {x: 9, y: 1.1880001}
  - {x: 10, y: 1.1733334}
  - {x: 11, y: 1.1586667}
  - {x: 12, y: 1.144}
  - {x: 13, y: 1.1293334}
  - {x: 14, y: 1.1146667}
  - {x: 15, y: 1.1}
  - {x: 16, y: 1.0853333}
  - {x: 17, y: 1.0706667}
  - {x: 18, y: 1.0560001}
  - {x: 19, y: 1.0413334}
  - {x: 20, y: 1.0266668}
  - {x: 21, y: 1.0120001}
  - {x: 22, y: 0.9973334}
  - {x: 23, y: 0.98266673}
  - {x: 24, y: 0.96800005}
  - {x: 25, y: 0.9533334}
  - {x: 26, y: 0.9386667}
  - {x: 27, y: 0.9240001}
  - {x: 28, y: 0.9093334}
  - {x: 29, y: 0.89466673}
  - {x: 30, y: 0.88000005}
  cachedRequirementCurve: 54020000c20200003e030000c9030000640400000f050000c905000092060000690700004d0800003b090000310a00002b0b0000270c0000200d0000120e0000f90e0000d00f0000921000003c110000c91100003612000081120000a7120000a8120000a9120000aa120000ab120000ac120000ad120000ae120000af120000b0120000b1120000b2120000b3120000b4120000b5120000b6120000b7120000b8120000b9120000ba120000bb120000bc120000bd120000be120000bf120000c0120000
  cachedRawFormulaCurve: []
  zeroInitialMultiplier: 1.2
  zeroMinMultiplier: 1.02
  zeroDecelerationFactor: 0.005
  initialMultiplierScale: 1.2
  minMultiplierScale: 0.8
