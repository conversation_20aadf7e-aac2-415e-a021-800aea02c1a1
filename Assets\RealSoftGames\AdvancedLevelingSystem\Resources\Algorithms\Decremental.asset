%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f951bbfba3c926c4083e44ab317a1db8, type: 3}
  m_Name: Decremental
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: e1eb27c4-0e6f-4502-b557-638012a7cc3a
  algorithmName: Decremental
  description: Gets easier as you level up, with the multiplier decreasing as you
    progress through the game.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (1 + (levelUpMultiplier-1)
    * (1-(level/maxLevel)*0.2))


    Gets easier as you level up, with the multiplier
    decreasing as you progress through the game.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 302}
  - {x: 4, y: 332}
  - {x: 5, y: 365}
  - {x: 6, y: 400}
  - {x: 7, y: 439}
  - {x: 8, y: 481}
  - {x: 9, y: 527}
  - {x: 10, y: 577}
  - {x: 11, y: 631}
  - {x: 12, y: 690}
  - {x: 13, y: 754}
  - {x: 14, y: 823}
  - {x: 15, y: 898}
  - {x: 16, y: 979}
  - {x: 17, y: 1067}
  - {x: 18, y: 1162}
  - {x: 19, y: 1265}
  - {x: 20, y: 1376}
  - {x: 21, y: 1496}
  - {x: 22, y: 1625}
  - {x: 23, y: 1764}
  - {x: 24, y: 1914}
  - {x: 25, y: 2075}
  - {x: 26, y: 2248}
  - {x: 27, y: 2434}
  - {x: 28, y: 2634}
  - {x: 29, y: 2848}
  - {x: 30, y: 3078}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.0993104}
  - {x: 3, y: 1.0986208}
  - {x: 4, y: 1.097931}
  - {x: 5, y: 1.0972414}
  - {x: 6, y: 1.0965518}
  - {x: 7, y: 1.0958622}
  - {x: 8, y: 1.0951724}
  - {x: 9, y: 1.0944828}
  - {x: 10, y: 1.0937932}
  - {x: 11, y: 1.0931035}
  - {x: 12, y: 1.0924138}
  - {x: 13, y: 1.0917242}
  - {x: 14, y: 1.0910345}
  - {x: 15, y: 1.0903449}
  - {x: 16, y: 1.0896552}
  - {x: 17, y: 1.0889655}
  - {x: 18, y: 1.0882759}
  - {x: 19, y: 1.0875863}
  - {x: 20, y: 1.0868965}
  - {x: 21, y: 1.0862069}
  - {x: 22, y: 1.0855173}
  - {x: 23, y: 1.0848277}
  - {x: 24, y: 1.0841379}
  - {x: 25, y: 1.0834483}
  - {x: 26, y: 1.0827587}
  - {x: 27, y: 1.082069}
  - {x: 28, y: 1.0813793}
  - {x: 29, y: 1.0806897}
  - {x: 30, y: 1.08}
  cachedRequirementCurve: fb000000fc000000fd000000fe000000ff000000000100000101000002010000030100000401000005010000060100000701000008010000090100000a0100000b0100000c0100000d0100000e0100000f0100001001000011010000120100001301000014010000150100001601000017010000
  cachedRawFormulaCurve: []
  initialZeroMultiplier: 1.1
  finalZeroMultiplier: 1.05
  maxDecreaseFactor: 0.2
