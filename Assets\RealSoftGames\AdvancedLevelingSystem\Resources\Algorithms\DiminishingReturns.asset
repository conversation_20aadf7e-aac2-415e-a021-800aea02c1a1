%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 603315a528cd15249aa1496242c06d9d, type: 3}
  m_Name: DiminishingReturns
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 180581b4-862c-41b6-adef-c4a645c1ca15
  algorithmName: Diminishing Returns
  description: As levels increase, the percentage increase in required experience
    gradually diminishes, making high levels more achievable.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 - level/(level+10)))


    As levels increase, the percentage increase in
    required experience gradually diminishes, making high levels more achievable.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 263}
  - {x: 3, y: 265}
  - {x: 4, y: 266}
  - {x: 5, y: 267}
  - {x: 6, y: 268}
  - {x: 7, y: 269}
  - {x: 8, y: 270}
  - {x: 9, y: 271}
  - {x: 10, y: 272}
  - {x: 11, y: 273}
  - {x: 12, y: 274}
  - {x: 13, y: 275}
  - {x: 14, y: 276}
  - {x: 15, y: 277}
  - {x: 16, y: 278}
  - {x: 17, y: 279}
  - {x: 18, y: 280}
  - {x: 19, y: 281}
  - {x: 20, y: 282}
  - {x: 21, y: 283}
  - {x: 22, y: 284}
  - {x: 23, y: 285}
  - {x: 24, y: 286}
  - {x: 25, y: 287}
  - {x: 26, y: 288}
  - {x: 27, y: 289}
  - {x: 28, y: 290}
  - {x: 29, y: 291}
  - {x: 30, y: 292}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0500001}
  - {x: 2, y: 1.0083333}
  - {x: 3, y: 0.97307694}
  - {x: 4, y: 0.94285715}
  - {x: 5, y: 0.9166667}
  - {x: 6, y: 0.89375}
  - {x: 7, y: 0.87352943}
  - {x: 8, y: 0.8555556}
  - {x: 9, y: 0.8394737}
  - {x: 10, y: 0.82500005}
  - {x: 11, y: 0.8119048}
  - {x: 12, y: 0.8}
  - {x: 13, y: 0.78913045}
  - {x: 14, y: 0.7791667}
  - {x: 15, y: 0.77}
  - {x: 16, y: 0.76153845}
  - {x: 17, y: 0.7537037}
  - {x: 18, y: 0.7464286}
  - {x: 19, y: 0.7396552}
  - {x: 20, y: 0.73333335}
  - {x: 21, y: 0.7274194}
  - {x: 22, y: 0.721875}
  - {x: 23, y: 0.7166667}
  - {x: 24, y: 0.7117647}
  - {x: 25, y: 0.7071429}
  - {x: 26, y: 0.7027778}
  - {x: 27, y: 0.6986487}
  - {x: 28, y: 0.69473684}
  - {x: 29, y: 0.6910257}
  - {x: 30, y: 0.6875}
  cachedRequirementCurve: fb000000fc000000fd000000fe000000ff000000000100000101000002010000030100000401000005010000060100000701000008010000090100000a0100000b0100000c0100000d0100000e0100000f0100001001000011010000120100001301000014010000150100001601000017010000
  cachedRawFormulaCurve: []
  diminishingDenominator: 10
  diminishingScaleFactor: 0.5
  zeroBaseMultiplier: 1.1
