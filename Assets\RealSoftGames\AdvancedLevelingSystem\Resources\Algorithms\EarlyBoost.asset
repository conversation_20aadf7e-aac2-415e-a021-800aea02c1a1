%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d91243c7286baf742aed81b7cdb06d78, type: 3}
  m_Name: EarlyBoost
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: adab9e8a-f882-4aaf-a6e5-0895ded6adc7
  algorithmName: Early Boost
  description: Makes early levels very easy to achieve with minimal increases, then
    gradually transitions to normal progression.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (1 + 0.05 * (1 - (level/maxLevel)))


    Makes
    early levels very easy to achieve with minimal increases, then gradually transitions
    to normal progression.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 265}
  - {x: 3, y: 281}
  - {x: 4, y: 299}
  - {x: 5, y: 318}
  - {x: 6, y: 339}
  - {x: 7, y: 362}
  - {x: 8, y: 387}
  - {x: 9, y: 414}
  - {x: 10, y: 443}
  - {x: 11, y: 475}
  - {x: 12, y: 510}
  - {x: 13, y: 548}
  - {x: 14, y: 590}
  - {x: 15, y: 636}
  - {x: 16, y: 686}
  - {x: 17, y: 741}
  - {x: 18, y: 802}
  - {x: 19, y: 869}
  - {x: 20, y: 943}
  - {x: 21, y: 1024}
  - {x: 22, y: 1114}
  - {x: 23, y: 1213}
  - {x: 24, y: 1323}
  - {x: 25, y: 1444}
  - {x: 26, y: 1578}
  - {x: 27, y: 1727}
  - {x: 28, y: 1893}
  - {x: 29, y: 2077}
  - {x: 30, y: 2282}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.06}
  - {x: 2, y: 1.0613793}
  - {x: 3, y: 1.0627586}
  - {x: 4, y: 1.0641379}
  - {x: 5, y: 1.0655172}
  - {x: 6, y: 1.0668966}
  - {x: 7, y: 1.0682758}
  - {x: 8, y: 1.0696552}
  - {x: 9, y: 1.0710344}
  - {x: 10, y: 1.0724138}
  - {x: 11, y: 1.073793}
  - {x: 12, y: 1.0751724}
  - {x: 13, y: 1.0765517}
  - {x: 14, y: 1.077931}
  - {x: 15, y: 1.0793103}
  - {x: 16, y: 1.0806897}
  - {x: 17, y: 1.0820689}
  - {x: 18, y: 1.0834483}
  - {x: 19, y: 1.0848275}
  - {x: 20, y: 1.0862069}
  - {x: 21, y: 1.0875862}
  - {x: 22, y: 1.0889655}
  - {x: 23, y: 1.0903448}
  - {x: 24, y: 1.0917242}
  - {x: 25, y: 1.0931034}
  - {x: 26, y: 1.0944828}
  - {x: 27, y: 1.095862}
  - {x: 28, y: 1.0972414}
  - {x: 29, y: 1.0986207}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: 040100000e01000018010000220100002c01000036010000400100004a010000540100005d010000660100006f01000078010000800100008801000090010000970100009e010000a4010000aa010000af010000b4010000b8010000bc010000bf010000c1010000c3010000c4010000c5010000
  cachedRawFormulaCurve: []
  initialBoostMultiplier: 1.05
  minBlendFactor: 0.2
  maxBoostFactor: 0.05
