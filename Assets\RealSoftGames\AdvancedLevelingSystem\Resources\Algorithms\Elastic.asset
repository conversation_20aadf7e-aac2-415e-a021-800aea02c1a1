%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f50ecfaf0b3a83c44b044f0caa8bf5bf, type: 3}
  m_Name: Elastic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 49c2bab6-85dd-477b-ba1c-50f13ea6db91
  algorithmName: Elastic
  description: Creates an elastic bouncing effect where progression speeds up then
    slows down like a rubber band stretching and contracting.
  formulaExplanation: 'Formula: Uses damped oscillation with exponential decay


    Creates
    an elastic bouncing effect where progression speeds up then slows down, with
    the oscillations gradually diminishing over time, like a rubber band that stretches
    and contracts with decreasing intensity.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 305}
  - {x: 4, y: 339}
  - {x: 5, y: 378}
  - {x: 6, y: 421}
  - {x: 7, y: 470}
  - {x: 8, y: 527}
  - {x: 9, y: 593}
  - {x: 10, y: 671}
  - {x: 11, y: 762}
  - {x: 12, y: 868}
  - {x: 13, y: 991}
  - {x: 14, y: 1134}
  - {x: 15, y: 1302}
  - {x: 16, y: 1501}
  - {x: 17, y: 1737}
  - {x: 18, y: 2017}
  - {x: 19, y: 2349}
  - {x: 20, y: 2744}
  - {x: 21, y: 3216}
  - {x: 22, y: 3781}
  - {x: 23, y: 4460}
  - {x: 24, y: 5278}
  - {x: 25, y: 6267}
  - {x: 26, y: 7465}
  - {x: 27, y: 8920}
  - {x: 28, y: 10691}
  - {x: 29, y: 12854}
  - {x: 30, y: 15504}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.1086041}
  - {x: 3, y: 1.1128289}
  - {x: 4, y: 1.1136845}
  - {x: 5, y: 1.1139709}
  - {x: 6, y: 1.1160233}
  - {x: 7, y: 1.1204035}
  - {x: 8, y: 1.1260694}
  - {x: 9, y: 1.1314875}
  - {x: 10, y: 1.1357248}
  - {x: 11, y: 1.1388508}
  - {x: 12, y: 1.1416025}
  - {x: 13, y: 1.1447332}
  - {x: 14, y: 1.148542}
  - {x: 15, y: 1.1528217}
  - {x: 16, y: 1.1571337}
  - {x: 17, y: 1.1611477}
  - {x: 18, y: 1.1648139}
  - {x: 19, y: 1.1683123}
  - {x: 20, y: 1.1718742}
  - {x: 21, y: 1.1756239}
  - {x: 22, y: 1.1795337}
  - {x: 23, y: 1.1834847}
  - {x: 24, y: 1.1873671}
  - {x: 25, y: 1.1911457}
  - {x: 26, y: 1.1948597}
  - {x: 27, y: 1.198577}
  - {x: 28, y: 1.2023435}
  - {x: 29, y: 1.2061611}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000180200002002000029020000330200003e0200004b02000059020000690200007b0200008e020000a3020000ba020000d4020000f00200000f03000031030000560300007e030000aa030000da0300000e0400004704000086040000cb0400001605000068050000c205000025060000920600000a0700008e07000020080000c108000073090000380a0000130b0000060c0000140d0000410e0000900f000006110000a81200007c140000
  cachedRawFormulaCurve: []
  frequency: 8
  dampingFactor: 5
  baseAmplitude: 0.15
  zeroBaseMultiplier: 1.05
  zeroGrowthFactor: 0.05
  growthFactor: 0.1
