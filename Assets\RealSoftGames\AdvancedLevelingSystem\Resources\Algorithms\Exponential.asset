%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dda1f316be78c3545989ba84ec96567d, type: 3}
  m_Name: Exponential
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: d22ed41d-a4e2-4839-adf7-dcbba3b19833
  algorithmName: Exponential
  description: Experience requirements grow exponentially, making higher levels significantly
    more challenging.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 0.01 * level))


    Experience requirements grow exponentially, making
    higher levels significantly more challenging.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 278}
  - {x: 3, y: 312}
  - {x: 4, y: 353}
  - {x: 5, y: 404}
  - {x: 6, y: 467}
  - {x: 7, y: 545}
  - {x: 8, y: 641}
  - {x: 9, y: 762}
  - {x: 10, y: 914}
  - {x: 11, y: 1106}
  - {x: 12, y: 1350}
  - {x: 13, y: 1663}
  - {x: 14, y: 2067}
  - {x: 15, y: 2592}
  - {x: 16, y: 3279}
  - {x: 17, y: 4184}
  - {x: 18, y: 5385}
  - {x: 19, y: 6990}
  - {x: 20, y: 9150}
  - {x: 21, y: 12078}
  - {x: 22, y: 16076}
  - {x: 23, y: 21574}
  - {x: 24, y: 29190}
  - {x: 25, y: 39815}
  - {x: 26, y: 54746}
  - {x: 27, y: 75878}
  - {x: 28, y: 106002}
  - {x: 29, y: 149251}
  - {x: 30, y: 211787}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1110001}
  - {x: 2, y: 1.122}
  - {x: 3, y: 1.133}
  - {x: 4, y: 1.1439999}
  - {x: 5, y: 1.155}
  - {x: 6, y: 1.166}
  - {x: 7, y: 1.177}
  - {x: 8, y: 1.1880001}
  - {x: 9, y: 1.1990001}
  - {x: 10, y: 1.21}
  - {x: 11, y: 1.2210001}
  - {x: 12, y: 1.232}
  - {x: 13, y: 1.243}
  - {x: 14, y: 1.2540001}
  - {x: 15, y: 1.265}
  - {x: 16, y: 1.276}
  - {x: 17, y: 1.287}
  - {x: 18, y: 1.298}
  - {x: 19, y: 1.309}
  - {x: 20, y: 1.32}
  - {x: 21, y: 1.3310001}
  - {x: 22, y: 1.342}
  - {x: 23, y: 1.353}
  - {x: 24, y: 1.3640001}
  - {x: 25, y: 1.375}
  - {x: 26, y: 1.386}
  - {x: 27, y: 1.397}
  - {x: 28, y: 1.408}
  - {x: 29, y: 1.419}
  - {x: 30, y: 1.43}
  cachedRequirementCurve: f9010000030200001202000027020000430200006602000091020000c60200000603000053030000b103000022040000ac040000530500001f0600001a0700004f080000ce090000ab0b0000000e0000f1100000ab1400006c190000861f000068270000a73100000f3f0000b75000001f6800005c87000052b1000010ea00004e37010026a1010026330200e2fd020042190400faa70500b0dc0700c3010b000b850f00b8091600a8831f006d612d0044cd4100161260004f398d00e602d100486d3701
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  levelMultiplierCoefficient: 0.01
