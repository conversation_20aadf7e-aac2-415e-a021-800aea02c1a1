%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43d8b39a9ea38a34b8fb328c7ed558c7, type: 3}
  m_Name: Fibonacci
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 59cb9415-9fbd-47b9-8a9f-fca0db06eb19
  algorithmName: Fibonacci
  description: Uses the Fibonacci sequence to determine experience requirements,
    creating an interesting progression pattern.
  formulaExplanation: 'Formula: Uses Fibonacci sequence for multiplier


    Uses
    the Fibonacci sequence to determine experience requirements, creating an interesting
    progression pattern.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 296}
  - {x: 4, y: 323}
  - {x: 5, y: 362}
  - {x: 6, y: 401}
  - {x: 7, y: 446}
  - {x: 8, y: 495}
  - {x: 9, y: 550}
  - {x: 10, y: 611}
  - {x: 11, y: 679}
  - {x: 12, y: 755}
  - {x: 13, y: 839}
  - {x: 14, y: 932}
  - {x: 15, y: 1036}
  - {x: 16, y: 1151}
  - {x: 17, y: 1279}
  - {x: 18, y: 1421}
  - {x: 19, y: 1579}
  - {x: 20, y: 1755}
  - {x: 21, y: 1950}
  - {x: 22, y: 2167}
  - {x: 23, y: 2408}
  - {x: 24, y: 2676}
  - {x: 25, y: 2974}
  - {x: 26, y: 3305}
  - {x: 27, y: 3673}
  - {x: 28, y: 4082}
  - {x: 29, y: 4536}
  - {x: 30, y: 5041}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1}
  - {x: 2, y: 1.1800001}
  - {x: 3, y: 1.09}
  - {x: 4, y: 1.12}
  - {x: 5, y: 1.108}
  - {x: 6, y: 1.1125001}
  - {x: 7, y: 1.1107693}
  - {x: 8, y: 1.1114286}
  - {x: 9, y: 1.1111765}
  - {x: 10, y: 1.1112727}
  - {x: 11, y: 1.111236}
  - {x: 12, y: 1.11125}
  - {x: 13, y: 1.1112447}
  - {x: 14, y: 1.1112467}
  - {x: 15, y: 1.1112459}
  - {x: 16, y: 1.1112462}
  - {x: 17, y: 1.1112461}
  - {x: 18, y: 1.1112461}
  - {x: 19, y: 1.1112461}
  - {x: 20, y: 1.1112461}
  - {x: 21, y: 1.1112461}
  - {x: 22, y: 1.1112461}
  - {x: 23, y: 1.1112461}
  - {x: 24, y: 1.1112461}
  - {x: 25, y: 1.1112461}
  - {x: 26, y: 1.1112461}
  - {x: 27, y: 1.1112461}
  - {x: 28, y: 1.1112461}
  - {x: 29, y: 1.1112461}
  - {x: 30, y: 1.1112461}
  cachedRequirementCurve: f501000027020000430200006a0200008f020000b8020000e3020000110300004103000074030000ab030000e50300002304000064040000a9040000f30400004105000094050000ec0500004a060000ae0600001807000088070000ff0700007e08000004090000930900002a0a0000cb0a0000760b00002b0c0000ec0c0000b80d0000910e0000770f00006c1000007011000084120000a9130000e01400002a16000089170000fd180000881a00002c1c0000ea1d0000c31f0000ba210000d0230000
  cachedRawFormulaCurve: []
  zeroScaleFactor: 0.1
  minScaleFactor: 0.1
  maxScaleFactor: 0.5
