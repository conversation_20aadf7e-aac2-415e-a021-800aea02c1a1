%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f08c42aff6e4cd4a90903c9d3eb6b11, type: 3}
  m_Name: Fractional
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: fb9c59d7-4a07-449f-9bf0-4088de8a4180
  algorithmName: Fractional
  description: Uses fractional powers for a unique curve between linear and exponential
    growth. Creates a mathematically interesting progression pattern that starts
    steeper and gradually flattens.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (1.5^(1.3/level))


    Uses
    fractional powers for a unique curve between linear and exponential growth. Creates
    a mathematically interesting progression pattern that starts steeper and gradually
    flattens.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 283}
  - {x: 3, y: 301}
  - {x: 4, y: 314}
  - {x: 5, y: 324}
  - {x: 6, y: 332}
  - {x: 7, y: 339}
  - {x: 8, y: 345}
  - {x: 9, y: 350}
  - {x: 10, y: 355}
  - {x: 11, y: 359}
  - {x: 12, y: 363}
  - {x: 13, y: 367}
  - {x: 14, y: 371}
  - {x: 15, y: 374}
  - {x: 16, y: 377}
  - {x: 17, y: 380}
  - {x: 18, y: 383}
  - {x: 19, y: 386}
  - {x: 20, y: 389}
  - {x: 21, y: 391}
  - {x: 22, y: 393}
  - {x: 23, y: 395}
  - {x: 24, y: 397}
  - {x: 25, y: 399}
  - {x: 26, y: 401}
  - {x: 27, y: 403}
  - {x: 28, y: 405}
  - {x: 29, y: 407}
  - {x: 30, y: 409}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1319064}
  - {x: 2, y: 1.0639108}
  - {x: 3, y: 1.0421659}
  - {x: 4, y: 1.0314605}
  - {x: 5, y: 1.0250902}
  - {x: 6, y: 1.0208652}
  - {x: 7, y: 1.017858}
  - {x: 8, y: 1.0156084}
  - {x: 9, y: 1.0138623}
  - {x: 10, y: 1.0124674}
  - {x: 11, y: 1.0113276}
  - {x: 12, y: 1.0103787}
  - {x: 13, y: 1.0095766}
  - {x: 14, y: 1.0088896}
  - {x: 15, y: 1.0082945}
  - {x: 16, y: 1.007774}
  - {x: 17, y: 1.007315}
  - {x: 18, y: 1.0069072}
  - {x: 19, y: 1.0065426}
  - {x: 20, y: 1.0062144}
  - {x: 21, y: 1.0059175}
  - {x: 22, y: 1.0056479}
  - {x: 23, y: 1.0054016}
  - {x: 24, y: 1.005176}
  - {x: 25, y: 1.0049684}
  - {x: 26, y: 1.0047768}
  - {x: 27, y: 1.0045996}
  - {x: 28, y: 1.004435}
  - {x: 29, y: 1.0042816}
  - {x: 30, y: 1.0041387}
  cachedRequirementCurve: f5010000f6010000f7010000f8010000f9010000fa010000fb010000fc010000fd010000fe010000ff010000000200000102000002020000030200000402000005020000060200000702000008020000090200000a0200000b0200000c0200000d0200000e0200000f020000100200001102000012020000130200001402000015020000160200001702000018020000190200001a0200001b0200001c0200001d0200001e0200001f020000200200002102000022020000230200002402000025020000
  cachedRawFormulaCurve: []
  basePower: 1.5
  exponentNumerator: 1.3
