%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cb5ccb8c8b12704691a5c0cbdb07a78, type: 3}
  m_Name: Geometric
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: dcdc2b94-71c0-4068-ade9-773530a23315
  algorithmName: Geometric
  description: Similar to exponential, creates a geometric progression where each
    level is a fixed multiple of the previous.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier^(level/10))


    Similar
    to exponential, creates a geometric progression where each level is a fixed multiple
    of the previous.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 252}
  - {x: 3, y: 257}
  - {x: 4, y: 264}
  - {x: 5, y: 274}
  - {x: 6, y: 287}
  - {x: 7, y: 304}
  - {x: 8, y: 325}
  - {x: 9, y: 351}
  - {x: 10, y: 382}
  - {x: 11, y: 420}
  - {x: 12, y: 466}
  - {x: 13, y: 522}
  - {x: 14, y: 591}
  - {x: 15, y: 675}
  - {x: 16, y: 779}
  - {x: 17, y: 907}
  - {x: 18, y: 1067}
  - {x: 19, y: 1267}
  - {x: 20, y: 1519}
  - {x: 21, y: 1838}
  - {x: 22, y: 2245}
  - {x: 23, y: 2769}
  - {x: 24, y: 3448}
  - {x: 25, y: 4334}
  - {x: 26, y: 5500}
  - {x: 27, y: 7047}
  - {x: 28, y: 9115}
  - {x: 29, y: 11903}
  - {x: 30, y: 15693}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0095766}
  - {x: 2, y: 1.0192449}
  - {x: 3, y: 1.0290058}
  - {x: 4, y: 1.0388601}
  - {x: 5, y: 1.0488088}
  - {x: 6, y: 1.0588529}
  - {x: 7, y: 1.0689931}
  - {x: 8, y: 1.0792303}
  - {x: 9, y: 1.0895658}
  - {x: 10, y: 1.1}
  - {x: 11, y: 1.1105343}
  - {x: 12, y: 1.1211694}
  - {x: 13, y: 1.1319064}
  - {x: 14, y: 1.1427462}
  - {x: 15, y: 1.1536897}
  - {x: 16, y: 1.1647382}
  - {x: 17, y: 1.1758924}
  - {x: 18, y: 1.1871535}
  - {x: 19, y: 1.1985223}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.2215878}
  - {x: 22, y: 1.2332864}
  - {x: 23, y: 1.245097}
  - {x: 24, y: 1.2570208}
  - {x: 25, y: 1.2690588}
  - {x: 26, y: 1.2812121}
  - {x: 27, y: 1.2934817}
  - {x: 28, y: 1.3058687}
  - {x: 29, y: 1.3183745}
  - {x: 30, y: 1.3310001}
  cachedRequirementCurve: f5010000f6010000f7010000f8010000f9010000fa010000fb010000fc010000fd010000fe010000ff010000000200000102000002020000030200000402000005020000060200000702000008020000090200000a0200000b0200000c0200000d0200000e0200000f020000100200001102000012020000130200001402000015020000160200001702000018020000190200001a0200001b0200001c0200001d0200001e0200001f020000200200002102000022020000230200002402000025020000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.1
  levelScalingFactor: 0.1
