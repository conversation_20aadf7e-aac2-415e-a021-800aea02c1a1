%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ffc58dae5d4de7d4890e2b762a058cd5, type: 3}
  m_Name: Harmonic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 8cb5bd2a-7be3-440f-8335-ca420c99a494
  algorithmName: Harmonic
  description: Uses the harmonic series for a gradually decreasing growth rate, creating
    a balanced progression curve.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 1/(level+1)))


    Uses the harmonic series for a gradually decreasing
    growth rate, creating a balanced progression curve.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 413}
  - {x: 3, y: 606}
  - {x: 4, y: 833}
  - {x: 5, y: 1100}
  - {x: 6, y: 1412}
  - {x: 7, y: 1775}
  - {x: 8, y: 2197}
  - {x: 9, y: 2685}
  - {x: 10, y: 3249}
  - {x: 11, y: 3899}
  - {x: 12, y: 4646}
  - {x: 13, y: 5504}
  - {x: 14, y: 6487}
  - {x: 15, y: 7611}
  - {x: 16, y: 8895}
  - {x: 17, y: 10360}
  - {x: 18, y: 12029}
  - {x: 19, y: 13928}
  - {x: 20, y: 16087}
  - {x: 21, y: 18538}
  - {x: 22, y: 21319}
  - {x: 23, y: 24471}
  - {x: 24, y: 28040}
  - {x: 25, y: 32078}
  - {x: 26, y: 36643}
  - {x: 27, y: 41800}
  - {x: 28, y: 47622}
  - {x: 29, y: 54191}
  - {x: 30, y: 61597}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.6500001}
  - {x: 2, y: 1.4666667}
  - {x: 3, y: 1.375}
  - {x: 4, y: 1.32}
  - {x: 5, y: 1.2833333}
  - {x: 6, y: 1.2571429}
  - {x: 7, y: 1.2375001}
  - {x: 8, y: 1.2222223}
  - {x: 9, y: 1.21}
  - {x: 10, y: 1.2}
  - {x: 11, y: 1.1916667}
  - {x: 12, y: 1.1846155}
  - {x: 13, y: 1.1785715}
  - {x: 14, y: 1.1733334}
  - {x: 15, y: 1.16875}
  - {x: 16, y: 1.164706}
  - {x: 17, y: 1.1611111}
  - {x: 18, y: 1.1578948}
  - {x: 19, y: 1.155}
  - {x: 20, y: 1.1523811}
  - {x: 21, y: 1.15}
  - {x: 22, y: 1.1478261}
  - {x: 23, y: 1.1458334}
  - {x: 24, y: 1.1439999}
  - {x: 25, y: 1.1423078}
  - {x: 26, y: 1.1407408}
  - {x: 27, y: 1.1392857}
  - {x: 28, y: 1.137931}
  - {x: 29, y: 1.1366667}
  - {x: 30, y: 1.1354839}
  cachedRequirementCurve: 77010000f401000071020000ee0200006b030000e803000065040000e20400005f050000dc05000059060000d606000053070000d00700004d080000ca08000047090000c4090000410a0000be0a00003b0b0000b80b0000350c0000b20c00002f0d0000ac0d0000290e0000a60e0000230f0000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
