%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Heartbeat
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 906a4351-33e1-4e34-9cd3-8e1dabb8a6f5
  algorithmName: Heartbeat
  description: A heartbeat-like progression with double peaks.
  formulaExplanation: Experience = Previous * (1.1 + 0.15 * heartbeat(Level))
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 1
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 60
  cachedLevelUpMultiplier: 0.92
  cachedRequirementCurvePoints:
  - {x: 2, y: 264}
  - {x: 3, y: 289}
  - {x: 4, y: 325}
  - {x: 5, y: 372}
  - {x: 6, y: 428}
  - {x: 7, y: 489}
  - {x: 8, y: 549}
  - {x: 9, y: 600}
  - {x: 10, y: 633}
  - {x: 11, y: 641}
  - {x: 12, y: 676}
  - {x: 13, y: 739}
  - {x: 14, y: 830}
  - {x: 15, y: 949}
  - {x: 16, y: 1091}
  - {x: 17, y: 1247}
  - {x: 18, y: 1401}
  - {x: 19, y: 1531}
  - {x: 20, y: 1615}
  - {x: 21, y: 1634}
  - {x: 22, y: 1723}
  - {x: 23, y: 1883}
  - {x: 24, y: 2116}
  - {x: 25, y: 2419}
  - {x: 26, y: 2782}
  - {x: 27, y: 3181}
  - {x: 28, y: 3574}
  - {x: 29, y: 3907}
  - {x: 30, y: 4120}
  - {x: 31, y: 4169}
  - {x: 32, y: 4397}
  - {x: 33, y: 4806}
  - {x: 34, y: 5400}
  - {x: 35, y: 6174}
  - {x: 36, y: 7100}
  - {x: 37, y: 8117}
  - {x: 38, y: 9121}
  - {x: 39, y: 9970}
  - {x: 40, y: 10515}
  - {x: 41, y: 10641}
  - {x: 42, y: 11222}
  - {x: 43, y: 12267}
  - {x: 44, y: 13784}
  - {x: 45, y: 15758}
  - {x: 46, y: 18122}
  - {x: 47, y: 20718}
  - {x: 48, y: 23280}
  - {x: 49, y: 25448}
  - {x: 50, y: 26839}
  - {x: 51, y: 27161}
  - {x: 52, y: 28645}
  - {x: 53, y: 31312}
  - {x: 54, y: 35184}
  - {x: 55, y: 40224}
  - {x: 56, y: 46258}
  - {x: 57, y: 52884}
  - {x: 58, y: 59423}
  - {x: 59, y: 64956}
  - {x: 60, y: 68505}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0546443}
  - {x: 2, y: 1.0931144}
  - {x: 3, y: 1.1236444}
  - {x: 4, y: 1.1432458}
  - {x: 5, y: 1.15}
  - {x: 6, y: 1.1432458}
  - {x: 7, y: 1.1236444}
  - {x: 8, y: 1.0931144}
  - {x: 9, y: 1.0546443}
  - {x: 10, y: 1.0120001}
  - {x: 11, y: 1.0546443}
  - {x: 12, y: 1.0931144}
  - {x: 13, y: 1.1236444}
  - {x: 14, y: 1.1432458}
  - {x: 15, y: 1.15}
  - {x: 16, y: 1.1432458}
  - {x: 17, y: 1.1236444}
  - {x: 18, y: 1.0931144}
  - {x: 19, y: 1.0546443}
  - {x: 20, y: 1.0120001}
  - {x: 21, y: 1.0546445}
  - {x: 22, y: 1.0931144}
  - {x: 23, y: 1.1236444}
  - {x: 24, y: 1.1432458}
  - {x: 25, y: 1.15}
  - {x: 26, y: 1.1432458}
  - {x: 27, y: 1.1236444}
  - {x: 28, y: 1.0931144}
  - {x: 29, y: 1.0546445}
  - {x: 30, y: 1.0120001}
  - {x: 31, y: 1.0546445}
  - {x: 32, y: 1.0931144}
  - {x: 33, y: 1.1236444}
  - {x: 34, y: 1.1432458}
  - {x: 35, y: 1.15}
  - {x: 36, y: 1.1432458}
  - {x: 37, y: 1.1236444}
  - {x: 38, y: 1.0931144}
  - {x: 39, y: 1.0546443}
  - {x: 40, y: 1.0120001}
  - {x: 41, y: 1.0546445}
  - {x: 42, y: 1.0931145}
  - {x: 43, y: 1.1236445}
  - {x: 44, y: 1.1432458}
  - {x: 45, y: 1.15}
  - {x: 46, y: 1.1432458}
  - {x: 47, y: 1.1236444}
  - {x: 48, y: 1.0931144}
  - {x: 49, y: 1.0546443}
  - {x: 50, y: 1.0120002}
  - {x: 51, y: 1.0546445}
  - {x: 52, y: 1.0931145}
  - {x: 53, y: 1.1236445}
  - {x: 54, y: 1.1432459}
  - {x: 55, y: 1.15}
  - {x: 56, y: 1.1432458}
  - {x: 57, y: 1.1236444}
  - {x: 58, y: 1.0931144}
  - {x: 59, y: 1.0546443}
  - {x: 60, y: 1.0120001}
  cachedRequirementCurve: 08010000210100004501000074010000ac010000e901000025020000580200007902000081020000a4020000e30200003e030000b503000043040000df04000079050000fb0500004f06000062060000bb0600005b0700004408000073090000de0a00006d0c0000f60d0000430f000018100000491000002d110000c6120000181500001e180000bc1b0000b51f0000a1230000f22600001329000091290000d62b0000eb2f0000d83500008e3d0000ca460000ee500000f05a000068630000d7680000196a0000e56f0000507a000070890000209d0000b2b4000094ce00001fe80000bcfd0000990b0100
  cachedRawFormulaCurve: []
