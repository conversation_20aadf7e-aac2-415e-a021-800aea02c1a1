%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Heartbeat
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 906a4351-33e1-4e34-9cd3-8e1dabb8a6f5
  algorithmName: Heartbeat
  description: A heartbeat-like progression with double peaks.
  formulaExplanation: Experience = Previous * (1.1 + 0.15 * heartbeat(Level))
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 1
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 315}
  - {x: 3, y: 412}
  - {x: 4, y: 554}
  - {x: 5, y: 757}
  - {x: 6, y: 1041}
  - {x: 7, y: 1423}
  - {x: 8, y: 1912}
  - {x: 9, y: 2499}
  - {x: 10, y: 3151}
  - {x: 11, y: 3813}
  - {x: 12, y: 4808}
  - {x: 13, y: 6284}
  - {x: 14, y: 8442}
  - {x: 15, y: 11540}
  - {x: 16, y: 15868}
  - {x: 17, y: 21690}
  - {x: 18, y: 29140}
  - {x: 19, y: 38086}
  - {x: 20, y: 48026}
  - {x: 21, y: 58111}
  - {x: 22, y: 73277}
  - {x: 23, y: 95772}
  - {x: 24, y: 128669}
  - {x: 25, y: 175881}
  - {x: 26, y: 241836}
  - {x: 27, y: 330572}
  - {x: 28, y: 444119}
  - {x: 29, y: 580457}
  - {x: 30, y: 731949}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.2609878}
  - {x: 2, y: 1.3069847}
  - {x: 3, y: 1.3434879}
  - {x: 4, y: 1.3669244}
  - {x: 5, y: 1.375}
  - {x: 6, y: 1.3669244}
  - {x: 7, y: 1.3434879}
  - {x: 8, y: 1.3069847}
  - {x: 9, y: 1.2609878}
  - {x: 10, y: 1.21}
  - {x: 11, y: 1.2609878}
  - {x: 12, y: 1.3069847}
  - {x: 13, y: 1.3434879}
  - {x: 14, y: 1.3669244}
  - {x: 15, y: 1.375}
  - {x: 16, y: 1.3669244}
  - {x: 17, y: 1.3434879}
  - {x: 18, y: 1.3069847}
  - {x: 19, y: 1.2609878}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.260988}
  - {x: 22, y: 1.3069847}
  - {x: 23, y: 1.3434879}
  - {x: 24, y: 1.3669244}
  - {x: 25, y: 1.375}
  - {x: 26, y: 1.3669244}
  - {x: 27, y: 1.3434879}
  - {x: 28, y: 1.3069847}
  - {x: 29, y: 1.260988}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: 3d020000a9020000400300000a0400000c05000046060000a90700001a0900006f0a00007a0b0000280d0000a20f000018130000ba170000a81d0000da240000022d00007a3500004e3d00006f4300004d4d0000d95b00002e700000678b000041ae00008ad80000780801003c3a0100396801003f8c01003dc60100b61b02002d93020021330300e9ff03005ff80400041206006e360700a944080054180900166d0a0052630c004d210f0031cd12007d80170071341d005fab23009a612a0078953000
  cachedRawFormulaCurve: []
