%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcd560f2d74c9c441b72c1c90e67b7d0, type: 3}
  m_Name: Hyperbolic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: a5421341-5ade-4775-acb0-1afb728922d2
  algorithmName: Hyperbolic
  description: Uses a hyperbolic function that grows more slowly at higher levels,
    creating a more manageable progression.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 - 1/(level+1)))


    Uses a hyperbolic function that grows more slowly at
    higher levels, creating a more manageable progression.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.1
