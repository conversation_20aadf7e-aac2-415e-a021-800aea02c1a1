%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcd560f2d74c9c441b72c1c90e67b7d0, type: 3}
  m_Name: Hyperbolic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: a5421341-5ade-4775-acb0-1afb728922d2
  algorithmName: Hyperbolic
  description: Uses a hyperbolic function that grows more slowly at higher levels,
    creating a more manageable progression.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 - 1/(level+1)))


    Uses a hyperbolic function that grows more slowly at
    higher levels, creating a more manageable progression.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 254}
  - {x: 6, y: 255}
  - {x: 7, y: 256}
  - {x: 8, y: 257}
  - {x: 9, y: 258}
  - {x: 10, y: 259}
  - {x: 11, y: 260}
  - {x: 12, y: 262}
  - {x: 13, y: 266}
  - {x: 14, y: 272}
  - {x: 15, y: 279}
  - {x: 16, y: 288}
  - {x: 17, y: 298}
  - {x: 18, y: 310}
  - {x: 19, y: 323}
  - {x: 20, y: 338}
  - {x: 21, y: 354}
  - {x: 22, y: 372}
  - {x: 23, y: 391}
  - {x: 24, y: 412}
  - {x: 25, y: 435}
  - {x: 26, y: 460}
  - {x: 27, y: 487}
  - {x: 28, y: 517}
  - {x: 29, y: 549}
  - {x: 30, y: 584}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.55}
  - {x: 2, y: 0.73333335}
  - {x: 3, y: 0.82500005}
  - {x: 4, y: 0.88000005}
  - {x: 5, y: 0.9166667}
  - {x: 6, y: 0.94285715}
  - {x: 7, y: 0.96250004}
  - {x: 8, y: 0.9777778}
  - {x: 9, y: 0.99}
  - {x: 10, y: 1}
  - {x: 11, y: 1.0083333}
  - {x: 12, y: 1.0153847}
  - {x: 13, y: 1.0214286}
  - {x: 14, y: 1.0266666}
  - {x: 15, y: 1.03125}
  - {x: 16, y: 1.0352942}
  - {x: 17, y: 1.0388889}
  - {x: 18, y: 1.0421053}
  - {x: 19, y: 1.045}
  - {x: 20, y: 1.0476191}
  - {x: 21, y: 1.05}
  - {x: 22, y: 1.052174}
  - {x: 23, y: 1.0541667}
  - {x: 24, y: 1.056}
  - {x: 25, y: 1.0576923}
  - {x: 26, y: 1.0592593}
  - {x: 27, y: 1.0607144}
  - {x: 28, y: 1.0620689}
  - {x: 29, y: 1.0633333}
  - {x: 30, y: 1.0645161}
  cachedRequirementCurve: f5010000f6010000f7010000f8010000f9010000fa010000fb010000fc010000fd010000fe010000ff010000000200000102000002020000030200000402000005020000060200000702000008020000090200000a0200000b0200000c0200000d0200000e0200000f020000100200001102000012020000130200001402000015020000160200001702000018020000190200001a0200001b0200001c0200001d0200001e0200001f020000200200002102000022020000230200002402000025020000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.1
