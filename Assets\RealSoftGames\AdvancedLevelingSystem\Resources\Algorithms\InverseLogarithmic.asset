%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ac786ce42e67084a9d13e27d70bede0, type: 3}
  m_Name: InverseLogarithmic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: f0541ffb-4593-404f-a06e-48c58eb3bcc8
  algorithmName: Inverse Logarithmic
  description: Inverse of logarithmic growth - starts slow and accelerates over time,
    creating a challenging late-game progression.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + log10(level+1)/log10(maxLevel+1) * 0.5))


    Inverse of logarithmic growth
    - starts slow and accelerates over time, creating a challenging late-game progression.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 303}
  - {x: 3, y: 387}
  - {x: 4, y: 512}
  - {x: 5, y: 695}
  - {x: 6, y: 964}
  - {x: 7, y: 1361}
  - {x: 8, y: 1950}
  - {x: 9, y: 2831}
  - {x: 10, y: 4158}
  - {x: 11, y: 6171}
  - {x: 12, y: 9244}
  - {x: 13, y: 13966}
  - {x: 14, y: 21266}
  - {x: 15, y: 32616}
  - {x: 16, y: 50361}
  - {x: 17, y: 78250}
  - {x: 18, y: 122299}
  - {x: 19, y: 192204}
  - {x: 20, y: 303645}
  - {x: 21, y: 482073}
  - {x: 22, y: 768942}
  - {x: 23, y: 1231993}
  - {x: 24, y: 1982288}
  - {x: 25, y: 3202480}
  - {x: 26, y: 5193874}
  - {x: 27, y: 8454969}
  - {x: 28, y: 13812867}
  - {x: 29, y: 22643688}
  - {x: 30, y: 37243164}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.211017}
  - {x: 2, y: 1.2759578}
  - {x: 3, y: 1.322034}
  - {x: 4, y: 1.3577735}
  - {x: 5, y: 1.3869748}
  - {x: 6, y: 1.4116641}
  - {x: 7, y: 1.433051}
  - {x: 8, y: 1.4519156}
  - {x: 9, y: 1.4687905}
  - {x: 10, y: 1.4840558}
  - {x: 11, y: 1.4979918}
  - {x: 12, y: 1.5108117}
  - {x: 13, y: 1.5226811}
  - {x: 14, y: 1.5337313}
  - {x: 15, y: 1.544068}
  - {x: 16, y: 1.5537779}
  - {x: 17, y: 1.5629326}
  - {x: 18, y: 1.5715922}
  - {x: 19, y: 1.5798075}
  - {x: 20, y: 1.5876219}
  - {x: 21, y: 1.5950727}
  - {x: 22, y: 1.6021923}
  - {x: 23, y: 1.6090088}
  - {x: 24, y: 1.6155471}
  - {x: 25, y: 1.6218288}
  - {x: 26, y: 1.6278734}
  - {x: 27, y: 1.6336981}
  - {x: 28, y: 1.6393185}
  - {x: 29, y: 1.6447482}
  - {x: 30, y: 1.6500001}
  cachedRequirementCurve: 200200006c020000d90200006e0300003604000041050000a506000080080000fd0a0000570e0000df120000071900006d210000f02c0000c83c0000ae52000012710000689b00009cd60000b3290100b89e010015440200852e0300ef7b04008d5706000500090058d00c00054d120064371a0032aa2500c9433600a7644e00338c7100a4e2a4005006f000ea3d5e01f041000234e9ee02c82a4f04e821580620195c092028d60da0bb7e14c0966a1e00b6392d804f5e43808188648181886482818864
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  maxIncreasePercentage: 0.5
