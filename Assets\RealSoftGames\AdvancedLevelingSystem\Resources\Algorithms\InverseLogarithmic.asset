%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ac786ce42e67084a9d13e27d70bede0, type: 3}
  m_Name: InverseLogarithmic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: f0541ffb-4593-404f-a06e-48c58eb3bcc8
  algorithmName: Inverse Logarithmic
  description: Inverse of logarithmic growth - starts slow and accelerates over time,
    creating a challenging late-game progression.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + log10(level+1)/log10(maxLevel+1) * 0.5))


    Inverse of logarithmic growth
    - starts slow and accelerates over time, creating a challenging late-game progression.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 303}
  - {x: 3, y: 387}
  - {x: 4, y: 512}
  - {x: 5, y: 695}
  - {x: 6, y: 964}
  - {x: 7, y: 1361}
  - {x: 8, y: 1950}
  - {x: 9, y: 2831}
  - {x: 10, y: 4158}
  - {x: 11, y: 6171}
  - {x: 12, y: 9244}
  - {x: 13, y: 13966}
  - {x: 14, y: 21266}
  - {x: 15, y: 32616}
  - {x: 16, y: 50361}
  - {x: 17, y: 78250}
  - {x: 18, y: 122299}
  - {x: 19, y: 192204}
  - {x: 20, y: 303645}
  - {x: 21, y: 482073}
  - {x: 22, y: 768942}
  - {x: 23, y: 1231993}
  - {x: 24, y: 1982288}
  - {x: 25, y: 3202480}
  - {x: 26, y: 5193874}
  - {x: 27, y: 8454969}
  - {x: 28, y: 13812867}
  - {x: 29, y: 22643688}
  - {x: 30, y: 37243164}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.211017}
  - {x: 2, y: 1.2759578}
  - {x: 3, y: 1.322034}
  - {x: 4, y: 1.3577735}
  - {x: 5, y: 1.3869748}
  - {x: 6, y: 1.4116641}
  - {x: 7, y: 1.433051}
  - {x: 8, y: 1.4519156}
  - {x: 9, y: 1.4687905}
  - {x: 10, y: 1.4840558}
  - {x: 11, y: 1.4979918}
  - {x: 12, y: 1.5108117}
  - {x: 13, y: 1.5226811}
  - {x: 14, y: 1.5337313}
  - {x: 15, y: 1.544068}
  - {x: 16, y: 1.5537779}
  - {x: 17, y: 1.5629326}
  - {x: 18, y: 1.5715922}
  - {x: 19, y: 1.5798075}
  - {x: 20, y: 1.5876219}
  - {x: 21, y: 1.5950727}
  - {x: 22, y: 1.6021923}
  - {x: 23, y: 1.6090088}
  - {x: 24, y: 1.6155471}
  - {x: 25, y: 1.6218288}
  - {x: 26, y: 1.6278734}
  - {x: 27, y: 1.6336981}
  - {x: 28, y: 1.6393185}
  - {x: 29, y: 1.6447482}
  - {x: 30, y: 1.6500001}
  cachedRequirementCurve: 130100003f0100007f010000d901000054020000fd020000e503000024050000dd060000420900009b0c000050110000f71700006a210000e72e000040420000225e00007d86000027c10000c61601003d940100ca4c02003e5d0300e3f00400f0480700e5c70a00f10210009cdc1700c0ad2300
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  maxIncreasePercentage: 0.5
