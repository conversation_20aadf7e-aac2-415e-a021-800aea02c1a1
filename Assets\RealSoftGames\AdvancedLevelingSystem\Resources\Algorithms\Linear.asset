%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Linear
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 46708606-dbcd-43ae-9315-948adf92f086
  algorithmName: Linear
  description: A simple linear progression with consistent level-up requirements.
  formulaExplanation: Experience = Previous * 1.1
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 302}
  - {x: 3, y: 365}
  - {x: 4, y: 442}
  - {x: 5, y: 535}
  - {x: 6, y: 647}
  - {x: 7, y: 783}
  - {x: 8, y: 947}
  - {x: 9, y: 1146}
  - {x: 10, y: 1387}
  - {x: 11, y: 1678}
  - {x: 12, y: 2030}
  - {x: 13, y: 2456}
  - {x: 14, y: 2972}
  - {x: 15, y: 3596}
  - {x: 16, y: 4351}
  - {x: 17, y: 5265}
  - {x: 18, y: 6371}
  - {x: 19, y: 7709}
  - {x: 20, y: 9328}
  - {x: 21, y: 11287}
  - {x: 22, y: 13657}
  - {x: 23, y: 16525}
  - {x: 24, y: 19995}
  - {x: 25, y: 24194}
  - {x: 26, y: 29275}
  - {x: 27, y: 35423}
  - {x: 28, y: 42862}
  - {x: 29, y: 51863}
  - {x: 30, y: 62754}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.21}
  - {x: 2, y: 1.21}
  - {x: 3, y: 1.21}
  - {x: 4, y: 1.21}
  - {x: 5, y: 1.21}
  - {x: 6, y: 1.21}
  - {x: 7, y: 1.21}
  - {x: 8, y: 1.21}
  - {x: 9, y: 1.21}
  - {x: 10, y: 1.21}
  - {x: 11, y: 1.21}
  - {x: 12, y: 1.21}
  - {x: 13, y: 1.21}
  - {x: 14, y: 1.21}
  - {x: 15, y: 1.21}
  - {x: 16, y: 1.21}
  - {x: 17, y: 1.21}
  - {x: 18, y: 1.21}
  - {x: 19, y: 1.21}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.21}
  - {x: 22, y: 1.21}
  - {x: 23, y: 1.21}
  - {x: 24, y: 1.21}
  - {x: 25, y: 1.21}
  - {x: 26, y: 1.21}
  - {x: 27, y: 1.21}
  - {x: 28, y: 1.21}
  - {x: 29, y: 1.21}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: 260200005d0200009a020000dd0200002603000077030000d0030000320400009d040000130500009505000024060000c10600006e0700002c080000fd080000e3090000e00a0000f60b0000280d0000790e0000ec0f000084110000441300003115000050170000a5190000361c0000081f0000222200008c2500004d2900006e2d0000f9310000f8360000773c0000834200002a4900007b50000087580000616100001e6b0000d47500009c810000928e0000d49c000083ac0000c3bd0000bdd00000
  cachedRawFormulaCurve: []
