%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b571d22c231c24419287a3e5d97cf7f, type: 3}
  m_Name: Logarithmic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 0eb2510b-a4a8-4954-8a02-aa874a2595ed
  algorithmName: Logarithmic
  description: Experience requirements grow more slowly at higher levels, making
    progression easier over time.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 - log10(level)/log10(maxLevel) * 0.2))


    Experience requirements grow
    more slowly at higher levels, making progression easier over time.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 290}
  - {x: 4, y: 298}
  - {x: 5, y: 301}
  - {x: 6, y: 302}
  - {x: 7, y: 303}
  - {x: 8, y: 304}
  - {x: 9, y: 305}
  - {x: 10, y: 306}
  - {x: 11, y: 307}
  - {x: 12, y: 308}
  - {x: 13, y: 309}
  - {x: 14, y: 310}
  - {x: 15, y: 311}
  - {x: 16, y: 312}
  - {x: 17, y: 313}
  - {x: 18, y: 314}
  - {x: 19, y: 315}
  - {x: 20, y: 316}
  - {x: 21, y: 317}
  - {x: 22, y: 318}
  - {x: 23, y: 319}
  - {x: 24, y: 320}
  - {x: 25, y: 321}
  - {x: 26, y: 322}
  - {x: 27, y: 323}
  - {x: 28, y: 324}
  - {x: 29, y: 325}
  - {x: 30, y: 326}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.055165}
  - {x: 3, y: 1.0289384}
  - {x: 4, y: 1.0103302}
  - {x: 5, y: 0.9958966}
  - {x: 6, y: 0.98410344}
  - {x: 7, y: 0.97413254}
  - {x: 8, y: 0.9654953}
  - {x: 9, y: 0.95787674}
  - {x: 10, y: 0.95106167}
  - {x: 11, y: 0.9448967}
  - {x: 12, y: 0.9392685}
  - {x: 13, y: 0.93409115}
  - {x: 14, y: 0.9292976}
  - {x: 15, y: 0.9248349}
  - {x: 16, y: 0.9206604}
  - {x: 17, y: 0.916739}
  - {x: 18, y: 0.9130418}
  - {x: 19, y: 0.9095445}
  - {x: 20, y: 0.90622675}
  - {x: 21, y: 0.90307087}
  - {x: 22, y: 0.9000618}
  - {x: 23, y: 0.8971865}
  - {x: 24, y: 0.8944336}
  - {x: 25, y: 0.89179313}
  - {x: 26, y: 0.8892562}
  - {x: 27, y: 0.8868151}
  - {x: 28, y: 0.8844627}
  - {x: 29, y: 0.88219285}
  - {x: 30, y: 0.88}
  cachedRequirementCurve: fb000000fc000000fd000000fe000000ff000000000100000101000002010000030100000401000005010000060100000701000008010000090100000a0100000b0100000c0100000d0100000e0100000f0100001001000011010000120100001301000014010000150100001601000017010000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.1
  maxReductionPercentage: 0.2
