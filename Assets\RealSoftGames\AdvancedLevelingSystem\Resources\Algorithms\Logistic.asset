%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6dca88dfe87da214982254a48405aa7a, type: 3}
  m_Name: Logistic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: ca62f28d-4743-4c2d-978b-96732a7f6bec
  algorithmName: Logistic
  description: Creates an S-curve where growth is slow at first, then accelerates
    in the middle, then slows down again near max level.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 1/(1+e^(-(level-maxLevel/2)/(maxLevel/10)))))


    Creates an S-curve where
    growth is slow at first, then accelerates in the middle, then slows down again
    near max level.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 277}
  - {x: 3, y: 308}
  - {x: 4, y: 343}
  - {x: 5, y: 384}
  - {x: 6, y: 433}
  - {x: 7, y: 494}
  - {x: 8, y: 571}
  - {x: 9, y: 672}
  - {x: 10, y: 810}
  - {x: 11, y: 1007}
  - {x: 12, y: 1301}
  - {x: 13, y: 1761}
  - {x: 14, y: 2512}
  - {x: 15, y: 3795}
  - {x: 16, y: 6082}
  - {x: 17, y: 10323}
  - {x: 18, y: 18469}
  - {x: 19, y: 34600}
  - {x: 20, y: 67357}
  - {x: 21, y: 135231}
  - {x: 22, y: 278096}
  - {x: 23, y: 582415}
  - {x: 24, y: 1236447}
  - {x: 25, y: 2651308}
  - {x: 26, y: 5726694}
  - {x: 27, y: 12434525}
  - {x: 28, y: 27101470}
  - {x: 29, y: 59228204}
  - {x: 30, y: 129688150}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1073622}
  - {x: 2, y: 1.1103649}
  - {x: 3, y: 1.1145761}
  - {x: 4, y: 1.120466}
  - {x: 5, y: 1.1286732}
  - {x: 6, y: 1.1400496}
  - {x: 7, y: 1.1557046}
  - {x: 8, y: 1.1770346}
  - {x: 9, y: 1.2057053}
  - {x: 10, y: 1.2435492}
  - {x: 11, y: 1.2923211}
  - {x: 12, y: 1.3532757}
  - {x: 13, y: 1.426598}
  - {x: 14, y: 1.5108472}
  - {x: 15, y: 1.6027033}
  - {x: 16, y: 1.6972967}
  - {x: 17, y: 1.7891529}
  - {x: 18, y: 1.8734021}
  - {x: 19, y: 1.9467244}
  - {x: 20, y: 2.007679}
  - {x: 21, y: 2.0564508}
  - {x: 22, y: 2.0942948}
  - {x: 23, y: 2.1229656}
  - {x: 24, y: 2.1442955}
  - {x: 25, y: 2.1599505}
  - {x: 26, y: 2.1713269}
  - {x: 27, y: 2.179534}
  - {x: 28, y: 2.185424}
  - {x: 29, y: 2.189635}
  - {x: 30, y: 2.192638}
  cachedRequirementCurve: fc000000fe00000001010000060100000d01000017010000250100003a0100005801000085010000c901000032020000d9020000e9030000b2050000ca0800004c0e000059180000172b0000a54e000007930000ed1701003f1c0200221d0400ec130800f1f10f00f0971f00b4c43e00fff17c00
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  scaleFactor: 0.1
  midpointFactor: 0.5
