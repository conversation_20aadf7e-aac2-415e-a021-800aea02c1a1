%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc6e242014e3e44429c11356671cfa55, type: 3}
  m_Name: MicroProgression
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: ccabe48a-8844-4648-88e4-e8681d6e71ea
  algorithmName: Micro Progression
  description: Tiny increments in difficulty using logarithmic scaling to keep increases
    very small.
  formulaExplanation: 'Formula: requiredExp = requiredExp * log10(level+10)/log10(level+9)
    + min(1, requiredExp*0.005)


    Tiny increments in difficulty using logarithmic
    scaling to keep increases very small.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 262}
  - {x: 3, y: 273}
  - {x: 4, y: 284}
  - {x: 5, y: 294}
  - {x: 6, y: 303}
  - {x: 7, y: 312}
  - {x: 8, y: 321}
  - {x: 9, y: 329}
  - {x: 10, y: 337}
  - {x: 11, y: 344}
  - {x: 12, y: 351}
  - {x: 13, y: 358}
  - {x: 14, y: 365}
  - {x: 15, y: 371}
  - {x: 16, y: 377}
  - {x: 17, y: 383}
  - {x: 18, y: 389}
  - {x: 19, y: 395}
  - {x: 20, y: 401}
  - {x: 21, y: 406}
  - {x: 22, y: 411}
  - {x: 23, y: 416}
  - {x: 24, y: 421}
  - {x: 25, y: 426}
  - {x: 26, y: 431}
  - {x: 27, y: 436}
  - {x: 28, y: 441}
  - {x: 29, y: 446}
  - {x: 30, y: 451}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.046532}
  - {x: 2, y: 1.0409151}
  - {x: 3, y: 1.0364327}
  - {x: 4, y: 1.032782}
  - {x: 5, y: 1.0297574}
  - {x: 6, y: 1.0272154}
  - {x: 7, y: 1.0250523}
  - {x: 8, y: 1.0231919}
  - {x: 9, y: 1.0215766}
  - {x: 10, y: 1.0201625}
  - {x: 11, y: 1.0189152}
  - {x: 12, y: 1.0178081}
  - {x: 13, y: 1.016819}
  - {x: 14, y: 1.0159309}
  - {x: 15, y: 1.0151296}
  - {x: 16, y: 1.0144031}
  - {x: 17, y: 1.013742}
  - {x: 18, y: 1.0131378}
  - {x: 19, y: 1.0125842}
  - {x: 20, y: 1.0120747}
  - {x: 21, y: 1.0116048}
  - {x: 22, y: 1.01117}
  - {x: 23, y: 1.0107667}
  - {x: 24, y: 1.0103917}
  - {x: 25, y: 1.0100424}
  - {x: 26, y: 1.0097159}
  - {x: 27, y: 1.0094105}
  - {x: 28, y: 1.0091239}
  - {x: 29, y: 1.008855}
  - {x: 30, y: 1.0086018}
  cachedRequirementCurve: 0a0200001e020000300200004102000051020000600200006e0200007c0200008902000095020000a1020000ac020000b7020000c1020000cb020000d5020000de020000e7020000f0020000f90200000103000009030000110300001903000021030000280300002f030000360300003d030000440300004b03000051030000570300005d03000063030000690300006f030000750300007b03000081030000870300008c03000091030000960300009b030000a0030000a5030000aa030000af030000
  cachedRawFormulaCurve: []
  logBaseOffset: 10
  fixedAdditionCoefficient: 0.005
