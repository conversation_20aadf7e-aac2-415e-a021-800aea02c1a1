%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da7c51d85eb644543b06763531a3f473, type: 3}
  m_Name: Milestone
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: ee130793-2b0f-4b58-84a4-d0198e26df4b
  algorithmName: Milestone
  description: Big jumps at milestone levels (e.g., every 5 levels) with smaller
    increases between milestones.
  formulaExplanation: 'Formula: Uses higher multiplier at milestone levels


    Big
    jumps at milestone levels (e.g., every 5 levels) with smaller increases between
    milestones.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 417}
  - {x: 6, y: 418}
  - {x: 7, y: 419}
  - {x: 8, y: 420}
  - {x: 9, y: 421}
  - {x: 10, y: 695}
  - {x: 11, y: 696}
  - {x: 12, y: 697}
  - {x: 13, y: 698}
  - {x: 14, y: 699}
  - {x: 15, y: 1153}
  - {x: 16, y: 1154}
  - {x: 17, y: 1155}
  - {x: 18, y: 1156}
  - {x: 19, y: 1157}
  - {x: 20, y: 1909}
  - {x: 21, y: 1910}
  - {x: 22, y: 1911}
  - {x: 23, y: 1912}
  - {x: 24, y: 1913}
  - {x: 25, y: 3156}
  - {x: 26, y: 3157}
  - {x: 27, y: 3158}
  - {x: 28, y: 3159}
  - {x: 29, y: 3160}
  - {x: 30, y: 5214}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.88000005}
  - {x: 2, y: 0.88000005}
  - {x: 3, y: 0.88000005}
  - {x: 4, y: 0.88000005}
  - {x: 5, y: 1.6500001}
  - {x: 6, y: 0.88000005}
  - {x: 7, y: 0.88000005}
  - {x: 8, y: 0.88000005}
  - {x: 9, y: 0.88000005}
  - {x: 10, y: 1.6500001}
  - {x: 11, y: 0.88000005}
  - {x: 12, y: 0.88000005}
  - {x: 13, y: 0.88000005}
  - {x: 14, y: 0.88000005}
  - {x: 15, y: 1.6500001}
  - {x: 16, y: 0.88000005}
  - {x: 17, y: 0.88000005}
  - {x: 18, y: 0.88000005}
  - {x: 19, y: 0.88000005}
  - {x: 20, y: 1.6500001}
  - {x: 21, y: 0.88000005}
  - {x: 22, y: 0.88000005}
  - {x: 23, y: 0.88000005}
  - {x: 24, y: 0.88000005}
  - {x: 25, y: 1.6500001}
  - {x: 26, y: 0.88000005}
  - {x: 27, y: 0.88000005}
  - {x: 28, y: 0.88000005}
  - {x: 29, y: 0.88000005}
  - {x: 30, y: 1.6500001}
  cachedRequirementCurve: fb000000fc000000fd0000007c0100007d0100007e0100007f010000800100004002000041020000420200004302000044020000660300006703000068030000690300006a0300001f05000020050000210500002205000023050000b4070000b5070000b6070000b7070000b8070000940b0000
  cachedRawFormulaCurve: []
  milestoneInterval: 5
  zeroMilestoneMultiplier: 1.2
  zeroBetweenMilestoneMultiplier: 1.05
  milestoneIntensity: 1.5
  betweenMilestoneIntensity: 0.8
