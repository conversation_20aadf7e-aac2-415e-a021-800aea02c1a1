%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3aaaf5f6596783e4088dc0b2adb16a20, type: 3}
  m_Name: Plateau
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 0f07e6bb-2471-49ff-8f90-c6a0bcb49652
  algorithmName: Plateau
  description: Creates a pattern with steep climbs followed by extended plateaus,
    giving players time to enjoy their achievements before facing the next challenge.
  formulaExplanation: 'Formula: Combines steep growth with extended flat periods


    Creates
    a progression with short periods of rapid growth followed by longer plateaus
    of consistent difficulty, like climbing a mountain with flat resting areas.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 413}
  - {x: 3, y: 689}
  - {x: 4, y: 1163}
  - {x: 5, y: 1175}
  - {x: 6, y: 1187}
  - {x: 7, y: 1199}
  - {x: 8, y: 1211}
  - {x: 9, y: 1223}
  - {x: 10, y: 1235}
  - {x: 11, y: 1247}
  - {x: 12, y: 2294}
  - {x: 13, y: 4264}
  - {x: 14, y: 8006}
  - {x: 15, y: 8086}
  - {x: 16, y: 8167}
  - {x: 17, y: 8249}
  - {x: 18, y: 8331}
  - {x: 19, y: 8414}
  - {x: 20, y: 8498}
  - {x: 21, y: 8583}
  - {x: 22, y: 17418}
  - {x: 23, y: 35677}
  - {x: 24, y: 73753}
  - {x: 25, y: 74491}
  - {x: 26, y: 75236}
  - {x: 27, y: 75988}
  - {x: 28, y: 76748}
  - {x: 29, y: 77515}
  - {x: 30, y: 78290}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.6500001}
  - {x: 2, y: 1.6689656}
  - {x: 3, y: 1.6879311}
  - {x: 4, y: 1.01}
  - {x: 5, y: 1.01}
  - {x: 6, y: 1.01}
  - {x: 7, y: 1.01}
  - {x: 8, y: 1.01}
  - {x: 9, y: 1.01}
  - {x: 10, y: 1.01}
  - {x: 11, y: 1.8396553}
  - {x: 12, y: 1.8586208}
  - {x: 13, y: 1.8775862}
  - {x: 14, y: 1.01}
  - {x: 15, y: 1.01}
  - {x: 16, y: 1.01}
  - {x: 17, y: 1.01}
  - {x: 18, y: 1.01}
  - {x: 19, y: 1.01}
  - {x: 20, y: 1.01}
  - {x: 21, y: 2.0293105}
  - {x: 22, y: 2.0482757}
  - {x: 23, y: 2.0672414}
  - {x: 24, y: 1.01}
  - {x: 25, y: 1.01}
  - {x: 26, y: 1.01}
  - {x: 27, y: 1.01}
  - {x: 28, y: 1.01}
  - {x: 29, y: 1.01}
  - {x: 30, y: 1.01}
  cachedRequirementCurve: ee0200006d040000bb060000cc060000dd060000ef060000010700001307000025070000370700008f0b0000a31200003d1e00008a1e0000d81e0000271f0000771f0000c81f0000192000006b2000003e370000b35e00004fa30000f1a4000097a6000041a80000f0a90000a3ab00005aad000016af00003a3c01005f3e02001b19040099230400322e0400e6380400b5430400a04e0400a7590400ca640400486208005b1510009d041f0005541f0038a41f0038f51f0008472000a99920001eed2000
  cachedRawFormulaCurve: []
  plateauCycle: 10
  climbDuration: 3
  zeroClimbMultiplier: 1.12
  zeroPlateauMultiplier: 1.01
  climbIntensityBase: 1.5
  climbIntensityGrowth: 0.5
  plateauIntensity: 0.6
