%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3aaaf5f6596783e4088dc0b2adb16a20, type: 3}
  m_Name: Plateau
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 0f07e6bb-2471-49ff-8f90-c6a0bcb49652
  algorithmName: Plateau
  description: Creates a pattern with steep climbs followed by extended plateaus,
    giving players time to enjoy their achievements before facing the next challenge.
  formulaExplanation: 'Formula: Combines steep growth with extended flat periods


    Creates
    a progression with short periods of rapid growth followed by longer plateaus
    of consistent difficulty, like climbing a mountain with flat resting areas.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 413}
  - {x: 3, y: 689}
  - {x: 4, y: 1163}
  - {x: 5, y: 1175}
  - {x: 6, y: 1187}
  - {x: 7, y: 1199}
  - {x: 8, y: 1211}
  - {x: 9, y: 1223}
  - {x: 10, y: 1235}
  - {x: 11, y: 1247}
  - {x: 12, y: 2294}
  - {x: 13, y: 4264}
  - {x: 14, y: 8006}
  - {x: 15, y: 8086}
  - {x: 16, y: 8167}
  - {x: 17, y: 8249}
  - {x: 18, y: 8331}
  - {x: 19, y: 8414}
  - {x: 20, y: 8498}
  - {x: 21, y: 8583}
  - {x: 22, y: 17418}
  - {x: 23, y: 35677}
  - {x: 24, y: 73753}
  - {x: 25, y: 74491}
  - {x: 26, y: 75236}
  - {x: 27, y: 75988}
  - {x: 28, y: 76748}
  - {x: 29, y: 77515}
  - {x: 30, y: 78290}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.6500001}
  - {x: 2, y: 1.6689656}
  - {x: 3, y: 1.6879311}
  - {x: 4, y: 1.01}
  - {x: 5, y: 1.01}
  - {x: 6, y: 1.01}
  - {x: 7, y: 1.01}
  - {x: 8, y: 1.01}
  - {x: 9, y: 1.01}
  - {x: 10, y: 1.01}
  - {x: 11, y: 1.8396553}
  - {x: 12, y: 1.8586208}
  - {x: 13, y: 1.8775862}
  - {x: 14, y: 1.01}
  - {x: 15, y: 1.01}
  - {x: 16, y: 1.01}
  - {x: 17, y: 1.01}
  - {x: 18, y: 1.01}
  - {x: 19, y: 1.01}
  - {x: 20, y: 1.01}
  - {x: 21, y: 2.0293105}
  - {x: 22, y: 2.0482757}
  - {x: 23, y: 2.0672414}
  - {x: 24, y: 1.01}
  - {x: 25, y: 1.01}
  - {x: 26, y: 1.01}
  - {x: 27, y: 1.01}
  - {x: 28, y: 1.01}
  - {x: 29, y: 1.01}
  - {x: 30, y: 1.01}
  cachedRequirementCurve: 770100003902000069030000720300007b030000840300008d030000960300009f030000a80300001d060000540a0000a1110000ce110000fc1100002a1200005812000087120000b6120000e6120000dd220000eb400000007a0000387b0000737c0000b27d0000f47e00003980000081810000
  cachedRawFormulaCurve: []
  plateauCycle: 10
  climbDuration: 3
  zeroClimbMultiplier: 1.12
  zeroPlateauMultiplier: 1.01
  climbIntensityBase: 1.5
  climbIntensityGrowth: 0.5
  plateauIntensity: 0.6
