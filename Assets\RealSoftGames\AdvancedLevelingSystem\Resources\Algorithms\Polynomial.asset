%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61e11a8ade1670c429ff9a69750d51a2, type: 3}
  m_Name: Polynomial
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 9cd22a4c-2e1c-46a2-9098-1be1a2967f99
  algorithmName: Polynomial
  description: Uses a polynomial with degree 2.5, creating a curve between quadratic
    and cubic growth.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 0.002 * level^2.5))


    Uses a polynomial with degree 2.5, creating a
    curve between quadratic and cubic growth.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 276}
  - {x: 3, y: 307}
  - {x: 4, y: 348}
  - {x: 5, y: 407}
  - {x: 6, y: 498}
  - {x: 7, y: 644}
  - {x: 8, y: 892}
  - {x: 9, y: 1336}
  - {x: 10, y: 2184}
  - {x: 11, y: 3922}
  - {x: 12, y: 7777}
  - {x: 13, y: 17089}
  - {x: 14, y: 41706}
  - {x: 15, y: 113165}
  - {x: 16, y: 341433}
  - {x: 17, y: 1144757}
  - {x: 18, y: 4260180}
  - {x: 19, y: 17569640}
  - {x: 20, y: 80149850}
  - {x: 21, y: 403592930}
  - {x: 22, y: 403592930}
  - {x: 23, y: 403592930}
  - {x: 24, y: 403592930}
  - {x: 25, y: 403592930}
  - {x: 26, y: 403592930}
  - {x: 27, y: 403592930}
  - {x: 28, y: 403592930}
  - {x: 29, y: 403592930}
  - {x: 30, y: 403592930}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1022}
  - {x: 2, y: 1.1124451}
  - {x: 3, y: 1.1342946}
  - {x: 4, y: 1.1704}
  - {x: 5, y: 1.2229838}
  - {x: 6, y: 1.2939996}
  - {x: 7, y: 1.3852121}
  - {x: 8, y: 1.4982426}
  - {x: 9, y: 1.6346}
  - {x: 10, y: 1.7957011}
  - {x: 11, y: 1.9828855}
  - {x: 12, y: 2.1974275}
  - {x: 13, y: 2.4405444}
  - {x: 14, y: 2.713403}
  - {x: 15, y: 3.017127}
  - {x: 16, y: 3.3528001}
  - {x: 17, y: 3.7214706}
  - {x: 18, y: 4.1241546}
  - {x: 19, y: 4.5618377}
  - {x: 20, y: 5.0354795}
  - {x: 21, y: 5.5460153}
  - {x: 22, y: 6.0943546}
  - {x: 23, y: 6.681389}
  - {x: 24, y: 7.307987}
  - {x: 25, y: 7.975001}
  - {x: 26, y: 8.683262}
  - {x: 27, y: 9.43359}
  - {x: 28, y: 10.226784}
  - {x: 29, y: 11.063633}
  - {x: 30, y: 11.944907}
  cachedRequirementCurve: f5010000fb0100000b0200002c0200006a020000d702000093030000de0400003c070000cf0b000049150000852a0000565e0000b4e80000457e020072990700beb519007a64600070c08f0168f2250700490a2401490a2402490a2403490a2404490a2405490a2406490a2407490a2408490a2409490a240a490a240b490a240c490a240d490a240e490a240f490a2410490a2411490a2412490a2413490a2414490a2415490a2416490a2417490a2418490a2419490a241a490a241b490a241c490a24
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  polynomialCoefficient: 0.002
  polynomialPower: 2.5
