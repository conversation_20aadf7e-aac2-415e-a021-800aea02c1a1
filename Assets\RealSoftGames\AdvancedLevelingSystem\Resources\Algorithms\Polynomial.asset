%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61e11a8ade1670c429ff9a69750d51a2, type: 3}
  m_Name: Polynomial
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 9cd22a4c-2e1c-46a2-9098-1be1a2967f99
  algorithmName: Polynomial
  description: Uses a polynomial with degree 2.5, creating a curve between quadratic
    and cubic growth.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 0.002 * level^2.5))


    Uses a polynomial with degree 2.5, creating a
    curve between quadratic and cubic growth.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 276}
  - {x: 3, y: 307}
  - {x: 4, y: 348}
  - {x: 5, y: 407}
  - {x: 6, y: 498}
  - {x: 7, y: 644}
  - {x: 8, y: 892}
  - {x: 9, y: 1336}
  - {x: 10, y: 2184}
  - {x: 11, y: 3922}
  - {x: 12, y: 7777}
  - {x: 13, y: 17089}
  - {x: 14, y: 41706}
  - {x: 15, y: 113165}
  - {x: 16, y: 341433}
  - {x: 17, y: 1144757}
  - {x: 18, y: 4260180}
  - {x: 19, y: 17569640}
  - {x: 20, y: 80149850}
  - {x: 21, y: 403592930}
  - {x: 22, y: 403592930}
  - {x: 23, y: 403592930}
  - {x: 24, y: 403592930}
  - {x: 25, y: 403592930}
  - {x: 26, y: 403592930}
  - {x: 27, y: 403592930}
  - {x: 28, y: 403592930}
  - {x: 29, y: 403592930}
  - {x: 30, y: 403592930}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1022}
  - {x: 2, y: 1.1124451}
  - {x: 3, y: 1.1342946}
  - {x: 4, y: 1.1704}
  - {x: 5, y: 1.2229838}
  - {x: 6, y: 1.2939996}
  - {x: 7, y: 1.3852121}
  - {x: 8, y: 1.4982426}
  - {x: 9, y: 1.6346}
  - {x: 10, y: 1.7957011}
  - {x: 11, y: 1.9828855}
  - {x: 12, y: 2.1974275}
  - {x: 13, y: 2.4405444}
  - {x: 14, y: 2.713403}
  - {x: 15, y: 3.017127}
  - {x: 16, y: 3.3528001}
  - {x: 17, y: 3.7214706}
  - {x: 18, y: 4.1241546}
  - {x: 19, y: 4.5618377}
  - {x: 20, y: 5.0354795}
  - {x: 21, y: 5.5460153}
  - {x: 22, y: 6.0943546}
  - {x: 23, y: 6.681389}
  - {x: 24, y: 7.307987}
  - {x: 25, y: 7.975001}
  - {x: 26, y: 8.683262}
  - {x: 27, y: 9.43359}
  - {x: 28, y: 10.226784}
  - {x: 29, y: 11.063633}
  - {x: 30, y: 11.944907}
  cachedRequirementCurve: fb000000fe0000000601000017010000360100006d010000cc01000073020000a4030000f1050000b60a0000661500007a2f00001d7500003941010016d3030066f00c00f3823000f02ec90074f59803c0512312807c7d64817c7d64827c7d64837c7d64847c7d64857c7d64867c7d64877c7d64
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  polynomialCoefficient: 0.002
  polynomialPower: 2.5
