%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df7b0fa7b7970164fb9553327b68a4c9, type: 3}
  m_Name: Pulsating
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 6346e114-9205-4a2c-8d37-506061c73741
  algorithmName: Pulsating
  description: Creates a pulsating pattern with gradually increasing intensity, like
    a drumbeat that gets stronger over time.
  formulaExplanation: 'Formula: Combines sine waves with increasing amplitude


    Creates
    a pulsating pattern where the intensity of pulses increases as levels progress,
    creating a rhythm that becomes more pronounced over time.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.179
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 303}
  - {x: 4, y: 334}
  - {x: 5, y: 368}
  - {x: 6, y: 404}
  - {x: 7, y: 443}
  - {x: 8, y: 486}
  - {x: 9, y: 534}
  - {x: 10, y: 589}
  - {x: 11, y: 651}
  - {x: 12, y: 718}
  - {x: 13, y: 789}
  - {x: 14, y: 864}
  - {x: 15, y: 946}
  - {x: 16, y: 1038}
  - {x: 17, y: 1145}
  - {x: 18, y: 1267}
  - {x: 19, y: 1401}
  - {x: 20, y: 1542}
  - {x: 21, y: 1688}
  - {x: 22, y: 1844}
  - {x: 23, y: 2020}
  - {x: 24, y: 2226}
  - {x: 25, y: 2465}
  - {x: 26, y: 2731}
  - {x: 27, y: 3012}
  - {x: 28, y: 3299}
  - {x: 29, y: 3598}
  - {x: 30, y: 3931}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.1021025}
  - {x: 3, y: 1.1029775}
  - {x: 4, y: 1.1016889}
  - {x: 5, y: 1.0988715}
  - {x: 6, y: 1.0964763}
  - {x: 7, y: 1.0964203}
  - {x: 8, y: 1.0990734}
  - {x: 9, y: 1.1027651}
  - {x: 10, y: 1.1048205}
  - {x: 11, y: 1.1034979}
  - {x: 12, y: 1.0994221}
  - {x: 13, y: 1.0953621}
  - {x: 14, y: 1.0943516}
  - {x: 15, y: 1.09743}
  - {x: 16, y: 1.1026787}
  - {x: 17, y: 1.106396}
  - {x: 18, y: 1.1057082}
  - {x: 19, y: 1.1007737}
  - {x: 20, y: 1.0949016}
  - {x: 21, y: 1.0923389}
  - {x: 22, y: 1.0952004}
  - {x: 23, y: 1.1017605}
  - {x: 24, y: 1.1074642}
  - {x: 25, y: 1.1080885}
  - {x: 26, y: 1.1028627}
  - {x: 27, y: 1.0952445}
  - {x: 28, y: 1.0906422}
  - {x: 29, y: 1.0925756}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: 270100005c0100009c010000e801000042020000ab02000026030000b40300005904000019050000fa0500000507000045080000c6090000960b0000c20d0000561000005b130000db160000e01a00007d1f0000d0240000082b0000693200004b3b0000144600003053000006630000ec7500001d8c0000c1a5000003c300003fe40000350a01003c3601005b6a010044a901002ff601008c54020099c70200e8510300fdf4030056b1040020870500ac7706005f87070079bf08001c2f0a003beb0b00
  cachedRawFormulaCurve: []
  frequency: 8
  baseAmplitude: 0.05
  amplitudeGrowth: 3
  zeroBaseMultiplier: 1.05
  amplitudeScalingFactor: 0.5
