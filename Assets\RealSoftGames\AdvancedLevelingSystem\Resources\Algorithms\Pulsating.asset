%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df7b0fa7b7970164fb9553327b68a4c9, type: 3}
  m_Name: Pulsating
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 6346e114-9205-4a2c-8d37-506061c73741
  algorithmName: Pulsating
  description: Creates a pulsating pattern with gradually increasing intensity, like
    a drumbeat that gets stronger over time.
  formulaExplanation: 'Formula: Combines sine waves with increasing amplitude


    Creates
    a pulsating pattern where the intensity of pulses increases as levels progress,
    creating a rhythm that becomes more pronounced over time.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  frequency: 8
  baseAmplitude: 0.05
  amplitudeGrowth: 3
  zeroBaseMultiplier: 1.05
  amplitudeScalingFactor: 0.5
