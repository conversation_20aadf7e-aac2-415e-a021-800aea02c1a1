%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3b5b2520fb200642af5197b365c8bf3, type: 3}
  m_Name: Quadratic
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 55356d08-8f38-424d-9e49-3e2d87248fb6
  algorithmName: Quadratic
  description: Experience requirements grow with the square of the level, creating
    a steep curve.
  formulaExplanation: 'Formula: requiredExp = requiredExp * (levelUpMultiplier *
    (1 + 0.005 * level^2))


    Experience requirements grow with the square of
    the level, creating a steep curve.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 276}
  - {x: 3, y: 310}
  - {x: 4, y: 356}
  - {x: 5, y: 423}
  - {x: 6, y: 523}
  - {x: 7, y: 679}
  - {x: 8, y: 930}
  - {x: 9, y: 1350}
  - {x: 10, y: 2086}
  - {x: 11, y: 3442}
  - {x: 12, y: 6077}
  - {x: 13, y: 11498}
  - {x: 14, y: 23335}
  - {x: 15, y: 50824}
  - {x: 16, y: 118801}
  - {x: 17, y: 297953}
  - {x: 18, y: 801345}
  - {x: 19, y: 2309476}
  - {x: 20, y: 7125889}
  - {x: 21, y: 23515436}
  - {x: 22, y: 82903670}
  - {x: 23, y: 311883620}
  - {x: 24, y: 1.2504974e+9}
  - {x: 25, y: 1.2504974e+9}
  - {x: 26, y: 1.2504974e+9}
  - {x: 27, y: 1.2504974e+9}
  - {x: 28, y: 1.2504974e+9}
  - {x: 29, y: 1.2504974e+9}
  - {x: 30, y: 1.2504974e+9}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1055}
  - {x: 2, y: 1.122}
  - {x: 3, y: 1.1495}
  - {x: 4, y: 1.1880001}
  - {x: 5, y: 1.2375001}
  - {x: 6, y: 1.298}
  - {x: 7, y: 1.3695}
  - {x: 8, y: 1.4519999}
  - {x: 9, y: 1.5455}
  - {x: 10, y: 1.6500001}
  - {x: 11, y: 1.7655001}
  - {x: 12, y: 1.8920001}
  - {x: 13, y: 2.0295}
  - {x: 14, y: 2.178}
  - {x: 15, y: 2.3375}
  - {x: 16, y: 2.5080001}
  - {x: 17, y: 2.6895}
  - {x: 18, y: 2.882}
  - {x: 19, y: 3.0855002}
  - {x: 20, y: 3.3000002}
  - {x: 21, y: 3.5255}
  - {x: 22, y: 3.7619998}
  - {x: 23, y: 4.0095}
  - {x: 24, y: 4.268}
  - {x: 25, y: 4.5375}
  - {x: 26, y: 4.8180003}
  - {x: 27, y: 5.1095}
  - {x: 28, y: 5.412}
  - {x: 29, y: 5.7255}
  - {x: 30, y: 6.05}
  cachedRequirementCurve: fb000000000100000c01000021010000450100007f010000dd01000076020000750300003005000053080000510e00006a1a00004d340000246f000067fd0000926b02004657060048c91100d85b3500cc03ab00e4de480280da530880974f2081974f2082974f2083974f2084974f2085974f20
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  quadraticCoefficient: 0.005
