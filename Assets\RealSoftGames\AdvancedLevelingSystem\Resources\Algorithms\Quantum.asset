%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e027542d7ee409f4a930ce575c55258d, type: 3}
  m_Name: Quantum
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: c79f7834-a7f1-41f8-8d58-89710686f92a
  algorithmName: Quantum
  description: Creates a quantum uncertainty pattern with random fluctuations around
    a central trend, like particles in quantum mechanics that behave unpredictably
    yet follow probability laws.
  formulaExplanation: 'Formula: Combines deterministic growth with pseudo-random
    quantum fluctuations


    Creates a quantum uncertainty pattern with random-appearing
    fluctuations around a central trend, like particles in quantum mechanics that
    behave unpredictably yet follow probability laws.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 276}
  - {x: 3, y: 305}
  - {x: 4, y: 337}
  - {x: 5, y: 373}
  - {x: 6, y: 413}
  - {x: 7, y: 458}
  - {x: 8, y: 509}
  - {x: 9, y: 566}
  - {x: 10, y: 630}
  - {x: 11, y: 705}
  - {x: 12, y: 791}
  - {x: 13, y: 888}
  - {x: 14, y: 997}
  - {x: 15, y: 1118}
  - {x: 16, y: 1255}
  - {x: 17, y: 1418}
  - {x: 18, y: 1603}
  - {x: 19, y: 1818}
  - {x: 20, y: 2064}
  - {x: 21, y: 2346}
  - {x: 22, y: 2670}
  - {x: 23, y: 3043}
  - {x: 24, y: 3466}
  - {x: 25, y: 3954}
  - {x: 26, y: 4530}
  - {x: 27, y: 5215}
  - {x: 28, y: 6002}
  - {x: 29, y: 6922}
  - {x: 30, y: 7967}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.102593}
  - {x: 2, y: 1.1038885}
  - {x: 3, y: 1.1057281}
  - {x: 4, y: 1.1065112}
  - {x: 5, y: 1.1061072}
  - {x: 6, y: 1.1087015}
  - {x: 7, y: 1.1104301}
  - {x: 8, y: 1.1122024}
  - {x: 9, y: 1.1132272}
  - {x: 10, y: 1.1190315}
  - {x: 11, y: 1.1222126}
  - {x: 12, y: 1.1223348}
  - {x: 13, y: 1.1230494}
  - {x: 14, y: 1.1216925}
  - {x: 15, y: 1.1224574}
  - {x: 16, y: 1.1300645}
  - {x: 17, y: 1.1306398}
  - {x: 18, y: 1.1339059}
  - {x: 19, y: 1.135355}
  - {x: 20, y: 1.1366866}
  - {x: 21, y: 1.1380451}
  - {x: 22, y: 1.1396873}
  - {x: 23, y: 1.1389818}
  - {x: 24, y: 1.1408763}
  - {x: 25, y: 1.1456656}
  - {x: 26, y: 1.1512314}
  - {x: 27, y: 1.1508579}
  - {x: 28, y: 1.1533328}
  - {x: 29, y: 1.1508958}
  - {x: 30, y: 1.1518874}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000170200001c02000021020000260200002c02000032020000390200004102000049020000520200005c02000066020000710200007d0200008a02000098020000a7020000b7020000c8020000da020000ed02000002030000180300002f03000048030000630300007f0300009d030000bd030000df030000030400002a040000530400007f040000ae040000e0040000150500004e0500008b050000cc050000120600005d060000ad060000
  cachedRawFormulaCurve: []
  baseGrowth: 0.05
  quantumAmplitude: 0.1
  zeroBaseMultiplier: 1.05
  amplitudeScalingFactor: 0.5
  frequencies:
  - 7.3
  - 11.7
  - 19.3
  - 23.1
  - 29.9
  phases:
  - 0.1
  - 0.7
  - 1.3
  - 2.1
  - 2.9
  weights:
  - 0.5
  - 0.3
  - 0.2
  - 0.15
  - 0.1
  normalizationFactor: 1.25
