%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Random
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 93bec3ae-55e9-45f0-8864-13fc0ea94889
  algorithmName: Random
  description: A random progression with unpredictable jumps.
  formulaExplanation: Experience = Previous * (1.1 + 0.05 * random(-1, 1))
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 1
  randomAmplitude: 0.05
  randomSeed: 4418
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 60
  cachedLevelUpMultiplier: 0.92
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 262}
  - {x: 4, y: 263}
  - {x: 5, y: 276}
  - {x: 6, y: 277}
  - {x: 7, y: 291}
  - {x: 8, y: 293}
  - {x: 9, y: 309}
  - {x: 10, y: 313}
  - {x: 11, y: 314}
  - {x: 12, y: 319}
  - {x: 13, y: 320}
  - {x: 14, y: 327}
  - {x: 15, y: 328}
  - {x: 16, y: 336}
  - {x: 17, y: 337}
  - {x: 18, y: 347}
  - {x: 19, y: 348}
  - {x: 20, y: 359}
  - {x: 21, y: 360}
  - {x: 22, y: 373}
  - {x: 23, y: 374}
  - {x: 24, y: 389}
  - {x: 25, y: 390}
  - {x: 26, y: 408}
  - {x: 27, y: 409}
  - {x: 28, y: 429}
  - {x: 29, y: 431}
  - {x: 30, y: 454}
  - {x: 31, y: 458}
  - {x: 32, y: 484}
  - {x: 33, y: 491}
  - {x: 34, y: 492}
  - {x: 35, y: 501}
  - {x: 36, y: 502}
  - {x: 37, y: 513}
  - {x: 38, y: 514}
  - {x: 39, y: 527}
  - {x: 40, y: 528}
  - {x: 41, y: 544}
  - {x: 42, y: 545}
  - {x: 43, y: 564}
  - {x: 44, y: 565}
  - {x: 45, y: 587}
  - {x: 46, y: 588}
  - {x: 47, y: 613}
  - {x: 48, y: 614}
  - {x: 49, y: 643}
  - {x: 50, y: 645}
  - {x: 51, y: 678}
  - {x: 52, y: 683}
  - {x: 53, y: 721}
  - {x: 54, y: 729}
  - {x: 55, y: 730}
  - {x: 56, y: 741}
  - {x: 57, y: 742}
  - {x: 58, y: 756}
  - {x: 59, y: 757}
  - {x: 60, y: 775}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.995781}
  - {x: 2, y: 1.0438442}
  - {x: 3, y: 0.9999073}
  - {x: 4, y: 1.0479704}
  - {x: 5, y: 1.0040336}
  - {x: 6, y: 1.0520966}
  - {x: 7, y: 1.0081599}
  - {x: 8, y: 1.0562229}
  - {x: 9, y: 1.0122861}
  - {x: 10, y: 0.96834916}
  - {x: 11, y: 1.0164124}
  - {x: 12, y: 0.97247547}
  - {x: 13, y: 1.0205386}
  - {x: 14, y: 0.97660166}
  - {x: 15, y: 1.0246649}
  - {x: 16, y: 0.98072803}
  - {x: 17, y: 1.0287911}
  - {x: 18, y: 0.9848542}
  - {x: 19, y: 1.0329174}
  - {x: 20, y: 0.98898053}
  - {x: 21, y: 1.0370437}
  - {x: 22, y: 0.9931067}
  - {x: 23, y: 1.0411699}
  - {x: 24, y: 0.9972331}
  - {x: 25, y: 1.0452962}
  - {x: 26, y: 1.0013592}
  - {x: 27, y: 1.0494224}
  - {x: 28, y: 1.0054855}
  - {x: 29, y: 1.0535487}
  - {x: 30, y: 1.0096118}
  - {x: 31, y: 1.0576749}
  - {x: 32, y: 1.013738}
  - {x: 33, y: 0.96980125}
  - {x: 34, y: 1.0178643}
  - {x: 35, y: 0.97392744}
  - {x: 36, y: 1.0219905}
  - {x: 37, y: 0.9780536}
  - {x: 38, y: 1.0261168}
  - {x: 39, y: 0.98217994}
  - {x: 40, y: 1.030243}
  - {x: 41, y: 0.9863062}
  - {x: 42, y: 1.0343693}
  - {x: 43, y: 0.9904325}
  - {x: 44, y: 1.0384957}
  - {x: 45, y: 0.9945587}
  - {x: 46, y: 1.0426219}
  - {x: 47, y: 0.998685}
  - {x: 48, y: 1.0467482}
  - {x: 49, y: 1.0028112}
  - {x: 50, y: 1.0508744}
  - {x: 51, y: 1.0069375}
  - {x: 52, y: 1.0550007}
  - {x: 53, y: 1.0110638}
  - {x: 54, y: 0.96712685}
  - {x: 55, y: 1.01519}
  - {x: 56, y: 0.97125316}
  - {x: 57, y: 1.0193163}
  - {x: 58, y: 0.9753794}
  - {x: 59, y: 1.0234425}
  - {x: 60, y: 0.9795057}
  cachedRequirementCurve: fb00000006010000070100001401000015010000230100002501000035010000390100003a0100003f01000040010000470100004801000050010000510100005b0100005c0100006701000068010000750100007601000085010000860100009801000099010000ad010000af010000c6010000ca010000e4010000eb010000ec010000f5010000f601000001020000020200000f02000010020000200200002102000034020000350200004b0200004c02000065020000660200008302000085020000a6020000ab020000d1020000d9020000da020000e5020000e6020000f4020000f502000007030000
  cachedRawFormulaCurve: []
