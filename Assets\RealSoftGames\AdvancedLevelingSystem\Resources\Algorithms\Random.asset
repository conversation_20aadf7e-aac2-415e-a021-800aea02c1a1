%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Random
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 93bec3ae-55e9-45f0-8864-13fc0ea94889
  algorithmName: Random
  description: A random progression with unpredictable jumps.
  formulaExplanation: Experience = Previous * (1.1 + 0.05 * random(-1, 1))
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 1
  randomAmplitude: 0.05
  randomSeed: 4418
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 298}
  - {x: 3, y: 372}
  - {x: 4, y: 445}
  - {x: 5, y: 558}
  - {x: 6, y: 670}
  - {x: 7, y: 843}
  - {x: 8, y: 1016}
  - {x: 9, y: 1283}
  - {x: 10, y: 1553}
  - {x: 11, y: 1798}
  - {x: 12, y: 2185}
  - {x: 13, y: 2541}
  - {x: 14, y: 3101}
  - {x: 15, y: 3621}
  - {x: 16, y: 4436}
  - {x: 17, y: 5202}
  - {x: 18, y: 6399}
  - {x: 19, y: 7535}
  - {x: 20, y: 9306}
  - {x: 21, y: 11004}
  - {x: 22, y: 13644}
  - {x: 23, y: 16201}
  - {x: 24, y: 20168}
  - {x: 25, y: 24047}
  - {x: 26, y: 30054}
  - {x: 27, y: 35983}
  - {x: 28, y: 45149}
  - {x: 29, y: 54279}
  - {x: 30, y: 68374}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1906078}
  - {x: 2, y: 1.2480747}
  - {x: 3, y: 1.1955414}
  - {x: 4, y: 1.2530081}
  - {x: 5, y: 1.2004749}
  - {x: 6, y: 1.2579416}
  - {x: 7, y: 1.2054086}
  - {x: 8, y: 1.2628753}
  - {x: 9, y: 1.210342}
  - {x: 10, y: 1.1578088}
  - {x: 11, y: 1.2152756}
  - {x: 12, y: 1.1627424}
  - {x: 13, y: 1.2202092}
  - {x: 14, y: 1.167676}
  - {x: 15, y: 1.2251428}
  - {x: 16, y: 1.1726096}
  - {x: 17, y: 1.2300763}
  - {x: 18, y: 1.177543}
  - {x: 19, y: 1.2350099}
  - {x: 20, y: 1.1824768}
  - {x: 21, y: 1.2399435}
  - {x: 22, y: 1.1874102}
  - {x: 23, y: 1.244877}
  - {x: 24, y: 1.192344}
  - {x: 25, y: 1.2498107}
  - {x: 26, y: 1.1972774}
  - {x: 27, y: 1.2547442}
  - {x: 28, y: 1.2022109}
  - {x: 29, y: 1.2596778}
  - {x: 30, y: 1.2071446}
  cachedRequirementCurve: 1d020000660200009b020000f80200003d030000b40300000f040000a90400002105000066050000f70500004e060000fe0600006c07000044080000d0080000db0900008d0a0000d90b0000bc0c00005b0e00007f0f000089110000021300009915000082170000d11a00004f1d000090210000d5240000582a0000a82e00002f3100006a3600009b390000fe3f000008440000e14b000002510000b75a000041610000586d0000b7750000e0840000a58f0000caa20000b6b000000ec9000027db0000
  cachedRawFormulaCurve: []
