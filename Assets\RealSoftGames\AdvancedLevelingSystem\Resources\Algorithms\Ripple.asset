%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dfffd2f8420675647a573f1aeb18f0fe, type: 3}
  m_Name: Ripple
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 072ffa4f-45fa-4a89-91b1-eecd68f1cda6
  algorithmName: Ripple
  description: Creates a ripple effect with multiple overlapping waves of different
    frequencies, like dropping multiple stones in a pond.
  formulaExplanation: 'Formula: Combines multiple sine waves with different frequencies


    Creates
    a complex pattern by overlaying multiple wave patterns with different frequencies
    and amplitudes, creating a ripple-like effect that varies throughout the progression.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  amplitudeScalingFactor: 0.5
  frequencies:
  - 3
  - 7
  - 11
  amplitudes:
  - 0.05
  - 0.03
  - 0.02
  phases:
  - 0
  - 0.33
  - 0.67
  amplitudeGrowthFactor: 2
