%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dfffd2f8420675647a573f1aeb18f0fe, type: 3}
  m_Name: Ripple
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 072ffa4f-45fa-4a89-91b1-eecd68f1cda6
  algorithmName: Ripple
  description: Creates a ripple effect with multiple overlapping waves of different
    frequencies, like dropping multiple stones in a pond.
  formulaExplanation: 'Formula: Combines multiple sine waves with different frequencies


    Creates
    a complex pattern by overlaying multiple wave patterns with different frequencies
    and amplitudes, creating a ripple-like effect that varies throughout the progression.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 276}
  - {x: 3, y: 304}
  - {x: 4, y: 335}
  - {x: 5, y: 369}
  - {x: 6, y: 407}
  - {x: 7, y: 449}
  - {x: 8, y: 495}
  - {x: 9, y: 545}
  - {x: 10, y: 601}
  - {x: 11, y: 663}
  - {x: 12, y: 731}
  - {x: 13, y: 803}
  - {x: 14, y: 878}
  - {x: 15, y: 958}
  - {x: 16, y: 1047}
  - {x: 17, y: 1148}
  - {x: 18, y: 1261}
  - {x: 19, y: 1385}
  - {x: 20, y: 1521}
  - {x: 21, y: 1672}
  - {x: 22, y: 1841}
  - {x: 23, y: 2029}
  - {x: 24, y: 2234}
  - {x: 25, y: 2462}
  - {x: 26, y: 2724}
  - {x: 27, y: 3028}
  - {x: 28, y: 3369}
  - {x: 29, y: 3730}
  - {x: 30, y: 4099}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1021519}
  - {x: 2, y: 1.1022516}
  - {x: 3, y: 1.1015594}
  - {x: 4, y: 1.1015061}
  - {x: 5, y: 1.1022491}
  - {x: 6, y: 1.102669}
  - {x: 7, y: 1.1021621}
  - {x: 8, y: 1.1016978}
  - {x: 9, y: 1.1024595}
  - {x: 10, y: 1.1037238}
  - {x: 11, y: 1.1030483}
  - {x: 12, y: 1.0991123}
  - {x: 13, y: 1.0938948}
  - {x: 14, y: 1.0911788}
  - {x: 15, y: 1.0926962}
  - {x: 16, y: 1.0963498}
  - {x: 17, y: 1.0986824}
  - {x: 18, y: 1.098664}
  - {x: 19, y: 1.0982735}
  - {x: 20, y: 1.0994059}
  - {x: 21, y: 1.1012981}
  - {x: 22, y: 1.1018963}
  - {x: 23, y: 1.1012644}
  - {x: 24, y: 1.1021533}
  - {x: 25, y: 1.1063962}
  - {x: 26, y: 1.1115413}
  - {x: 27, y: 1.1125185}
  - {x: 28, y: 1.1070988}
  - {x: 29, y: 1.0988116}
  - {x: 30, y: 1.0935445}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000170200001c02000021020000260200002c02000032020000380200003e020000440200004a02000050020000560200005c02000062020000680200006e020000740200007a02000080020000860200008c020000930200009a020000a1020000a8020000af020000b6020000bd020000c4020000cb020000d2020000d9020000e0020000e7020000ee020000f6020000fe020000060300000e030000160300001e030000260300002e030000
  cachedRawFormulaCurve: []
  zeroBaseMultiplier: 1.05
  amplitudeScalingFactor: 0.5
  frequencies:
  - 3
  - 7
  - 11
  amplitudes:
  - 0.05
  - 0.03
  - 0.02
  phases:
  - 0
  - 0.33
  - 0.67
  amplitudeGrowthFactor: 2
