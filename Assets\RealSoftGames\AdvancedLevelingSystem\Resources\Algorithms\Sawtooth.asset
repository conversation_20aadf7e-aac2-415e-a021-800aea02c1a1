%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3434e5832742518448d3dfde26952fdd, type: 3}
  m_Name: Sawtooth
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 91388fbd-0242-422c-a258-36af1d863c06
  algorithmName: Sawtooth
  description: Creates a sawtooth pattern where difficulty increases linearly then
    drops, repeating in cycles.
  formulaExplanation: 'Formula: Uses cyclical pattern with reset


    Creates a
    sawtooth pattern where difficulty increases linearly then drops, repeating in
    cycles.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 262}
  - {x: 4, y: 288}
  - {x: 5, y: 333}
  - {x: 6, y: 403}
  - {x: 7, y: 404}
  - {x: 8, y: 422}
  - {x: 9, y: 464}
  - {x: 10, y: 536}
  - {x: 11, y: 649}
  - {x: 12, y: 650}
  - {x: 13, y: 679}
  - {x: 14, y: 747}
  - {x: 15, y: 863}
  - {x: 16, y: 1044}
  - {x: 17, y: 1045}
  - {x: 18, y: 1092}
  - {x: 19, y: 1201}
  - {x: 20, y: 1387}
  - {x: 21, y: 1678}
  - {x: 22, y: 1679}
  - {x: 23, y: 1755}
  - {x: 24, y: 1930}
  - {x: 25, y: 2229}
  - {x: 26, y: 2697}
  - {x: 27, y: 2698}
  - {x: 28, y: 2819}
  - {x: 29, y: 3101}
  - {x: 30, y: 3582}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.99}
  - {x: 2, y: 1.0450001}
  - {x: 3, y: 1.1}
  - {x: 4, y: 1.155}
  - {x: 5, y: 1.21}
  - {x: 6, y: 0.99}
  - {x: 7, y: 1.0450001}
  - {x: 8, y: 1.1}
  - {x: 9, y: 1.155}
  - {x: 10, y: 1.21}
  - {x: 11, y: 0.99}
  - {x: 12, y: 1.0450001}
  - {x: 13, y: 1.1}
  - {x: 14, y: 1.155}
  - {x: 15, y: 1.21}
  - {x: 16, y: 0.99}
  - {x: 17, y: 1.0450001}
  - {x: 18, y: 1.1}
  - {x: 19, y: 1.155}
  - {x: 20, y: 1.21}
  - {x: 21, y: 0.99}
  - {x: 22, y: 1.0450001}
  - {x: 23, y: 1.1}
  - {x: 24, y: 1.155}
  - {x: 25, y: 1.21}
  - {x: 26, y: 0.99}
  - {x: 27, y: 1.0450001}
  - {x: 28, y: 1.1}
  - {x: 29, y: 1.155}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: fb000000fc000000fd0000000a0100002501000026010000270100002801000037010000560100005701000058010000590100006a0100008e0100008f0100009001000091010000a5010000cf010000d0010000d1010000d2010000e90100001a0200001b0200001c0200001d02000038020000
  cachedRawFormulaCurve: []
  cycleLength: 5
  minMultiplier: 0.9
  maxMultiplier: 1.1
  zeroMultiplierMin: 1.02
  zeroMultiplierMax: 1.1
