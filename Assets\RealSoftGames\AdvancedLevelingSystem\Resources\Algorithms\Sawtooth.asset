%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3434e5832742518448d3dfde26952fdd, type: 3}
  m_Name: Sawtooth
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 91388fbd-0242-422c-a258-36af1d863c06
  algorithmName: Sawtooth
  description: Creates a sawtooth pattern where difficulty increases linearly then
    drops, repeating in cycles.
  formulaExplanation: 'Formula: Uses cyclical pattern with reset


    Creates a
    sawtooth pattern where difficulty increases linearly then drops, repeating in
    cycles.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 262}
  - {x: 4, y: 288}
  - {x: 5, y: 333}
  - {x: 6, y: 403}
  - {x: 7, y: 404}
  - {x: 8, y: 422}
  - {x: 9, y: 464}
  - {x: 10, y: 536}
  - {x: 11, y: 649}
  - {x: 12, y: 650}
  - {x: 13, y: 679}
  - {x: 14, y: 747}
  - {x: 15, y: 863}
  - {x: 16, y: 1044}
  - {x: 17, y: 1045}
  - {x: 18, y: 1092}
  - {x: 19, y: 1201}
  - {x: 20, y: 1387}
  - {x: 21, y: 1678}
  - {x: 22, y: 1679}
  - {x: 23, y: 1755}
  - {x: 24, y: 1930}
  - {x: 25, y: 2229}
  - {x: 26, y: 2697}
  - {x: 27, y: 2698}
  - {x: 28, y: 2819}
  - {x: 29, y: 3101}
  - {x: 30, y: 3582}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.99}
  - {x: 2, y: 1.0450001}
  - {x: 3, y: 1.1}
  - {x: 4, y: 1.155}
  - {x: 5, y: 1.21}
  - {x: 6, y: 0.99}
  - {x: 7, y: 1.0450001}
  - {x: 8, y: 1.1}
  - {x: 9, y: 1.155}
  - {x: 10, y: 1.21}
  - {x: 11, y: 0.99}
  - {x: 12, y: 1.0450001}
  - {x: 13, y: 1.1}
  - {x: 14, y: 1.155}
  - {x: 15, y: 1.21}
  - {x: 16, y: 0.99}
  - {x: 17, y: 1.0450001}
  - {x: 18, y: 1.1}
  - {x: 19, y: 1.155}
  - {x: 20, y: 1.21}
  - {x: 21, y: 0.99}
  - {x: 22, y: 1.0450001}
  - {x: 23, y: 1.1}
  - {x: 24, y: 1.155}
  - {x: 25, y: 1.21}
  - {x: 26, y: 0.99}
  - {x: 27, y: 1.0450001}
  - {x: 28, y: 1.1}
  - {x: 29, y: 1.155}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: f5010000f6010000f7010000100200004502000046020000470200004802000065020000a2020000a3020000a4020000a5020000c70200000e0300000f0300001003000011030000380300008a0300008b0300008c0300008d030000ba030000190400001a0400001b0400001c04000051040000c0040000c1040000c2040000c30400000005000080050000810500008205000083050000ca0500005e0600005f0600006006000061060000b30600005e0700005f0700006007000061070000bf070000
  cachedRawFormulaCurve: []
  cycleLength: 5
  minMultiplier: 0.9
  maxMultiplier: 1.1
  zeroMultiplierMin: 1.02
  zeroMultiplierMax: 1.1
