%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3434e5832742518448d3dfde26952fdd, type: 3}
  m_Name: Sawtooth
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 91388fbd-0242-422c-a258-36af1d863c06
  algorithmName: Sawtooth
  description: Creates a sawtooth pattern where difficulty increases linearly then
    drops, repeating in cycles.
  formulaExplanation: 'Formula: Uses cyclical pattern with reset


    Creates a
    sawtooth pattern where difficulty increases linearly then drops, repeating in
    cycles.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  cycleLength: 5
  minMultiplier: 0.9
  maxMultiplier: 1.1
  zeroMultiplierMin: 1.02
  zeroMultiplierMax: 1.1
