%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8ae21ecd4df2f344881ffa0c96b92dd7, type: 3}
  m_Name: Seasonal
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 9931ec25-5d67-4780-b382-1202b70ed9d5
  algorithmName: Seasonal
  description: Cycles between easy and hard periods, creating a rhythm to progression
    with periods of relief.
  formulaExplanation: 'Formula: Alternates between easy and hard multipliers based
    on level


    Cycles between easy and hard periods, creating a rhythm to progression
    with periods of relief.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  seasonLength: 4
  zeroEasySeasonMultiplier: 1.03
  zeroHardSeasonMultiplier: 1.08
  easySeasonScalingFactor: 0.9
  hardSeasonScalingFactor: 1.1
