%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8ae21ecd4df2f344881ffa0c96b92dd7, type: 3}
  m_Name: Seasonal
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 9931ec25-5d67-4780-b382-1202b70ed9d5
  algorithmName: Seasonal
  description: Cycles between easy and hard periods, creating a rhythm to progression
    with periods of relief.
  formulaExplanation: 'Formula: Alternates between easy and hard multipliers based
    on level


    Cycles between easy and hard periods, creating a rhythm to progression
    with periods of relief.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 305}
  - {x: 5, y: 369}
  - {x: 6, y: 370}
  - {x: 7, y: 371}
  - {x: 8, y: 449}
  - {x: 9, y: 543}
  - {x: 10, y: 544}
  - {x: 11, y: 545}
  - {x: 12, y: 659}
  - {x: 13, y: 797}
  - {x: 14, y: 798}
  - {x: 15, y: 799}
  - {x: 16, y: 967}
  - {x: 17, y: 1170}
  - {x: 18, y: 1171}
  - {x: 19, y: 1172}
  - {x: 20, y: 1418}
  - {x: 21, y: 1716}
  - {x: 22, y: 1717}
  - {x: 23, y: 1718}
  - {x: 24, y: 2079}
  - {x: 25, y: 2516}
  - {x: 26, y: 2517}
  - {x: 27, y: 2518}
  - {x: 28, y: 3047}
  - {x: 29, y: 3687}
  - {x: 30, y: 3688}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.99}
  - {x: 2, y: 0.99}
  - {x: 3, y: 1.21}
  - {x: 4, y: 1.21}
  - {x: 5, y: 0.99}
  - {x: 6, y: 0.99}
  - {x: 7, y: 1.21}
  - {x: 8, y: 1.21}
  - {x: 9, y: 0.99}
  - {x: 10, y: 0.99}
  - {x: 11, y: 1.21}
  - {x: 12, y: 1.21}
  - {x: 13, y: 0.99}
  - {x: 14, y: 0.99}
  - {x: 15, y: 1.21}
  - {x: 16, y: 1.21}
  - {x: 17, y: 0.99}
  - {x: 18, y: 0.99}
  - {x: 19, y: 1.21}
  - {x: 20, y: 1.21}
  - {x: 21, y: 0.99}
  - {x: 22, y: 0.99}
  - {x: 23, y: 1.21}
  - {x: 24, y: 1.21}
  - {x: 25, y: 0.99}
  - {x: 26, y: 0.99}
  - {x: 27, y: 1.21}
  - {x: 28, y: 1.21}
  - {x: 29, y: 0.99}
  - {x: 30, y: 0.99}
  cachedRequirementCurve: f5010000f6010000280200005f02000060020000610200009e020000e1020000e2020000e30200002d0300007e0300007f03000080030000da0300003d0400003e0400003f040000ac040000240500002505000026050000aa0500003b0600003c0600003d060000dd0600008d0700008e0700008f07000050080000250900002609000027090000110a0000130b0000140b0000150b0000310c0000690d00006a0d00006b0d0000c20e00003c1000003d1000003e100000de110000a7130000a8130000
  cachedRawFormulaCurve: []
  seasonLength: 4
  zeroEasySeasonMultiplier: 1.03
  zeroHardSeasonMultiplier: 1.08
  easySeasonScalingFactor: 0.9
  hardSeasonScalingFactor: 1.1
