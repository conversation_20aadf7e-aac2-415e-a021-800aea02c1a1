%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Sinusoidal
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: ff877ab8-bd0a-45f6-8eeb-337aa9cad95a
  algorithmName: Sinusoidal
  description: A wave-like progression with alternating easier and harder levels.
  formulaExplanation: "Experience = Previous * (1.1 + 0.1 * sin(Level * \u03C0/5))"
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 1
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 319}
  - {x: 3, y: 419}
  - {x: 4, y: 551}
  - {x: 5, y: 702}
  - {x: 6, y: 849}
  - {x: 7, y: 972}
  - {x: 8, y: 1074}
  - {x: 9, y: 1187}
  - {x: 10, y: 1360}
  - {x: 11, y: 1646}
  - {x: 12, y: 2098}
  - {x: 13, y: 2758}
  - {x: 14, y: 3626}
  - {x: 15, y: 4622}
  - {x: 16, y: 5593}
  - {x: 17, y: 6406}
  - {x: 18, y: 7081}
  - {x: 19, y: 7827}
  - {x: 20, y: 8965}
  - {x: 21, y: 10848}
  - {x: 22, y: 13827}
  - {x: 23, y: 18177}
  - {x: 24, y: 23896}
  - {x: 25, y: 30459}
  - {x: 26, y: 36855}
  - {x: 27, y: 42212}
  - {x: 28, y: 46660}
  - {x: 29, y: 51577}
  - {x: 30, y: 59073}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.2746564}
  - {x: 2, y: 1.3146163}
  - {x: 3, y: 1.3146163}
  - {x: 4, y: 1.2746564}
  - {x: 5, y: 1.21}
  - {x: 6, y: 1.1453437}
  - {x: 7, y: 1.1053839}
  - {x: 8, y: 1.1053839}
  - {x: 9, y: 1.1453437}
  - {x: 10, y: 1.21}
  - {x: 11, y: 1.2746564}
  - {x: 12, y: 1.3146163}
  - {x: 13, y: 1.3146163}
  - {x: 14, y: 1.2746564}
  - {x: 15, y: 1.21}
  - {x: 16, y: 1.1453437}
  - {x: 17, y: 1.1053839}
  - {x: 18, y: 1.1053839}
  - {x: 19, y: 1.1453437}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.2746564}
  - {x: 22, y: 1.3146163}
  - {x: 23, y: 1.3146163}
  - {x: 24, y: 1.2746564}
  - {x: 25, y: 1.2099999}
  - {x: 26, y: 1.1453435}
  - {x: 27, y: 1.1053839}
  - {x: 28, y: 1.1053839}
  - {x: 29, y: 1.1453437}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: 220100005b0100009f010000e101000011020000270200002a0200002d020000440200007e020000e3020000730300001f040000c704000041050000780500007f05000086050000c00500005306000054070000c2080000770a0000200c0000560d0000e30d0000f40d0000050e0000990e0000
  cachedRawFormulaCurve: []
