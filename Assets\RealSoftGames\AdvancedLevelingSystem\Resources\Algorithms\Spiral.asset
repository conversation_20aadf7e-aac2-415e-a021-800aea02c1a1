%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cd6ea70b1398a9f4f891dfc79764934f, type: 3}
  m_Name: Spiral
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 2d25b914-20b3-4fe0-b185-21e781d596e4
  algorithmName: Spiral
  description: Creates a spiral pattern with gradually increasing oscillations, like
    a spiral staircase that gets steeper as you climb.
  formulaExplanation: 'Formula: Combines exponential growth with increasing oscillations


    Creates
    a pattern that spirals upward with increasing intensity, combining both overall
    growth and oscillating difficulty to create a dynamic progression.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 303}
  - {x: 4, y: 335}
  - {x: 5, y: 371}
  - {x: 6, y: 412}
  - {x: 7, y: 459}
  - {x: 8, y: 512}
  - {x: 9, y: 572}
  - {x: 10, y: 640}
  - {x: 11, y: 717}
  - {x: 12, y: 804}
  - {x: 13, y: 902}
  - {x: 14, y: 1013}
  - {x: 15, y: 1139}
  - {x: 16, y: 1284}
  - {x: 17, y: 1452}
  - {x: 18, y: 1648}
  - {x: 19, y: 1879}
  - {x: 20, y: 2152}
  - {x: 21, y: 2473}
  - {x: 22, y: 2847}
  - {x: 23, y: 3277}
  - {x: 24, y: 3766}
  - {x: 25, y: 4320}
  - {x: 26, y: 4951}
  - {x: 27, y: 5683}
  - {x: 28, y: 6554}
  - {x: 29, y: 7612}
  - {x: 30, y: 8904}
  - {x: 31, y: 10467}
  - {x: 32, y: 12311}
  - {x: 33, y: 14429}
  - {x: 34, y: 16820}
  - {x: 35, y: 19546}
  - {x: 36, y: 22772}
  - {x: 37, y: 26760}
  - {x: 38, y: 31799}
  - {x: 39, y: 38080}
  - {x: 40, y: 45597}
  - {x: 41, y: 54184}
  - {x: 42, y: 63805}
  - {x: 43, y: 74969}
  - {x: 44, y: 88874}
  - {x: 45, y: 107003}
  - {x: 46, y: 130296}
  - {x: 47, y: 158434}
  - {x: 48, y: 190193}
  - {x: 49, y: 225564}
  - {x: 50, y: 268106}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.1026852}
  - {x: 3, y: 1.1054622}
  - {x: 4, y: 1.1082813}
  - {x: 5, y: 1.1110649}
  - {x: 6, y: 1.1137106}
  - {x: 7, y: 1.1161023}
  - {x: 8, y: 1.1181309}
  - {x: 9, y: 1.1197236}
  - {x: 10, y: 1.120877}
  - {x: 11, y: 1.1216886}
  - {x: 12, y: 1.1223727}
  - {x: 13, y: 1.1232497}
  - {x: 14, y: 1.1246959}
  - {x: 15, y: 1.1270549}
  - {x: 16, y: 1.130518}
  - {x: 17, y: 1.1350058}
  - {x: 18, y: 1.1400965}
  - {x: 19, y: 1.1450498}
  - {x: 20, y: 1.1489632}
  - {x: 21, y: 1.1510558}
  - {x: 22, y: 1.1510122}
  - {x: 23, y: 1.1492608}
  - {x: 24, y: 1.1470268}
  - {x: 25, y: 1.1460415}
  - {x: 26, y: 1.1479069}
  - {x: 27, y: 1.1533015}
  - {x: 28, y: 1.161384}
  - {x: 29, y: 1.1697962}
  - {x: 30, y: 1.1754919}
  - {x: 31, y: 1.1762197}
  - {x: 32, y: 1.1720076}
  - {x: 33, y: 1.1657299}
  - {x: 34, y: 1.162073}
  - {x: 35, y: 1.1650305}
  - {x: 36, y: 1.1751355}
  - {x: 37, y: 1.1882857}
  - {x: 38, y: 1.1975253}
  - {x: 39, y: 1.1973965}
  - {x: 40, y: 1.1883229}
  - {x: 41, y: 1.1775692}
  - {x: 42, y: 1.1749728}
  - {x: 43, y: 1.1854789}
  - {x: 44, y: 1.2039804}
  - {x: 45, y: 1.2176857}
  - {x: 46, y: 1.2159555}
  - {x: 47, y: 1.2004575}
  - {x: 48, y: 1.185975}
  - {x: 49, y: 1.1886027}
  - {x: 50, y: 1.21}
  cachedRequirementCurve: 130100002f0100004f010000730100009c010000cb010000000200003c02000080020000cd0200002403000086030000f50300007304000004050000ac050000700600005707000068080000a90900001f0b0000cd0c0000b60e0000e010000057130000331600009a190000bc1d0000c8220000e3280000173000005d380000b44100005a4c0000f458000088680000377c0000c09400001db20000a8d300003df90000d92401002a5b0100fba10100f8fc0100e26a0200f1e602001c7103004a170400
  cachedRawFormulaCurve: []
  baseFrequency: 4
  frequencyGrowth: 6
  baseAmplitude: 0.03
  amplitudeGrowth: 5
  zeroBaseMultiplier: 1.05
  zeroGrowthComponent: 0.05
  amplitudeScalingFactor: 0.5
  growthScalingFactor: 0.1
  exponentialBase: 1.1
  exponentialScaling: 10
