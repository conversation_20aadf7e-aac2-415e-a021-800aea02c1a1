%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cd6ea70b1398a9f4f891dfc79764934f, type: 3}
  m_Name: Spiral
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 2d25b914-20b3-4fe0-b185-21e781d596e4
  algorithmName: Spiral
  description: Creates a spiral pattern with gradually increasing oscillations, like
    a spiral staircase that gets steeper as you climb.
  formulaExplanation: 'Formula: Combines exponential growth with increasing oscillations


    Creates
    a pattern that spirals upward with increasing intensity, combining both overall
    growth and oscillating difficulty to create a dynamic progression.'
  difficultyRating: {fileID: -8535638344401531483, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 304}
  - {x: 4, y: 337}
  - {x: 5, y: 375}
  - {x: 6, y: 419}
  - {x: 7, y: 469}
  - {x: 8, y: 526}
  - {x: 9, y: 591}
  - {x: 10, y: 665}
  - {x: 11, y: 752}
  - {x: 12, y: 857}
  - {x: 13, y: 983}
  - {x: 14, y: 1132}
  - {x: 15, y: 1301}
  - {x: 16, y: 1491}
  - {x: 17, y: 1714}
  - {x: 18, y: 1991}
  - {x: 19, y: 2338}
  - {x: 20, y: 2747}
  - {x: 21, y: 3201}
  - {x: 22, y: 3725}
  - {x: 23, y: 4401}
  - {x: 24, y: 5274}
  - {x: 25, y: 6276}
  - {x: 26, y: 7372}
  - {x: 27, y: 8770}
  - {x: 28, y: 10675}
  - {x: 29, y: 12887}
  - {x: 30, y: 15264}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.104593}
  - {x: 3, y: 1.1093464}
  - {x: 4, y: 1.113885}
  - {x: 5, y: 1.1176794}
  - {x: 6, y: 1.1202917}
  - {x: 7, y: 1.1217843}
  - {x: 8, y: 1.1230685}
  - {x: 9, y: 1.125785}
  - {x: 10, y: 1.131371}
  - {x: 11, y: 1.1395617}
  - {x: 12, y: 1.1475269}
  - {x: 13, y: 1.151254}
  - {x: 14, y: 1.1493382}
  - {x: 15, y: 1.1461289}
  - {x: 16, y: 1.1493758}
  - {x: 17, y: 1.1616856}
  - {x: 18, y: 1.1743524}
  - {x: 19, y: 1.1749731}
  - {x: 20, y: 1.165146}
  - {x: 21, y: 1.1637809}
  - {x: 22, y: 1.1815082}
  - {x: 23, y: 1.1982526}
  - {x: 24, y: 1.1898952}
  - {x: 25, y: 1.1746103}
  - {x: 26, y: 1.1896237}
  - {x: 27, y: 1.2171654}
  - {x: 28, y: 1.2072177}
  - {x: 29, y: 1.184482}
  - {x: 30, y: 1.21}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000180200002002000029020000330200003e0200004b02000059020000690200007b0200008e020000a3020000ba020000d4020000f00200000f03000031030000560300007e030000aa030000da0300000e0400004704000086040000cb0400001605000068050000c205000025060000920600000a0700008e07000020080000c108000073090000380a0000130b0000060c0000140d0000410e0000900f000006110000a81200007c140000
  cachedRawFormulaCurve: []
  baseFrequency: 4
  frequencyGrowth: 6
  baseAmplitude: 0.03
  amplitudeGrowth: 5
  zeroBaseMultiplier: 1.05
  zeroGrowthComponent: 0.05
  amplitudeScalingFactor: 0.5
  growthScalingFactor: 0.1
  exponentialBase: 1.1
  exponentialScaling: 10
