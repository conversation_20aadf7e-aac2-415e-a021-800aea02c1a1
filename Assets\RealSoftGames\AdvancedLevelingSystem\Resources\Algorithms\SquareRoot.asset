%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b102b532e2718246b781e01b8a9eee6, type: 3}
  m_Name: SquareRoot
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: ca76363f-92c6-4b9f-b8c7-ead52663a287
  algorithmName: Square Root
  description: Uses square root scaling to create a moderate progression curve that
    slows down at higher levels.
  formulaExplanation: 'Formula: requiredExp = requiredExp * Mathf.Sqrt(level * levelUpMultiplier)


    Uses
    square root scaling to create a moderate progression curve that slows down at
    higher levels.'
  difficultyRating: {fileID: 4327957321709331361, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 262}
  - {x: 3, y: 389}
  - {x: 4, y: 707}
  - {x: 5, y: 1483}
  - {x: 6, y: 3478}
  - {x: 7, y: 8935}
  - {x: 8, y: 24794}
  - {x: 9, y: 73551}
  - {x: 10, y: 231423}
  - {x: 11, y: 767543}
  - {x: 12, y: 2669902}
  - {x: 13, y: 9700236}
  - {x: 14, y: 36681776}
  - {x: 15, y: 143949700}
  - {x: 16, y: 584726460}
  - {x: 17, y: 584726460}
  - {x: 18, y: 584726460}
  - {x: 19, y: 584726460}
  - {x: 20, y: 584726460}
  - {x: 21, y: 584726460}
  - {x: 22, y: 584726460}
  - {x: 23, y: 584726460}
  - {x: 24, y: 584726460}
  - {x: 25, y: 584726460}
  - {x: 26, y: 584726460}
  - {x: 27, y: 584726460}
  - {x: 28, y: 584726460}
  - {x: 29, y: 584726460}
  - {x: 30, y: 584726460}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0488088}
  - {x: 2, y: 1.4832398}
  - {x: 3, y: 1.8165903}
  - {x: 4, y: 2.0976176}
  - {x: 5, y: 2.345208}
  - {x: 6, y: 2.5690465}
  - {x: 7, y: 2.7748873}
  - {x: 8, y: 2.9664795}
  - {x: 9, y: 3.1464267}
  - {x: 10, y: 3.3166249}
  - {x: 11, y: 3.4785054}
  - {x: 12, y: 3.6331806}
  - {x: 13, y: 3.7815342}
  - {x: 14, y: 3.9242835}
  - {x: 15, y: 4.0620193}
  - {x: 16, y: 4.1952353}
  - {x: 17, y: 4.32435}
  - {x: 18, y: 4.4497194}
  - {x: 19, y: 4.571652}
  - {x: 20, y: 4.690416}
  - {x: 21, y: 4.806246}
  - {x: 22, y: 4.9193497}
  - {x: 23, y: 5.0299106}
  - {x: 24, y: 5.138093}
  - {x: 25, y: 5.2440443}
  - {x: 26, y: 5.347897}
  - {x: 27, y: 5.449771}
  - {x: 28, y: 5.5497746}
  - {x: 29, y: 5.648009}
  - {x: 30, y: 5.7445626}
  cachedRequirementCurve: fb0000006301000067020000ce040000be0a0000501a00009e450000e8c40000b84e0200044c070082331800ded55300dc452e0168006b04c0591c11006771440167714402677144036771440467714405677144066771440767714408677144096771440a6771440b6771440c6771440d677144
  cachedRawFormulaCurve: []
  baseMultiplierFactor: 1
  rootPower: 2
