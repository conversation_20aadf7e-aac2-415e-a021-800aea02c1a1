%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afaa217e1b23a9444b7a02f9e79889a6, type: 3}
  m_Name: Staircase
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 10c93727-6faa-48e9-9354-bc4af81d7ed9
  algorithmName: Staircase
  description: Creates a staircase pattern with plateaus of consistent difficulty
    followed by significant jumps, giving players time to adjust to each new difficulty
    tier.
  formulaExplanation: 'Formula: Uses step functions with increasing step heights


    Creates
    a progression with flat plateaus followed by significant jumps, like climbing
    a staircase with increasingly taller steps.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 413}
  - {x: 3, y: 417}
  - {x: 4, y: 421}
  - {x: 5, y: 719}
  - {x: 6, y: 726}
  - {x: 7, y: 733}
  - {x: 8, y: 1293}
  - {x: 9, y: 1306}
  - {x: 10, y: 1319}
  - {x: 11, y: 2401}
  - {x: 12, y: 2425}
  - {x: 13, y: 2449}
  - {x: 14, y: 4598}
  - {x: 15, y: 4644}
  - {x: 16, y: 4690}
  - {x: 17, y: 9073}
  - {x: 18, y: 9164}
  - {x: 19, y: 9256}
  - {x: 20, y: 18432}
  - {x: 21, y: 18616}
  - {x: 22, y: 18802}
  - {x: 23, y: 38512}
  - {x: 24, y: 38897}
  - {x: 25, y: 39286}
  - {x: 26, y: 82704}
  - {x: 27, y: 83531}
  - {x: 28, y: 84366}
  - {x: 29, y: 182405}
  - {x: 30, y: 184229}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.6500001}
  - {x: 2, y: 1.01}
  - {x: 3, y: 1.01}
  - {x: 4, y: 1.7068967}
  - {x: 5, y: 1.01}
  - {x: 6, y: 1.01}
  - {x: 7, y: 1.7637931}
  - {x: 8, y: 1.01}
  - {x: 9, y: 1.01}
  - {x: 10, y: 1.8206897}
  - {x: 11, y: 1.01}
  - {x: 12, y: 1.01}
  - {x: 13, y: 1.8775862}
  - {x: 14, y: 1.01}
  - {x: 15, y: 1.01}
  - {x: 16, y: 1.9344828}
  - {x: 17, y: 1.01}
  - {x: 18, y: 1.01}
  - {x: 19, y: 1.9913794}
  - {x: 20, y: 1.01}
  - {x: 21, y: 1.01}
  - {x: 22, y: 2.0482757}
  - {x: 23, y: 1.01}
  - {x: 24, y: 1.01}
  - {x: 25, y: 2.1051724}
  - {x: 26, y: 1.01}
  - {x: 27, y: 1.01}
  - {x: 28, y: 2.162069}
  - {x: 29, y: 1.01}
  - {x: 30, y: 1.01}
  cachedRequirementCurve: 770100007b0100007f01000052020000580200005e020000cc030000d6030000e00300006a0600007a0600008b0600002b0b0000480b0000650b00000a1400003d140000711400000225000061250000c12500004d46000001470000b74700003f8900009e8a0000018c00002e130100ee150100
  cachedRawFormulaCurve: []
  initialStepSize: 3
  maxStepSize: 5
  stepGrowthRate: 0.2
  zeroBoundaryMultiplier: 1.15
  zeroPlateauMultiplier: 1.01
  baseStepHeight: 1.5
  stepHeightGrowth: 0.5
  plateauMultiplierFactor: 0.7
