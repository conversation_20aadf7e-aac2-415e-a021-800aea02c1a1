%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 91143bfbb917c974e864b58989dbb1e0, type: 3}
  m_Name: Stepped
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 00f1de9d-53b0-48b6-ac22-8445ff29ef68
  algorithmName: Stepped
  description: Creates plateaus of difficulty with sudden jumps at specific level
    thresholds, making progression feel like distinct tiers or ranks.
  formulaExplanation: 'Formula: Uses higher multiplier at specific level thresholds


    Creates
    plateaus of difficulty with sudden jumps at specific level thresholds, making
    progression feel like distinct tiers or ranks.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 254}
  - {x: 6, y: 419}
  - {x: 7, y: 420}
  - {x: 8, y: 421}
  - {x: 9, y: 422}
  - {x: 10, y: 423}
  - {x: 11, y: 698}
  - {x: 12, y: 699}
  - {x: 13, y: 700}
  - {x: 14, y: 701}
  - {x: 15, y: 702}
  - {x: 16, y: 1158}
  - {x: 17, y: 1159}
  - {x: 18, y: 1160}
  - {x: 19, y: 1161}
  - {x: 20, y: 1162}
  - {x: 21, y: 1917}
  - {x: 22, y: 1918}
  - {x: 23, y: 1919}
  - {x: 24, y: 1920}
  - {x: 25, y: 1921}
  - {x: 26, y: 3170}
  - {x: 27, y: 3171}
  - {x: 28, y: 3172}
  - {x: 29, y: 3173}
  - {x: 30, y: 3174}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.88000005}
  - {x: 2, y: 0.88000005}
  - {x: 3, y: 0.88000005}
  - {x: 4, y: 0.88000005}
  - {x: 5, y: 1.6500001}
  - {x: 6, y: 0.88000005}
  - {x: 7, y: 0.88000005}
  - {x: 8, y: 0.88000005}
  - {x: 9, y: 0.88000005}
  - {x: 10, y: 1.6500001}
  - {x: 11, y: 0.88000005}
  - {x: 12, y: 0.88000005}
  - {x: 13, y: 0.88000005}
  - {x: 14, y: 0.88000005}
  - {x: 15, y: 1.6500001}
  - {x: 16, y: 0.88000005}
  - {x: 17, y: 0.88000005}
  - {x: 18, y: 0.88000005}
  - {x: 19, y: 0.88000005}
  - {x: 20, y: 1.6500001}
  - {x: 21, y: 0.88000005}
  - {x: 22, y: 0.88000005}
  - {x: 23, y: 0.88000005}
  - {x: 24, y: 0.88000005}
  - {x: 25, y: 1.6500001}
  - {x: 26, y: 0.88000005}
  - {x: 27, y: 0.88000005}
  - {x: 28, y: 0.88000005}
  - {x: 29, y: 0.88000005}
  - {x: 30, y: 1.6500001}
  cachedRequirementCurve: fb000000fc000000fd000000fe0000007d0100007e0100007f01000080010000810100004202000043020000440200004502000046020000690300006a0300006b0300006c0300006d0300002405000025050000260500002705000028050000bc070000bd070000be070000bf070000c0070000
  cachedRawFormulaCurve: []
  stepSize: 5
  stepThresholdMultiplier: 1.5
  betweenStepsMultiplier: 0.8
  zeroMultiplierStepThreshold: 1.15
  zeroMultiplierBetweenSteps: 1.03
