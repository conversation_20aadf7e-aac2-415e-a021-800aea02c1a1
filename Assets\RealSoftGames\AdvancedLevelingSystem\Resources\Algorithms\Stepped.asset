%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 91143bfbb917c974e864b58989dbb1e0, type: 3}
  m_Name: Stepped
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 00f1de9d-53b0-48b6-ac22-8445ff29ef68
  algorithmName: Stepped
  description: Creates plateaus of difficulty with sudden jumps at specific level
    thresholds, making progression feel like distinct tiers or ranks.
  formulaExplanation: 'Formula: Uses higher multiplier at specific level thresholds


    Creates
    plateaus of difficulty with sudden jumps at specific level thresholds, making
    progression feel like distinct tiers or ranks.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.225
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 254}
  - {x: 6, y: 419}
  - {x: 7, y: 420}
  - {x: 8, y: 421}
  - {x: 9, y: 422}
  - {x: 10, y: 423}
  - {x: 11, y: 698}
  - {x: 12, y: 699}
  - {x: 13, y: 700}
  - {x: 14, y: 701}
  - {x: 15, y: 702}
  - {x: 16, y: 1158}
  - {x: 17, y: 1159}
  - {x: 18, y: 1160}
  - {x: 19, y: 1161}
  - {x: 20, y: 1162}
  - {x: 21, y: 1917}
  - {x: 22, y: 1918}
  - {x: 23, y: 1919}
  - {x: 24, y: 1920}
  - {x: 25, y: 1921}
  - {x: 26, y: 3170}
  - {x: 27, y: 3171}
  - {x: 28, y: 3172}
  - {x: 29, y: 3173}
  - {x: 30, y: 3174}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.88000005}
  - {x: 2, y: 0.88000005}
  - {x: 3, y: 0.88000005}
  - {x: 4, y: 0.88000005}
  - {x: 5, y: 1.6500001}
  - {x: 6, y: 0.88000005}
  - {x: 7, y: 0.88000005}
  - {x: 8, y: 0.88000005}
  - {x: 9, y: 0.88000005}
  - {x: 10, y: 1.6500001}
  - {x: 11, y: 0.88000005}
  - {x: 12, y: 0.88000005}
  - {x: 13, y: 0.88000005}
  - {x: 14, y: 0.88000005}
  - {x: 15, y: 1.6500001}
  - {x: 16, y: 0.88000005}
  - {x: 17, y: 0.88000005}
  - {x: 18, y: 0.88000005}
  - {x: 19, y: 0.88000005}
  - {x: 20, y: 1.6500001}
  - {x: 21, y: 0.88000005}
  - {x: 22, y: 0.88000005}
  - {x: 23, y: 0.88000005}
  - {x: 24, y: 0.88000005}
  - {x: 25, y: 1.6500001}
  - {x: 26, y: 0.88000005}
  - {x: 27, y: 0.88000005}
  - {x: 28, y: 0.88000005}
  - {x: 29, y: 0.88000005}
  - {x: 30, y: 1.6500001}
  cachedRequirementCurve: fb000000fc000000fd000000fe000000d3010000d4010000d5010000d6010000d701000061030000620300006303000064030000650300003d0600003e0600003f06000040060000410600007e0b00007f0b0000800b0000810b0000820b00002515000026150000271500002815000029150000e2260000e3260000e4260000e5260000e62600007a4700007b4700007c4700007d4700007e4700005e8300005f8300006083000061830000628300006af100006bf100006cf100006df100006ef10000
  cachedRawFormulaCurve: []
  stepSize: 5
  stepThresholdMultiplier: 1.5
  betweenStepsMultiplier: 0.8
  zeroMultiplierStepThreshold: 1.15
  zeroMultiplierBetweenSteps: 1.03
