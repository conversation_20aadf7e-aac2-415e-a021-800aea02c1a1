%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7cffcc6cc970d547830239ef5d7732f, type: 3}
  m_Name: Tiered
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: e9e2e665-524a-464c-8d33-debb4ad48b3c
  algorithmName: Tiered
  description: Progression with distinct tiers, where each tier has its own fixed
    multiplier. Creates a feeling of advancement through ranks.
  formulaExplanation: 'Formula: Varies based on level tier


    Progression with
    distinct tiers, where each tier has its own fixed multiplier. Creates a feeling
    of advancement through ranks.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 251}
  - {x: 3, y: 252}
  - {x: 4, y: 253}
  - {x: 5, y: 254}
  - {x: 6, y: 255}
  - {x: 7, y: 256}
  - {x: 8, y: 257}
  - {x: 9, y: 258}
  - {x: 10, y: 259}
  - {x: 11, y: 260}
  - {x: 12, y: 261}
  - {x: 13, y: 262}
  - {x: 14, y: 288}
  - {x: 15, y: 317}
  - {x: 16, y: 349}
  - {x: 17, y: 384}
  - {x: 18, y: 422}
  - {x: 19, y: 464}
  - {x: 20, y: 561}
  - {x: 21, y: 679}
  - {x: 22, y: 822}
  - {x: 23, y: 995}
  - {x: 24, y: 1204}
  - {x: 25, y: 1457}
  - {x: 26, y: 1923}
  - {x: 27, y: 2538}
  - {x: 28, y: 3350}
  - {x: 29, y: 4422}
  - {x: 30, y: 5837}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 0.88000005}
  - {x: 2, y: 0.88000005}
  - {x: 3, y: 0.88000005}
  - {x: 4, y: 0.88000005}
  - {x: 5, y: 0.88000005}
  - {x: 6, y: 0.88000005}
  - {x: 7, y: 0.99000007}
  - {x: 8, y: 0.99000007}
  - {x: 9, y: 0.99000007}
  - {x: 10, y: 0.99000007}
  - {x: 11, y: 0.99000007}
  - {x: 12, y: 0.99000007}
  - {x: 13, y: 1.1}
  - {x: 14, y: 1.1}
  - {x: 15, y: 1.1}
  - {x: 16, y: 1.1}
  - {x: 17, y: 1.1}
  - {x: 18, y: 1.1}
  - {x: 19, y: 1.21}
  - {x: 20, y: 1.21}
  - {x: 21, y: 1.21}
  - {x: 22, y: 1.21}
  - {x: 23, y: 1.21}
  - {x: 24, y: 1.21}
  - {x: 25, y: 1.32}
  - {x: 26, y: 1.32}
  - {x: 27, y: 1.32}
  - {x: 28, y: 1.32}
  - {x: 29, y: 1.32}
  - {x: 30, y: 1.32}
  cachedRequirementCurve: f5010000f6010000f7010000f8010000f9010000fa010000fb010000fc010000fd010000fe010000ff010000000200000102000002020000030200000402000005020000060200000702000008020000090200000a0200000b0200000c0200000d0200000e0200000f0200001002000011020000120200004702000081020000c10200000803000056030000ab0300000904000070040000e20400005f05000072060000bc07000048090000230b00005d0d0000091000003e13000017170000b51b0000
  cachedRawFormulaCurve: []
  beginnerTierMultiplier: 1.05
  intermediateTierMultiplier: 1.1
  advancedTierMultiplier: 1.15
  expertTierMultiplier: 1.2
  masterTierMultiplier: 1.25
  baseMultiplierScale: 0.8
  tierIncrementScale: 0.1
