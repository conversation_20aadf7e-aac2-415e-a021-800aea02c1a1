%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2fbb257193b588d45b350dddd3c20989, type: 3}
  m_Name: Wave
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 23392563-c18a-47eb-a219-c198a7ba72d8
  algorithmName: Wave
  description: Creates a smooth wave pattern with gradually increasing amplitude,
    like ocean waves that grow larger over time.
  formulaExplanation: 'Formula: Uses sine waves with increasing amplitude and frequency


    Creates
    a wave pattern where both the height and frequency of waves increase as levels
    progress, creating a more dynamic and challenging progression.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 30
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 303}
  - {x: 4, y: 334}
  - {x: 5, y: 369}
  - {x: 6, y: 407}
  - {x: 7, y: 448}
  - {x: 8, y: 492}
  - {x: 9, y: 539}
  - {x: 10, y: 590}
  - {x: 11, y: 647}
  - {x: 12, y: 713}
  - {x: 13, y: 788}
  - {x: 14, y: 872}
  - {x: 15, y: 962}
  - {x: 16, y: 1056}
  - {x: 17, y: 1154}
  - {x: 18, y: 1261}
  - {x: 19, y: 1386}
  - {x: 20, y: 1533}
  - {x: 21, y: 1700}
  - {x: 22, y: 1876}
  - {x: 23, y: 2053}
  - {x: 24, y: 2238}
  - {x: 25, y: 2451}
  - {x: 26, y: 2711}
  - {x: 27, y: 3012}
  - {x: 28, y: 3324}
  - {x: 29, y: 3628}
  - {x: 30, y: 3950}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.101494}
  - {x: 3, y: 1.1028823}
  - {x: 4, y: 1.1034921}
  - {x: 5, y: 1.1027712}
  - {x: 6, y: 1.1006445}
  - {x: 7, y: 1.0977625}
  - {x: 8, y: 1.0954218}
  - {x: 9, y: 1.095039}
  - {x: 10, y: 1.0973108}
  - {x: 11, y: 1.1014948}
  - {x: 12, y: 1.1053984}
  - {x: 13, y: 1.1064155}
  - {x: 14, y: 1.1032823}
  - {x: 15, y: 1.0974538}
  - {x: 16, y: 1.0927908}
  - {x: 17, y: 1.0931227}
  - {x: 18, y: 1.0990028}
  - {x: 19, y: 1.1063324}
  - {x: 20, y: 1.1088716}
  - {x: 21, y: 1.1035919}
  - {x: 22, y: 1.0944283}
  - {x: 23, y: 1.0899329}
  - {x: 24, y: 1.0953393}
  - {x: 25, y: 1.1061304}
  - {x: 26, y: 1.1110338}
  - {x: 27, y: 1.103742}
  - {x: 28, y: 1.091502}
  - {x: 29, y: 1.0887593}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: fc000000ff0000000201000005010000080100000b0100000e0100001101000014010000170100001a0100001d010000200100002301000026010000290100002c0100002f0100003201000035010000380100003b0100003e0100004101000044010000470100004a0100004d01000050010000
  cachedRawFormulaCurve: []
  baseFrequency: 5
  frequencyGrowth: 3
  baseAmplitude: 0.05
  amplitudeGrowth: 4
  zeroBaseMultiplier: 1.05
