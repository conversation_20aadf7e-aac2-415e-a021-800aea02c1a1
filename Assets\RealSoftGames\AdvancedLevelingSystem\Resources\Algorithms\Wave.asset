%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2fbb257193b588d45b350dddd3c20989, type: 3}
  m_Name: Wave
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 23392563-c18a-47eb-a219-c198a7ba72d8
  algorithmName: Wave
  description: Creates a smooth wave pattern with gradually increasing amplitude,
    like ocean waves that grow larger over time.
  formulaExplanation: 'Formula: Uses sine waves with increasing amplitude and frequency


    Creates
    a wave pattern where both the height and frequency of waves increase as levels
    progress, creating a more dynamic and challenging progression.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 275}
  - {x: 3, y: 303}
  - {x: 4, y: 334}
  - {x: 5, y: 369}
  - {x: 6, y: 407}
  - {x: 7, y: 448}
  - {x: 8, y: 492}
  - {x: 9, y: 539}
  - {x: 10, y: 590}
  - {x: 11, y: 647}
  - {x: 12, y: 713}
  - {x: 13, y: 788}
  - {x: 14, y: 872}
  - {x: 15, y: 962}
  - {x: 16, y: 1056}
  - {x: 17, y: 1154}
  - {x: 18, y: 1261}
  - {x: 19, y: 1386}
  - {x: 20, y: 1533}
  - {x: 21, y: 1700}
  - {x: 22, y: 1876}
  - {x: 23, y: 2053}
  - {x: 24, y: 2238}
  - {x: 25, y: 2451}
  - {x: 26, y: 2711}
  - {x: 27, y: 3012}
  - {x: 28, y: 3324}
  - {x: 29, y: 3628}
  - {x: 30, y: 3950}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.1}
  - {x: 2, y: 1.101494}
  - {x: 3, y: 1.1028823}
  - {x: 4, y: 1.1034921}
  - {x: 5, y: 1.1027712}
  - {x: 6, y: 1.1006445}
  - {x: 7, y: 1.0977625}
  - {x: 8, y: 1.0954218}
  - {x: 9, y: 1.095039}
  - {x: 10, y: 1.0973108}
  - {x: 11, y: 1.1014948}
  - {x: 12, y: 1.1053984}
  - {x: 13, y: 1.1064155}
  - {x: 14, y: 1.1032823}
  - {x: 15, y: 1.0974538}
  - {x: 16, y: 1.0927908}
  - {x: 17, y: 1.0931227}
  - {x: 18, y: 1.0990028}
  - {x: 19, y: 1.1063324}
  - {x: 20, y: 1.1088716}
  - {x: 21, y: 1.1035919}
  - {x: 22, y: 1.0944283}
  - {x: 23, y: 1.0899329}
  - {x: 24, y: 1.0953393}
  - {x: 25, y: 1.1061304}
  - {x: 26, y: 1.1110338}
  - {x: 27, y: 1.103742}
  - {x: 28, y: 1.091502}
  - {x: 29, y: 1.0887593}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: f9010000fe01000003020000080200000d02000012020000170200001c02000021020000260200002c02000032020000380200003e020000440200004a02000050020000560200005c02000062020000680200006e020000740200007a02000080020000860200008c020000930200009a020000a1020000a8020000af020000b6020000bd020000c4020000cb020000d2020000d9020000e0020000e7020000ee020000f6020000fe020000060300000e030000160300001e030000260300002e030000
  cachedRawFormulaCurve: []
  baseFrequency: 5
  frequencyGrowth: 3
  baseAmplitude: 0.05
  amplitudeGrowth: 4
  zeroBaseMultiplier: 1.05
