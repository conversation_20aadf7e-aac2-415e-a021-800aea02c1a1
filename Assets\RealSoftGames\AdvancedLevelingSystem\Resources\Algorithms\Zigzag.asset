%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Zigzag
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 4142de15-14db-41d8-8db9-9c51e1c90306
  algorithmName: Zigzag
  description: A zigzag progression with sharp ups and downs.
  formulaExplanation: Experience = Previous * (1.1 + 0.1 * zigzag(Level))
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 1
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 60
  cachedLevelUpMultiplier: 0.92
  cachedRequirementCurvePoints:
  - {x: 2, y: 261}
  - {x: 3, y: 272}
  - {x: 4, y: 273}
  - {x: 5, y: 285}
  - {x: 6, y: 297}
  - {x: 7, y: 298}
  - {x: 8, y: 311}
  - {x: 9, y: 324}
  - {x: 10, y: 325}
  - {x: 11, y: 339}
  - {x: 12, y: 353}
  - {x: 13, y: 354}
  - {x: 14, y: 369}
  - {x: 15, y: 385}
  - {x: 16, y: 386}
  - {x: 17, y: 402}
  - {x: 18, y: 419}
  - {x: 19, y: 420}
  - {x: 20, y: 438}
  - {x: 21, y: 457}
  - {x: 22, y: 458}
  - {x: 23, y: 478}
  - {x: 24, y: 498}
  - {x: 25, y: 499}
  - {x: 26, y: 520}
  - {x: 27, y: 542}
  - {x: 28, y: 543}
  - {x: 29, y: 566}
  - {x: 30, y: 590}
  - {x: 31, y: 591}
  - {x: 32, y: 616}
  - {x: 33, y: 642}
  - {x: 34, y: 643}
  - {x: 35, y: 670}
  - {x: 36, y: 699}
  - {x: 37, y: 700}
  - {x: 38, y: 730}
  - {x: 39, y: 761}
  - {x: 40, y: 762}
  - {x: 41, y: 795}
  - {x: 42, y: 829}
  - {x: 43, y: 830}
  - {x: 44, y: 865}
  - {x: 45, y: 902}
  - {x: 46, y: 903}
  - {x: 47, y: 942}
  - {x: 48, y: 982}
  - {x: 49, y: 983}
  - {x: 50, y: 1025}
  - {x: 51, y: 1069}
  - {x: 52, y: 1070}
  - {x: 53, y: 1116}
  - {x: 54, y: 1164}
  - {x: 55, y: 1165}
  - {x: 56, y: 1215}
  - {x: 57, y: 1267}
  - {x: 58, y: 1268}
  - {x: 59, y: 1322}
  - {x: 60, y: 1378}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.0426667}
  - {x: 2, y: 1.0426667}
  - {x: 3, y: 0.92}
  - {x: 4, y: 1.0426667}
  - {x: 5, y: 1.0426667}
  - {x: 6, y: 0.92}
  - {x: 7, y: 1.0426667}
  - {x: 8, y: 1.0426667}
  - {x: 9, y: 0.92}
  - {x: 10, y: 1.0426667}
  - {x: 11, y: 1.0426667}
  - {x: 12, y: 0.92}
  - {x: 13, y: 1.0426667}
  - {x: 14, y: 1.0426667}
  - {x: 15, y: 0.92}
  - {x: 16, y: 1.0426667}
  - {x: 17, y: 1.0426667}
  - {x: 18, y: 0.92}
  - {x: 19, y: 1.0426667}
  - {x: 20, y: 1.0426667}
  - {x: 21, y: 0.92}
  - {x: 22, y: 1.0426667}
  - {x: 23, y: 1.0426667}
  - {x: 24, y: 0.92}
  - {x: 25, y: 1.0426667}
  - {x: 26, y: 1.0426667}
  - {x: 27, y: 0.92}
  - {x: 28, y: 1.0426667}
  - {x: 29, y: 1.0426667}
  - {x: 30, y: 0.92}
  - {x: 31, y: 1.0426667}
  - {x: 32, y: 1.0426667}
  - {x: 33, y: 0.92}
  - {x: 34, y: 1.0426667}
  - {x: 35, y: 1.0426667}
  - {x: 36, y: 0.92}
  - {x: 37, y: 1.0426667}
  - {x: 38, y: 1.0426667}
  - {x: 39, y: 0.92}
  - {x: 40, y: 1.0426667}
  - {x: 41, y: 1.0426667}
  - {x: 42, y: 0.92}
  - {x: 43, y: 1.0426667}
  - {x: 44, y: 1.0426667}
  - {x: 45, y: 0.92}
  - {x: 46, y: 1.0426667}
  - {x: 47, y: 1.0426667}
  - {x: 48, y: 0.92}
  - {x: 49, y: 1.0426667}
  - {x: 50, y: 1.0426667}
  - {x: 51, y: 0.92}
  - {x: 52, y: 1.0426667}
  - {x: 53, y: 1.0426667}
  - {x: 54, y: 0.92}
  - {x: 55, y: 1.0426667}
  - {x: 56, y: 1.0426667}
  - {x: 57, y: 0.92}
  - {x: 58, y: 1.0426667}
  - {x: 59, y: 1.0426667}
  - {x: 60, y: 0.92}
  cachedRequirementCurve: 0501000010010000110100001d010000290100002a01000037010000440100004501000053010000610100006201000071010000810100008201000092010000a3010000a4010000b6010000c9010000ca010000de010000f2010000f3010000080200001e0200001f020000360200004e0200004f0200006802000082020000830200009e020000bb020000bc020000da020000f9020000fa0200001b0300003d0300003e030000610300008603000087030000ae030000d6030000d7030000010400002d0400002e0400005c0400008c0400008d040000bf040000f3040000f40400002a05000062050000
  cachedRawFormulaCurve: []
