%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39fcfb81d5d04034a8723efcae8018eb, type: 3}
  m_Name: Zigzag
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 4142de15-14db-41d8-8db9-9c51e1c90306
  algorithmName: Zigzag
  description: A zigzag progression with sharp ups and downs.
  formulaExplanation: Experience = Previous * (1.1 + 0.1 * zigzag(Level))
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points:
  - {x: 0, y: 1.1}
  - {x: 1, y: 1.3}
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 1
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 500
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1
  cachedRequirementCurvePoints:
  - {x: 2, y: 312}
  - {x: 3, y: 389}
  - {x: 4, y: 428}
  - {x: 5, y: 534}
  - {x: 6, y: 666}
  - {x: 7, y: 733}
  - {x: 8, y: 914}
  - {x: 9, y: 1139}
  - {x: 10, y: 1253}
  - {x: 11, y: 1562}
  - {x: 12, y: 1947}
  - {x: 13, y: 2142}
  - {x: 14, y: 2670}
  - {x: 15, y: 3329}
  - {x: 16, y: 3662}
  - {x: 17, y: 4565}
  - {x: 18, y: 5691}
  - {x: 19, y: 6260}
  - {x: 20, y: 7804}
  - {x: 21, y: 9729}
  - {x: 22, y: 10702}
  - {x: 23, y: 13342}
  - {x: 24, y: 16633}
  - {x: 25, y: 18296}
  - {x: 26, y: 22809}
  - {x: 27, y: 28435}
  - {x: 28, y: 31278}
  - {x: 29, y: 38993}
  - {x: 30, y: 48611}
  cachedRawFormulaCurvePoints:
  - {x: 1, y: 1.2466667}
  - {x: 2, y: 1.2466667}
  - {x: 3, y: 1.1}
  - {x: 4, y: 1.2466667}
  - {x: 5, y: 1.2466667}
  - {x: 6, y: 1.1}
  - {x: 7, y: 1.2466667}
  - {x: 8, y: 1.2466667}
  - {x: 9, y: 1.1}
  - {x: 10, y: 1.2466667}
  - {x: 11, y: 1.2466667}
  - {x: 12, y: 1.1}
  - {x: 13, y: 1.2466667}
  - {x: 14, y: 1.2466667}
  - {x: 15, y: 1.1}
  - {x: 16, y: 1.2466667}
  - {x: 17, y: 1.2466667}
  - {x: 18, y: 1.1}
  - {x: 19, y: 1.2466667}
  - {x: 20, y: 1.2466667}
  - {x: 21, y: 1.1}
  - {x: 22, y: 1.2466667}
  - {x: 23, y: 1.2466667}
  - {x: 24, y: 1.1}
  - {x: 25, y: 1.2466667}
  - {x: 26, y: 1.2466667}
  - {x: 27, y: 1.1}
  - {x: 28, y: 1.2466667}
  - {x: 29, y: 1.2466667}
  - {x: 30, y: 1.1}
  cachedRequirementCurve: 370200008302000084020000da0200003b0300003c030000aa0300002704000028040000b604000057050000580500000e060000dd060000de060000c8070000d2080000d3080000000a0000550b0000560b0000d90c0000900e0000910e000082100000b5120000b612000035150000091800000a1800003f1b0000e11e0000e21e000000230000ab270000ac270000f62c0000f5320000f6320000c139000074410000754100002f4a000013540000145400004a5f0000ff6b0000006c0000667a0000
  cachedRawFormulaCurve: []
