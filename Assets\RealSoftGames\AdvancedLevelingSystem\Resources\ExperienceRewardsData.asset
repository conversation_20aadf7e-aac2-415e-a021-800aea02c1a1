%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fd42e085fc663e24796061beca1b75f5, type: 3}
  m_Name: ExperienceRewardsData
  m_EditorClassIdentifier: 
  rewardCategories:
  - name: Epic Quest
    multiplier: 0.12
  - name: Hard Quest
    multiplier: 0.08
  - name: Medium Quest
    multiplier: 0.05
  - name: Easy Quest
    multiplier: 0.03
  - name: Daily Activity
    multiplier: 0.02
  - name: World Boss
    multiplier: 0.1
  - name: Boss Enemy
    multiplier: 0.05
  - name: Elite Enemy
    multiplier: 0.015
  - name: Hard Enemy
    multiplier: 0.003
  - name: Medium Enemy
    multiplier: 0.002
  - name: Easy Enemy
    multiplier: 0.001
  - name: Very Easy Enemy
    multiplier: 0.0005
  - name: Crafting Item
    multiplier: 0.0001
  - name: Gathering Resource
    multiplier: 0.0001
  selectedAlgorithm: {fileID: 11400000, guid: 9e3893ffc0a0f5e4ea3846dd035e4c19, type: 2}
  levelUpMultiplier: 1
  startingExperience: 250
  startingLevel: 1
  maxLevel: 60
  levelRangeSize: 5
  showDetailedBreakdown: 1
