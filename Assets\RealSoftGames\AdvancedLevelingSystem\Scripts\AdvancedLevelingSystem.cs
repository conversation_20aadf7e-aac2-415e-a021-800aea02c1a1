﻿using System;
using UnityEngine;


namespace RealSoftGames.AdvancedLevelingSystem
{
    // The LevelingDifficulty enum has been replaced with ScriptableObjects
    // See DifficultyRating.cs and AlgorithmDatabase.cs for the new implementation
    // The legacy enum is kept for backward compatibility but will be removed in a future version

    public class AdvancedLevelingSystem : MonoBehaviour
    {
        public static AdvancedLevelingSystem Instance;

        /// <summary>
        /// Returns, Current EXP, RequiredEXP
        /// </summary>
        public static Action<int, int> OnExpModified;

        /// <summary>
        /// Reeturns ExpectedEXP,RequiredEXP   This is used for Lerping the UI incrementally over time
        /// </summary>
        public static Action<int, int> OnEXPUIUpdate;

        /// <summary>
        /// Triggers the OnLevelUP Event when the CurrentExp(ExpectedExp) = RequiredExp
        /// </summary>
        public static Action<int> OnLevelUp;

        /// <summary>
        /// OnMaxLevel is triggered when the player reaches the maximum level specified in maxLevel
        /// </summary>
        public static Action OnMaxLevel;

        //This is used to smoothly update the UI
        private int expectedExp;

        /// <summary>
        /// Reference to the leveling system data ScriptableObject
        /// </summary>
        [SerializeField] private LevelingSystemData levelingSystemData;

        /// <summary>
        /// Current level of the player
        /// </summary>
        [SerializeField, ReadOnly] private int currentLevel = 1;

        /// <summary>
        /// The current Experience for the player
        /// </summary>
        [SerializeField, ReadOnly] private int currentExperience = 0;

        /// <summary>
        /// The required EXP
        /// </summary>
        [SerializeField, ReadOnly] private int requiredExperience = 250;

        /// <summary>
        /// Paly Audio on level up
        /// </summary>
        [SerializeField] private bool playLevelupAudio;

        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip levelUpClip;
        [SerializeField] private ParticleSystem levelUpEffect;

        /// <summary>
        /// The level Bar & Text increment smoothly over this transition time
        /// </summary>
        [SerializeField, Range(0f, 100f)] public float transitionDuration = 10f;

        public int MaxLevel { get => levelingSystemData != null ? levelingSystemData.maxLevel : 30; }
        public int CurrentLevel { get => currentLevel; private set => currentLevel = value; }
        public int CurrentExperience { get => currentExperience; set => currentExperience = value; }
        public int RequiredExperience { get => requiredExperience; private set => requiredExperience = value; }
        public float LevelUpMultiplier { get => levelingSystemData != null ? levelingSystemData.levelUpMultiplier : 1.1f; }
        public LevelingAlgorithmBase LevelingAlgorithm { get => levelingSystemData != null ? levelingSystemData.levelingAlgorithm : null; }
        public AudioClip LevelUpClip { get => levelingSystemData != null ? levelingSystemData.levelUpClip : null; }

        public bool IsMaxLevel { get => currentLevel == MaxLevel; }

        /// <summary>
        /// Gets the description for the current leveling algorithm
        /// </summary>
        /// <returns>The description string or a default message if no description is available</returns>
        public string GetCurrentAlgorithmDescription()
        {
            if (LevelingAlgorithm != null)
            {
                return LevelingAlgorithm.Description;
            }

            return "No algorithm selected";
        }

        /// <summary>
        /// Gets the formula explanation for the current leveling algorithm
        /// </summary>
        /// <returns>The formula explanation or a default message if no algorithm is available</returns>
        public string GetCurrentAlgorithmFormulaExplanation()
        {
            if (LevelingAlgorithm != null)
            {
                return LevelingAlgorithm.formulaExplanation;
            }

            return "No algorithm selected";
        }

        /// <summary>
        /// Helper method to update the LevelingSystemData ScriptableObject
        /// </summary>
        public void UpdateLevelingSystemData(int maxLevel = -1, float levelUpMultiplier = -1, LevelingAlgorithmBase algorithm = null, AudioClip levelUpClip = null)
        {
            if (levelingSystemData == null)
            {
                Debug.LogWarning("LevelingSystemData is null. Cannot update.");
                return;
            }

            if (maxLevel > 0)
                levelingSystemData.maxLevel = maxLevel;

            if (levelUpMultiplier > 0)
                levelingSystemData.levelUpMultiplier = levelUpMultiplier;

            if (algorithm != null)
                levelingSystemData.levelingAlgorithm = algorithm;

            if (levelUpClip != null)
                levelingSystemData.levelUpClip = levelUpClip;

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(levelingSystemData);
            #endif
        }

        private void Awake()
        {
            if (Instance == null || Instance != this)
                Instance = this;

            OnLevelUp?.Invoke(CurrentLevel);
        }

        private void Start()
        {
            // Initialize the leveling system data if not set
            if (levelingSystemData == null)
            {
                // Try to load the default data from Resources
                levelingSystemData = Resources.Load<LevelingSystemData>("LevelingSystemData");

                // If still null, create a default instance
                if (levelingSystemData == null)
                {
                    Debug.LogWarning("LevelingSystemData not found. Using default values.");
                }
            }

            // Initialize required experience from the data
            if (requiredExperience <= 0)
            {
                requiredExperience = levelingSystemData != null ? levelingSystemData.initialRequiredExperience : 250;
            }

            // Initialize UI Elements
            OnExpModified?.Invoke(CurrentExperience, RequiredExperience);
            OnLevelUp?.Invoke(CurrentLevel);
            OnLevelUp += PlayLevelUpAudio;
            OnLevelUp += PlayLevelUpEffect;
            OnEXPUIUpdate?.Invoke(CurrentExperience, RequiredExperience);
        }

        private void OnDestroy()
        {
            OnLevelUp = null;
            OnExpModified = null;
        }

        private void FixedUpdate()
        {
            // Check if the current experience is not equal to the target experience
            if (expectedExp != CurrentExperience)
            {
                if (currentExperience >= requiredExperience)
                {
                    float t = Mathf.Clamp01(Time.fixedDeltaTime / (transitionDuration / 100));
                    float smoothStepT = Mathf.SmoothStep(0f, 1f, t);
                    expectedExp = Mathf.CeilToInt(Mathf.Lerp(expectedExp, requiredExperience, smoothStepT));

                    OnEXPUIUpdate?.Invoke(expectedExp, requiredExperience);
                    if (expectedExp >= requiredExperience)
                        LevelUp();
                }
                else
                {
                    float t = Mathf.Clamp01(Time.fixedDeltaTime / (transitionDuration / 100));
                    float smoothStepT = Mathf.SmoothStep(0f, 1f, t);
                    expectedExp = Mathf.CeilToInt(Mathf.Lerp(expectedExp, currentExperience, smoothStepT));

                    OnEXPUIUpdate?.Invoke(expectedExp, requiredExperience);
                }
            }
            else if (currentExperience >= requiredExperience)
            {
                // LevelUp Detected
                LevelUp();
            }
        }

        private void PlayLevelUpAudio(int level)
        {
            if (audioSource != null && LevelUpClip != null)
            {
                audioSource.PlayOneShot(LevelUpClip);
            }
        }

        private void PlayLevelUpEffect(int level)
        {
            if (levelUpEffect != null)
                levelUpEffect.Play();
        }

        private void LevelUp()
        {
            currentLevel++;
            currentExperience -= requiredExperience;
            expectedExp = 0;

            // Adjust experience needed for the next level based on the selected algorithm
            if (LevelingAlgorithm != null)
            {
                // Use the algorithm's CalculateNextRequirement method
                requiredExperience = LevelingAlgorithm.CalculateNextRequirement(
                    requiredExperience,
                    currentLevel,
                    LevelUpMultiplier,
                    1, // Starting level
                    MaxLevel);
            }
            else
            {
                // Fallback to a simple linear progression if no algorithm is selected
                LinearLevelUp();
            }

            // You can customize the level-up process based on your requirements
            OnExpModified?.Invoke(CurrentExperience, RequiredExperience);
            OnEXPUIUpdate?.Invoke(expectedExp, requiredExperience);
            OnLevelUp?.Invoke(CurrentLevel);

            if (IsMaxLevel)
            {
                currentExperience = 0;
                OnMaxLevel?.Invoke();
                OnMaxLevel = null;
                // Invoke OnExpModified event again with updated values
                OnExpModified?.Invoke(CurrentExperience, RequiredExperience);
            }
        }

        /// <summary>
        /// Add Experience to the leveling system
        /// </summary>
        /// <param name="experienceToAdd"></param>
        /// <returns></returns>
        public int AddExperience(int experienceToAdd)
        {
            if (currentLevel == MaxLevel)
                return experienceToAdd; // Return remaining experience

            currentExperience += experienceToAdd;

            // Invoke OnExpModified event with current experience and required experience to level up
            OnExpModified?.Invoke(CurrentExperience, RequiredExperience);

            // Return remaining experience if level-up is detected
            if (currentExperience >= requiredExperience)
                return currentExperience - requiredExperience;

            return 0; // No remaining experience
        }

        #region Level Up Formulas

        // Very Easy leveling formula - extremely gentle progression
        private void VeryEasyLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal fixed multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * 1.02f);
            }
            else
            {
                // Minimal increase in required experience (only 2-5% per level)
                float minimalMultiplier = 1.02f + (LevelUpMultiplier - 1.0f) * 0.1f; // Scale based on levelUpMultiplier but keep it very low
                requiredExperience = Mathf.RoundToInt(requiredExperience * minimalMultiplier);
            }
        }

        // Flat leveling formula - almost no increase in difficulty
        private void FlatLevelUp()
        {
            // Fixed small amount added each level instead of multiplication
            int baseIncrease = Mathf.RoundToInt(requiredExperience * 0.01f); // Just 1% of current requirement
            requiredExperience += baseIncrease;
        }

        // Early Boost leveling formula - makes early levels very easy
        private void EarlyBoostLevelUp()
        {
            // Early levels have very small increases, later levels increase more
            float earlyLevelThreshold = 10f;
            float progressionFactor;

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal progression
                if (currentLevel < earlyLevelThreshold)
                {
                    // Very small increase for early levels (1-2%)
                    progressionFactor = 1.01f + (currentLevel / earlyLevelThreshold) * 0.01f;
                }
                else
                {
                    // Slightly higher but still gentle progression after early levels
                    progressionFactor = 1.02f;
                }
            }
            else
            {
                if (currentLevel < earlyLevelThreshold)
                {
                    // Very small increase for early levels (1-3%)
                    progressionFactor = 1.01f + (currentLevel / earlyLevelThreshold) * 0.02f;
                }
                else
                {
                    // Normal progression after early levels
                    progressionFactor = 1.03f + (LevelUpMultiplier - 1.0f) * 0.5f;
                }
            }

            requiredExperience = Mathf.RoundToInt(requiredExperience * progressionFactor);
        }

        // Decremental leveling formula - gets easier as you level up
        private void DecrementalLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal decreasing progression
                float minDecreaseFactor = Mathf.Max(0.95f, 1.0f - (currentLevel / (float)MaxLevel) * 0.1f);
                float minEffectiveMultiplier = 1.01f * minDecreaseFactor;

                // Ensure we always have at least some increase
                minEffectiveMultiplier = Mathf.Max(1.005f, minEffectiveMultiplier);
                requiredExperience = Mathf.RoundToInt(requiredExperience * minEffectiveMultiplier);
            }
            else
            {
                // The multiplier decreases as level increases
                float decreaseFactor = Mathf.Max(0.95f, 1.0f - (currentLevel / (float)MaxLevel) * 0.2f);
                float effectiveMultiplier = 1.0f + (LevelUpMultiplier - 1.0f) * decreaseFactor;

                // Ensure we always have at least some increase
                effectiveMultiplier = Mathf.Max(1.01f, effectiveMultiplier);
                requiredExperience = Mathf.RoundToInt(requiredExperience * effectiveMultiplier);
            }
        }

        // Micro Progression leveling formula - tiny increments in difficulty
        private void MicroProgressionLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal fixed increase
                // Add a tiny fixed amount to ensure some progression
                int minIncrease = Mathf.Max(1, Mathf.RoundToInt(requiredExperience * 0.003f)); // At least 0.3% increase
                requiredExperience += minIncrease;
            }
            else
            {
                // Uses a logarithmic scale to make increases very small
                float microFactor = Mathf.Log10(currentLevel + 10) / Mathf.Log10(currentLevel + 9); // Very close to 1.0
                requiredExperience = Mathf.RoundToInt(requiredExperience * microFactor);

                // Add a tiny fixed amount to ensure some progression
                int minIncrease = Mathf.Max(1, Mathf.RoundToInt(requiredExperience * 0.005f)); // At least 0.5% increase
                requiredExperience += minIncrease;
            }
        }

        // Linear leveling formula
        private void LinearLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a small fixed multiplier
                // to ensure some progression
                requiredExperience = Mathf.RoundToInt(requiredExperience * 1.05f);
            }
            else
            {
                // Use the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * LevelUpMultiplier);
            }
        }

        // Sine wave leveling formula (modified to create an S-curve)
        private void SineWaveLevelUp()
        {
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)currentLevel / MaxLevel);

            // Create a true S-curve using a logistic function
            // This gives us a proper S-curve that starts slow, accelerates in the middle, and levels off at the end

            // Shift and scale the normalized position to center the S-curve
            // Map 0-1 to -6 to 6 for a good logistic curve range
            float x = (normalizedPosition - 0.5f) * 12.0f;

            // Apply the logistic function: 1/(1+e^(-x))
            // This creates a smooth S-curve from 0 to 1
            float sCurveValue = 1.0f / (1.0f + Mathf.Exp(-x));

            // Define the base range of multipliers for the S-curve
            float minMultiplier = 1.01f;  // Minimum growth rate (at beginning and end)
            float maxMultiplier = 1.10f;   // Maximum growth rate (in the middle)
            float effectiveMultiplier;

            // Apply the levelUpMultiplier to adjust the curve intensity
            if (LevelUpMultiplier <= 0.01f) // Effectively zero - use pure S-curve
            {
                // Just use the base S-curve without any additional scaling
                effectiveMultiplier = Mathf.Lerp(minMultiplier, maxMultiplier, sCurveValue);
            }
            else // Apply the levelUpMultiplier to scale the curve
            {
                // Scale the range based on the levelUpMultiplier
                // This preserves the S-curve shape while making it steeper or flatter
                float range = maxMultiplier - minMultiplier;
                float scaledMaxMultiplier = minMultiplier + (range * LevelUpMultiplier);
                effectiveMultiplier = Mathf.Lerp(minMultiplier, scaledMaxMultiplier, sCurveValue);
            }

            // Apply the effective multiplier
            requiredExperience = Mathf.RoundToInt(requiredExperience * effectiveMultiplier);
        }

        // Exponential leveling formula
        private void ExponentialLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a linear progression
                // Add a small percentage increase to ensure progression
                requiredExperience = Mathf.RoundToInt(requiredExperience * 1.05f);
            }
            else
            {
                // Use the exponential formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(LevelUpMultiplier, currentLevel));
            }
        }

        // Quadratic leveling formula
        private void QuadraticLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure quadratic formula
                // based only on the current level
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * 0.1f, 2));
            }
            else
            {
                // Use the quadratic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * LevelUpMultiplier, 2));
            }
        }

        // Logarithmic leveling formula
        private void LogarithmicLevelUp()
        {
            // Handle the case when levelUpMultiplier is close to 1 or 0
            if (LevelUpMultiplier <= 0.01f || Mathf.Abs(LevelUpMultiplier - 1.0f) < 0.01f)
            {
                // Use a default logarithmic curve with base 2 when multiplier is invalid
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Log(currentLevel + 1, 2.0f));
            }
            else
            {
                // Use the specified multiplier as the logarithm base
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Log(currentLevel + 1, LevelUpMultiplier));
            }
        }

        // Square root leveling formula
        private void RootLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure square root formula
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Sqrt(currentLevel));
            }
            else
            {
                // Apply the levelUpMultiplier to scale the curve
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Sqrt(currentLevel * LevelUpMultiplier));
            }
        }

        // Fibonacci sequence leveling formula
        private void FibonacciLevelUp()
        {
            // Calculate the Fibonacci number for the current level
            int fibNumber = Fibonacci(currentLevel);

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a small fixed multiplier
                // with the Fibonacci sequence to ensure progression
                float baseMultiplier = 1.01f;
                requiredExperience = Mathf.RoundToInt(requiredExperience * (baseMultiplier + fibNumber * 0.01f));
            }
            else
            {
                // Use the Fibonacci sequence with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + fibNumber * LevelUpMultiplier));
            }

            static int Fibonacci(int n)
            {
                int a = 0;
                int b = 1;
                for (int i = 0; i < n; i++)
                {
                    int temp = a;
                    a = b;
                    b = temp + b;
                }
                return a;
            }
        }

        // Geometric progression leveling formula
        private void GeometricLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a linear progression
                // Add a small percentage increase to ensure progression
                requiredExperience = Mathf.RoundToInt(requiredExperience * 1.05f);
            }
            else
            {
                // Use the geometric formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(LevelUpMultiplier, currentLevel));
            }
        }

        // Cubic leveling formula
        private void CubicLevelUp()
        {
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure cubic formula
                // with a small coefficient to prevent excessive growth
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * 0.05f, 3));
            }
            else
            {
                // Use the cubic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * LevelUpMultiplier, 3));
            }
        }

        // Polynomial leveling formula (higher degree polynomial growth)
        private void PolynomialLevelUp()
        {
            float degree = 2.5f; // Degree between 2 (quadratic) and 3 (cubic)

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure polynomial formula
                // with a small coefficient to prevent excessive growth
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * 0.075f, degree));
            }
            else
            {
                // Use the polynomial formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(currentLevel * LevelUpMultiplier, degree));
            }
        }

        // Hyperbolic leveling formula (inverse relationship with level)
        private void HyperbolicLevelUp()
        {
            // Uses a hyperbolic function that grows more slowly at higher levels
            float baseValue = requiredExperience;
            float factor = 1.0f / (1.0f + Mathf.Exp(-currentLevel * 0.1f));

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal growth rate
                // that still reflects the hyperbolic curve shape
                requiredExperience = Mathf.RoundToInt(baseValue * (1 + factor * 0.05f));
            }
            else
            {
                // Use the hyperbolic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(baseValue * (1 + factor * LevelUpMultiplier));
            }
        }

        // Sinusoidal leveling formula (oscillating difficulty)
        private void SinusoidalLevelUp()
        {
            // Creates a wave pattern that oscillates between easier and harder levels
            float frequency = 0.5f; // Controls how often the difficulty oscillates
            float amplitude = 0.3f; // Controls how much the difficulty varies
            float baseMultiplier = 1.2f;
            float oscillation = 1 + amplitude * Mathf.Sin(frequency * currentLevel * Mathf.PI);

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the base oscillation
                // with a small multiplier to ensure progression
                requiredExperience = Mathf.RoundToInt(requiredExperience * baseMultiplier * oscillation * 0.1f);
            }
            else
            {
                // Use the sinusoidal formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * baseMultiplier * oscillation * LevelUpMultiplier);
            }
        }

        // Stepped leveling formula (jumps in difficulty at certain thresholds)
        private void SteppedLevelUp()
        {
            // Creates plateaus of difficulty with sudden jumps
            int stepSize = 5; // How many levels before a difficulty jump
            float baseMultiplier = 1.05f;
            float jumpMultiplier = 1.5f;

            // Apply levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use base values
                if (currentLevel % stepSize == 0)
                {
                    // Big jump at step thresholds
                    requiredExperience = Mathf.RoundToInt(requiredExperience * jumpMultiplier);
                }
                else
                {
                    // Small increase within steps
                    requiredExperience = Mathf.RoundToInt(requiredExperience * baseMultiplier);
                }
            }
            else
            {
                // Use the stepped formula with the specified multiplier
                if (currentLevel % stepSize == 0)
                {
                    // Big jump at step thresholds, affected by levelUpMultiplier
                    requiredExperience = Mathf.RoundToInt(requiredExperience * jumpMultiplier * LevelUpMultiplier);
                }
                else
                {
                    // Small increase within steps, also affected by levelUpMultiplier
                    requiredExperience = Mathf.RoundToInt(requiredExperience * baseMultiplier * LevelUpMultiplier);
                }
            }
        }

        // Logistic growth leveling formula (S-curve growth)
        private void LogisticLevelUp()
        {
            // Logistic growth creates an S-curve where growth is slow at first, then accelerates, then slows down again
            float k = 0.5f; // Growth rate
            float midpoint = MaxLevel / 2.0f; // Point of maximum growth
            float logisticValue = 1.0f / (1.0f + Mathf.Exp(-k * (currentLevel - midpoint)));

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal growth rate
                // that still reflects the S-curve shape
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + logisticValue * 0.05f));
            }
            else
            {
                // Use the logistic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + logisticValue * LevelUpMultiplier));
            }
        }

        // Inverse logarithmic leveling formula
        private void InverseLogarithmicLevelUp()
        {
            // Inverse of logarithmic - starts slow, accelerates over time
            float baseValue = requiredExperience;
            float factor = Mathf.Exp(currentLevel / 10.0f) / 10.0f;

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal growth rate
                // that still reflects the inverse logarithmic curve shape
                requiredExperience = Mathf.RoundToInt(baseValue * (1 + factor * 0.05f));
            }
            else
            {
                // Use the inverse logarithmic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(baseValue * (1 + factor * LevelUpMultiplier));
            }
        }

        // Sawtooth leveling formula
        private void SawtoothLevelUp()
        {
            // Creates a sawtooth pattern where difficulty increases linearly then drops
            int cycleLength = 5; // Length of each sawtooth cycle
            float cycleProgress = currentLevel % cycleLength;
            float normalizedProgress = cycleProgress / cycleLength;

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal sawtooth pattern
                // with a small multiplier to ensure progression
                float baseIncrease = 1.02f;
                requiredExperience = Mathf.RoundToInt(requiredExperience * (baseIncrease + normalizedProgress * 0.05f));
            }
            else
            {
                // Linear increase within each cycle using the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + normalizedProgress * LevelUpMultiplier));
            }
        }

        // Harmonic leveling formula
        private void HarmonicLevelUp()
        {
            // Uses harmonic series (1/1 + 1/2 + 1/3 + ...) for a gradually decreasing growth rate
            // For efficiency, we use an approximation for high levels instead of calculating the full sum
            float harmonicSum;

            if (currentLevel <= 30)
            {
                // For lower levels, calculate the exact sum
                harmonicSum = 0;
                for (int i = 1; i <= currentLevel; i++)
                {
                    harmonicSum += 1.0f / i;
                }
            }
            else
            {
                // For higher levels, use the approximation: H_n ≈ ln(n) + γ
                // where γ is the Euler-Mascheroni constant (approximately 0.57721)
                harmonicSum = Mathf.Log(currentLevel) + 0.57721f;
            }

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier > 0.01f)
            {
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + harmonicSum * 0.1f * LevelUpMultiplier));
            }
            else
            {
                requiredExperience = Mathf.RoundToInt(requiredExperience * (1 + harmonicSum * 0.1f));
            }
        }

        // Cyclical leveling formula with multiple frequencies
        private void CyclicalLevelUp()
        {
            // Combines multiple sine waves with different frequencies for complex patterns
            float primaryWave = Mathf.Sin(currentLevel * 0.5f * Mathf.PI);
            float secondaryWave = Mathf.Sin(currentLevel * 0.2f * Mathf.PI) * 0.5f;
            float combinedWave = (primaryWave + secondaryWave) / 1.5f; // Normalize

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure cyclical pattern
                // with a small base increase to ensure progression
                float baseIncrease = 1.02f;
                float cyclicalComponent = 1 + Mathf.Abs(combinedWave) * 0.05f; // Small cyclical variation
                requiredExperience = Mathf.RoundToInt(requiredExperience * baseIncrease * cyclicalComponent);
            }
            else
            {
                // Ensure the multiplier is always positive when levelUpMultiplier is non-zero
                float multiplier = 1 + Mathf.Abs(combinedWave) * LevelUpMultiplier;
                requiredExperience = Mathf.RoundToInt(requiredExperience * multiplier);
            }
        }

        // Diminishing returns leveling formula
        private void DiminishingReturnsLevelUp()
        {
            // As levels increase, the percentage increase in required experience gradually diminishes
            float diminishingFactor = 1.0f / (1.0f + 0.05f * currentLevel);

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal growth rate
                // that still reflects the diminishing returns curve shape
                float minMultiplier = 1.02f;
                float minEffectiveMultiplier = minMultiplier * diminishingFactor;
                // Ensure we have at least some increase
                minEffectiveMultiplier = Mathf.Max(1.01f, minEffectiveMultiplier);
                requiredExperience = Mathf.RoundToInt(requiredExperience * minEffectiveMultiplier);
            }
            else
            {
                // Use the diminishing returns formula with the specified multiplier
                float effectiveMultiplier = 1 + (LevelUpMultiplier - 1) * diminishingFactor;
                requiredExperience = Mathf.RoundToInt(requiredExperience * effectiveMultiplier);
            }
        }

        // Asymptotic leveling formula - approaches a limit as levels increase
        private void AsymptoticLevelUp()
        {
            // Uses a formula that approaches a limit as level increases
            float baseValue = requiredExperience;
            float maxMultiplier = 1.5f; // Maximum multiplier we'll approach
            float rate = 0.1f; // How quickly we approach the maximum

            // Calculate a multiplier that approaches maxMultiplier as level increases
            float curveMultiplier = maxMultiplier * (1 - Mathf.Exp(-rate * currentLevel));

            // Ensure we have at least some increase
            curveMultiplier = Mathf.Max(1.01f, curveMultiplier);

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the curve multiplier
                requiredExperience = Mathf.RoundToInt(baseValue * curveMultiplier);
            }
            else
            {
                // Use the asymptotic formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(baseValue * curveMultiplier * LevelUpMultiplier);
            }
        }

        // Tiered leveling formula - progression with distinct tiers
        private void TieredLevelUp()
        {
            // Define tiers with different multipliers
            int tier1End = 10;
            int tier2End = 20;
            int tier3End = 30;

            float tier1Multiplier = 1.05f;
            float tier2Multiplier = 1.10f;
            float tier3Multiplier = 1.15f;
            float tier4Multiplier = 1.20f;

            // Apply multiplier based on current tier
            float tierMultiplier;

            if (currentLevel < tier1End)
            {
                tierMultiplier = tier1Multiplier;
            }
            else if (currentLevel < tier2End)
            {
                tierMultiplier = tier2Multiplier;
            }
            else if (currentLevel < tier3End)
            {
                tierMultiplier = tier3Multiplier;
            }
            else
            {
                tierMultiplier = tier4Multiplier;
            }

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the tier multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * tierMultiplier);
            }
            else
            {
                // Use the tiered formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * tierMultiplier * LevelUpMultiplier);
            }
        }

        // Accelerating leveling formula - starts slow, then speeds up
        private void AcceleratingLevelUp()
        {
            // Multiplier increases with level
            float baseMultiplier = 1.02f;
            float accelerationFactor = 0.01f; // How quickly the multiplier increases

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal acceleration
                // that still reflects the accelerating curve shape
                float minAcceleration = 0.002f;
                float minEffectiveMultiplier = baseMultiplier + (minAcceleration * currentLevel);
                requiredExperience = Mathf.RoundToInt(requiredExperience * minEffectiveMultiplier);
            }
            else
            {
                // Scale the acceleration factor by the levelUpMultiplier
                float scaledAcceleration = accelerationFactor * LevelUpMultiplier;
                float effectiveMultiplier = baseMultiplier + (scaledAcceleration * currentLevel);
                requiredExperience = Mathf.RoundToInt(requiredExperience * effectiveMultiplier);
            }
        }

        // Decelerating leveling formula - starts fast, then slows down
        private void DeceleratingLevelUp()
        {
            // Multiplier decreases with level but never below a minimum
            float initialMultiplier = 1.2f;
            float minMultiplier = 1.02f;
            float decelerationFactor = 0.01f; // How quickly the multiplier decreases

            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a minimal deceleration
                // that still reflects the decelerating curve shape
                float minInitialMultiplier = 1.05f;
                float minDeceleration = 0.002f;
                float minEffectiveMultiplier = Mathf.Max(minMultiplier, minInitialMultiplier - (minDeceleration * currentLevel));
                requiredExperience = Mathf.RoundToInt(requiredExperience * minEffectiveMultiplier);
            }
            else
            {
                // Scale the initial multiplier and deceleration factor by the levelUpMultiplier
                float scaledInitialMultiplier = 1.0f + (initialMultiplier - 1.0f) * LevelUpMultiplier;
                float scaledDeceleration = decelerationFactor * LevelUpMultiplier;
                float effectiveMultiplier = Mathf.Max(minMultiplier, scaledInitialMultiplier - (scaledDeceleration * currentLevel));
                requiredExperience = Mathf.RoundToInt(requiredExperience * effectiveMultiplier);
            }
        }

        // Seasonal leveling formula - cycles between easy and hard periods
        private void SeasonalLevelUp()
        {
            // Define season length and multipliers
            int seasonLength = 5;
            float easySeasonMultiplier = 1.05f;
            float hardSeasonMultiplier = 1.15f;

            // Determine if we're in an easy or hard season
            bool isEasySeason = (currentLevel / seasonLength) % 2 == 0;

            // Select the appropriate season multiplier
            float seasonMultiplier = isEasySeason ? easySeasonMultiplier : hardSeasonMultiplier;

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the season multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * seasonMultiplier);
            }
            else
            {
                // Use the seasonal formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * seasonMultiplier * LevelUpMultiplier);
            }
        }

        // Milestone leveling formula - big jumps at milestone levels
        private void MilestoneLevelUp()
        {
            // Define milestone intervals and multipliers
            int milestoneInterval = 5;
            float normalMultiplier = 1.05f;
            float milestoneMultiplier = 1.25f;

            // Check if current level is a milestone
            bool isMilestone = currentLevel % milestoneInterval == 0;

            // Select the appropriate multiplier
            float selectedMultiplier = isMilestone ? milestoneMultiplier : normalMultiplier;

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the milestone multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * selectedMultiplier);
            }
            else
            {
                // Use the milestone formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * selectedMultiplier * LevelUpMultiplier);
            }
        }

        // Adaptive leveling formula - adjusts based on current level vs max level
        private void AdaptiveLevelUp()
        {
            // Calculate progress through total levels (0 to 1)
            float progressRatio = (float)currentLevel / MaxLevel;

            // Define different phases with different multipliers
            float earlyGameMultiplier = 1.05f;
            float midGameMultiplier = 1.10f;
            float lateGameMultiplier = 1.15f;
            float endGameMultiplier = 1.08f; // Eases off at the very end

            // Select the appropriate phase multiplier
            float phaseMultiplier;
            if (progressRatio < 0.25f)
            {
                phaseMultiplier = earlyGameMultiplier;
            }
            else if (progressRatio < 0.5f)
            {
                phaseMultiplier = midGameMultiplier;
            }
            else if (progressRatio < 0.75f)
            {
                phaseMultiplier = lateGameMultiplier;
            }
            else
            {
                phaseMultiplier = endGameMultiplier;
            }

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use just the phase multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * phaseMultiplier);
            }
            else
            {
                // Use the adaptive formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * phaseMultiplier * LevelUpMultiplier);
            }
        }

        // Fractional leveling formula - uses fractional powers for a unique curve
        private void FractionalLevelUp()
        {
            // Uses a fractional power between 1 and 2 for a unique growth curve
            float baseFactor = 1.5f;
            float fractionalPower = 1.3f; // Between linear (1.0) and quadratic (2.0)

            // Prevent division by zero and ensure a minimum multiplier
            float safeDivisor = Mathf.Max(1.0f, currentLevel);
            float powerFactor = fractionalPower / safeDivisor;

            // Ensure we have a minimum multiplier to prevent stagnation at high levels
            powerFactor = Mathf.Max(powerFactor, 0.05f);

            // Apply the levelUpMultiplier if it's greater than 0
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a reduced base factor
                // to ensure a more gentle progression while maintaining the curve shape
                float reducedBaseFactor = 1.1f;
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(reducedBaseFactor, powerFactor));
            }
            else
            {
                // Use the fractional power formula with the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * Mathf.Pow(baseFactor, powerFactor) * LevelUpMultiplier);
            }
        }

        // Custom leveling formula
        private void CustomLevelUp()
        {
            // This is a placeholder for custom implementations
            // By default, we'll use a simple linear progression
            if (LevelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a small fixed multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * 1.05f);
            }
            else
            {
                // Use the specified multiplier
                requiredExperience = Mathf.RoundToInt(requiredExperience * LevelUpMultiplier);
            }

            // Developers should override this method with their own custom formula
            Debug.LogWarning("Using default implementation for Custom leveling. Override this method with your own formula.");
        }

        #endregion Level Up Formulas

        #region Save Load

        /// <summary>
        /// Save the User Data for the leveling system to file
        /// </summary>
        /// <param name="fileName"></param>
        public void Save(string fileName)
        {
            Debug.Log($"Saving:{fileName}");
            FileSystem.SaveData("Level", CurrentLevel, fileName);
            FileSystem.SaveData("CurExp", CurrentExperience, fileName);
            FileSystem.SaveData("ReqExp", RequiredExperience, fileName);

            FileSystem.SaveData("MaxLevel", MaxLevel, fileName);
            FileSystem.SaveData("LevelUpMultiplier", LevelUpMultiplier, fileName);

            // Save algorithm ID if available
            if (LevelingAlgorithm != null)
            {
                FileSystem.SaveData("AlgorithmID", LevelingAlgorithm.uniqueID, fileName);
            }
        }

        /// <summary>
        /// Load The User Data for the leveling system from file
        /// </summary>
        /// <param name="fileName"></param>
        public void Load(string fileName)
        {
            // Load and display data
            Data data = FileSystem.LoadData(fileName);
            if (data != null)
            {
                Debug.Log($"Loading:{fileName}");
                CurrentLevel = data.Get<int>("Level");
                CurrentExperience = data.Get<int>("CurExp");
                RequiredExperience = data.Get<int>("ReqExp");

                // Update the ScriptableObject data
                int maxLevel = data.Get<int>("MaxLevel");
                float levelUpMultiplier = data.Get<float>("LevelUpMultiplier");

                // Try to load algorithm by ID
                LevelingAlgorithmBase algorithm = null;
                string algorithmID = data.Get<string>("AlgorithmID");

                if (!string.IsNullOrEmpty(algorithmID))
                {
                    // Try to find the algorithm in the database
                    AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                    if (database != null)
                    {
                        algorithm = database.GetAlgorithmByID(algorithmID);
                    }
                }

                // Update the ScriptableObject
                UpdateLevelingSystemData(maxLevel, levelUpMultiplier, algorithm);

                OnExpModified?.Invoke(CurrentExperience, RequiredExperience);
                OnEXPUIUpdate?.Invoke(CurrentExperience, RequiredExperience);
                OnLevelUp -= PlayLevelUpAudio;
                OnLevelUp?.Invoke(CurrentLevel);
                OnLevelUp += PlayLevelUpAudio;
            }
            else
                Debug.LogError($"No Saved Data Named: {fileName}");
        }

        #endregion Save Load
    }
}