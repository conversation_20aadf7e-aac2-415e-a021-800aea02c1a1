using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Enums for algorithm types and interpolation methods
    /// </summary>
    public static class AlgorithmEnums
    {
        /// <summary>
        /// The type of algorithm to use
        /// </summary>
        public enum AlgorithmType
        {
            DrawnPattern,
            CodedFormula
        }

        /// <summary>
        /// Interpolation method for the drawn pattern
        /// </summary>
        public enum InterpolationMethod
        {
            Linear,
            Cubic
        }
    }
}
