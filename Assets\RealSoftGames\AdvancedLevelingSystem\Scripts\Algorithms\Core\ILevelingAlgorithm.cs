using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Interface for all leveling algorithms
    /// </summary>
    public interface ILevelingAlgorithm
    {
        /// <summary>
        /// Gets the name of the algorithm
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the difficulty rating of the algorithm
        /// </summary>
        DifficultyRating DifficultyRating { get; }

        /// <summary>
        /// Gets the description of the algorithm
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Gets the formula explanation for the algorithm
        /// </summary>
        string FormulaExplanation { get; }

        /// <summary>
        /// Calculates the next experience requirement based on the current level and experience
        /// </summary>
        /// <param name="currentExperience">Current experience requirement</param>
        /// <param name="currentLevel">Current level</param>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <returns>The next experience requirement</returns>
        int CalculateNextRequirement(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel);

        /// <summary>
        /// Calculates a sequence of experience requirements for visualization
        /// </summary>
        /// <param name="startingExperience">Starting experience requirement</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        /// <returns>List of experience requirements for each level</returns>
        List<int> CalculateRequirementCurve(int startingExperience, int startingLevel, int maxLevel, float levelUpMultiplier);

        /// <summary>
        /// Calculates experience requirements for all levels
        /// </summary>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        /// <param name="startingExperience">Starting experience requirement</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <returns>List of experience requirements for each level</returns>
        List<int> CalculateExperienceRequirements(float levelUpMultiplier, int startingExperience, int startingLevel, int maxLevel);


        /// <summary>
        /// Calculates raw formula values for visualization in raw mode
        /// This shows the pattern of level progression without the cumulative effect
        /// </summary>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        /// <returns>List of raw formula values for each level</returns>
        List<float> CalculateRawFormulaCurve(int startingLevel, int maxLevel, float levelUpMultiplier);

        /// <summary>
        /// Pre-calculates and caches points for efficient graph drawing
        /// </summary>
        /// <param name="startingExperience">Starting experience requirement</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        void PreCalculatePoints(int startingExperience = 250, int startingLevel = 1, int maxLevel = 50, float levelUpMultiplier = 1.1f);

        /// <summary>
        /// Gets the pre-calculated requirement curve points for graph drawing
        /// </summary>
        List<Vector2> GetRequirementCurvePoints();

        /// <summary>
        /// Gets the pre-calculated raw formula curve points for graph drawing
        /// </summary>
        List<Vector2> GetRawFormulaCurvePoints();
    }
}
