using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Base class for all leveling algorithms
    /// Provides common functionality and implements the ILevelingAlgorithm interface
    /// </summary>
    [CreateAssetMenu(fileName = "New Leveling Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Leveling Algorithm", order = 1)]
    public class LevelingAlgorithmBase : ScriptableObject, ILevelingAlgorithm
    {
        #region Algorithm Type
        [Tooltip("The type of algorithm to use")]
        [SerializeField] public AlgorithmEnums.AlgorithmType algorithmType = AlgorithmEnums.AlgorithmType.DrawnPattern;
        #endregion
        #region Basic Properties
        [Tooltip("Unique identifier for the algorithm")]
        [SerializeField] public string uniqueID = System.Guid.NewGuid().ToString();

        [Tooltip("The name of the algorithm")]
        [SerializeField] public string algorithmName = "New Algorithm";

        [Tooltip("The description of the algorithm")]
        [TextArea(3, 5)]
        [SerializeField] public string description = "Algorithm description.";

        [Tooltip("The formula explanation for the algorithm")]
        [TextArea(3, 5)]
        [SerializeField] public string formulaExplanation = "Formula explanation.";

        [Tooltip("The difficulty rating for this algorithm (determines star display)")]
        [SerializeField] public DifficultyRating difficultyRating;
        #endregion

        #region Drawn Pattern Properties
        [Tooltip("The points that define the pattern")]
        [SerializeField] public List<Vector2> points = new List<Vector2>();

        [Tooltip("The interpolation method to use")]
        [SerializeField] public AlgorithmEnums.InterpolationMethod interpolationMethod = AlgorithmEnums.InterpolationMethod.Linear;
        #endregion

        #region Coded Formula Properties
        [Tooltip("Base multiplier for the algorithm")]
        [SerializeField] public float baseMultiplier = 1.1f;

        [Tooltip("Level multiplier factor (how much the level affects the multiplier)")]
        [SerializeField] public float levelMultiplierFactor = 0.01f;

        [Tooltip("Use sine wave modulation")]
        [SerializeField] public bool useSineWave = false;

        [Tooltip("Sine wave amplitude")]
        [SerializeField] public float sineAmplitude = 0.1f;

        [Tooltip("Sine wave period (in levels)")]
        [SerializeField] public float sinePeriod = 5f;

        [Tooltip("Use heartbeat pattern")]
        [SerializeField] public bool useHeartbeat = false;

        [Tooltip("Heartbeat amplitude")]
        [SerializeField] public float heartbeatAmplitude = 0.15f;

        [Tooltip("Heartbeat period (in levels)")]
        [SerializeField] public float heartbeatPeriod = 10f;

        [Tooltip("Use zigzag pattern")]
        [SerializeField] public bool useZigzag = false;

        [Tooltip("Zigzag amplitude")]
        [SerializeField] public float zigzagAmplitude = 0.1f;

        [Tooltip("Zigzag period (in levels)")]
        [SerializeField] public float zigzagPeriod = 3f;

        [Tooltip("Use random fluctuations")]
        [SerializeField] public bool useRandomFluctuations = false;

        [Tooltip("Random fluctuation amplitude")]
        [SerializeField] public float randomAmplitude = 0.05f;

        [Tooltip("Random seed for reproducible results")]
        [SerializeField] public int randomSeed = 12345;
        #endregion

        #region Cached Values
        // Cached values for performance
        [HideInInspector] [SerializeField] protected int cachedStartingExperience = 250;
        [HideInInspector] [SerializeField] protected int cachedStartingLevel = 1;
        [HideInInspector] [SerializeField] protected int cachedMaxLevel = 50;
        [HideInInspector] [SerializeField] protected float cachedLevelUpMultiplier = 1.1f;

        // Cached points for graph drawing
        [HideInInspector] [SerializeField] protected List<Vector2> cachedRequirementCurvePoints = new();
        [HideInInspector] [SerializeField] protected List<Vector2> cachedRawFormulaCurvePoints = new();

        // Original data lists (for calculations)
        [HideInInspector] [SerializeField] protected List<int> cachedRequirementCurve = new();
        [HideInInspector] [SerializeField] protected List<float> cachedRawFormulaCurve = new();
        #endregion

        #region ILevelingAlgorithm Implementation
        /// <summary>
        /// Gets the name of the algorithm
        /// </summary>
        public string Name => algorithmName;

        /// <summary>
        /// Gets the difficulty rating of the algorithm
        /// </summary>
        public DifficultyRating DifficultyRating => difficultyRating;

        /// <summary>
        /// Gets the description of the algorithm
        /// </summary>
        public string Description => description;

        /// <summary>
        /// Gets the formula explanation for the algorithm
        /// </summary>
        public string FormulaExplanation => formulaExplanation;

        /// <summary>
        /// Calculates the next experience requirement based on the current level and experience
        /// </summary>
        public int CalculateNextRequirement(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Choose the appropriate calculation method based on the algorithm type
            if (algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
            {
                return CalculateNextRequirementDrawnPattern(currentExperience, currentLevel, levelUpMultiplier, startingLevel, maxLevel);
            }
            else
            {
                return CalculateNextRequirementCodedFormula(currentExperience, currentLevel, levelUpMultiplier, startingLevel, maxLevel);
            }
        }

        /// <summary>
        /// Calculates a sequence of experience requirements for visualization
        /// </summary>
        public List<int> CalculateRequirementCurve(int startingExperience, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            return CalculateExperienceRequirements(levelUpMultiplier, startingExperience, startingLevel, maxLevel);
        }

        /// <summary>
        /// Calculates experience requirements for all levels
        /// </summary>
        public List<int> CalculateExperienceRequirements(float levelUpMultiplier, int startingExperience, int startingLevel, int maxLevel)
        {
            List<int> requirements = new List<int>();
            int currentExperience = startingExperience;

            // Calculate requirements for each level
            for (int level = startingLevel; level < maxLevel; level++)
            {
                int nextRequirement = CalculateNextRequirement(currentExperience, level, levelUpMultiplier, startingLevel, maxLevel);
                requirements.Add(nextRequirement);
                currentExperience = nextRequirement;
            }

            // Cache the results
            cachedRequirementCurve = new List<int>(requirements);
            cachedStartingExperience = startingExperience;
            cachedStartingLevel = startingLevel;
            cachedMaxLevel = maxLevel;
            cachedLevelUpMultiplier = levelUpMultiplier;

            return requirements;
        }

        /// <summary>
        /// Calculates raw formula values for visualization in raw mode
        /// </summary>
        public List<float> CalculateRawFormulaCurve(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            // Choose the appropriate calculation method based on the algorithm type
            if (algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
            {
                return CalculateRawFormulaCurveDrawnPattern(startingLevel, maxLevel, levelUpMultiplier);
            }
            else
            {
                return CalculateRawFormulaCurveCodedFormula(startingLevel, maxLevel, levelUpMultiplier);
            }
        }

        /// <summary>
        /// Pre-calculates and caches points for efficient graph drawing
        /// </summary>
        public virtual void PreCalculatePoints(int startingExperience = 250, int startingLevel = 1, int maxLevel = 50, float levelUpMultiplier = 1.1f)
        {
            // Clear existing cached points
            cachedRequirementCurvePoints.Clear();
            cachedRawFormulaCurvePoints.Clear();

            // Calculate the requirement curve
            List<int> requirementCurve = CalculateExperienceRequirements(levelUpMultiplier, startingExperience, startingLevel, maxLevel);
            for (int i = 0; i < requirementCurve.Count; i++)
            {
                cachedRequirementCurvePoints.Add(new Vector2(startingLevel + i + 1, requirementCurve[i]));
            }

            // Calculate the raw formula curve
            List<float> rawFormulaCurve = CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);
            for (int i = 0; i < rawFormulaCurve.Count; i++)
            {
                cachedRawFormulaCurvePoints.Add(new Vector2(startingLevel + i, rawFormulaCurve[i]));
            }

            // Cache the parameters
            cachedStartingExperience = startingExperience;
            cachedStartingLevel = startingLevel;
            cachedMaxLevel = maxLevel;
            cachedLevelUpMultiplier = levelUpMultiplier;
        }

        /// <summary>
        /// Gets the pre-calculated requirement curve points for graph drawing
        /// </summary>
        public virtual List<Vector2> GetRequirementCurvePoints()
        {
            return cachedRequirementCurvePoints;
        }

        /// <summary>
        /// Gets the pre-calculated raw formula curve points for graph drawing
        /// </summary>
        public virtual List<Vector2> GetRawFormulaCurvePoints()
        {
            return cachedRawFormulaCurvePoints;
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Gets an effective multiplier that respects the levelUpMultiplier setting
        /// </summary>
        public virtual float GetEffectiveMultiplier(float levelUpMultiplier)
        {
            // If the multiplier is 0, use 1.0 (no change)
            if (Mathf.Approximately(levelUpMultiplier, 0f))
                return 1.0f;

            // For multipliers between 0 and 1, we still want them to have an effect
            // Values below 1 should make the curve less steep (slower progression)
            // Values above 1 should make the curve more steep (faster progression)
            return levelUpMultiplier;
        }

        /// <summary>
        /// Normalizes a level to a 0-1 range
        /// </summary>
        protected float NormalizeLevel(int level, int startingLevel, int maxLevel)
        {
            if (maxLevel <= startingLevel)
                return 0;

            return (level - startingLevel) / (float)(maxLevel - startingLevel);
        }

        /// <summary>
        /// Clears all cached values to force recalculation
        /// </summary>
        public virtual void ClearCachedValues()
        {

            // Clear all cached lists
            cachedRequirementCurve.Clear();
            cachedRawFormulaCurve.Clear();
            cachedRequirementCurvePoints.Clear();
            cachedRawFormulaCurvePoints.Clear();

            // Reset cached parameters to default values
            cachedStartingExperience = 250;
            cachedStartingLevel = 1;
            cachedMaxLevel = 50;
            cachedLevelUpMultiplier = 1.1f;

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }

        #region Drawn Pattern Implementation
        /// <summary>
        /// Calculates the next experience requirement using the drawn pattern method
        /// </summary>
        protected int CalculateNextRequirementDrawnPattern(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            if (points.Count < 2)
                return currentExperience + 100; // Default increase if no points

            // Normalize the current level to a 0-1 range for interpolation
            float normalizedLevel = NormalizeLevel(currentLevel, startingLevel, maxLevel);

            // Get the multiplier from the pattern
            float multiplier = EvaluatePattern(normalizedLevel);

            // Apply the level up multiplier
            multiplier *= GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * multiplier);

            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the drawn pattern method
        /// </summary>
        protected List<float> CalculateRawFormulaCurveDrawnPattern(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Validate points list
            if (points == null || points.Count < 2)
            {
                // Ensure we have valid points
                Validate();

                // If still invalid after validation, use default values
                if (points == null || points.Count < 2)
                {
                    // Default values if no points
                    for (int level = startingLevel; level <= maxLevel; level++)
                    {
                        rawValues.Add(1.1f);
                    }
                    return rawValues;
                }
            }

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Normalize the level to a 0-1 range
                float normalizedLevel = NormalizeLevel(level, startingLevel, maxLevel);

                // Get the multiplier from the pattern
                float rawValue = EvaluatePattern(normalizedLevel);

                // Apply the level up multiplier
                rawValue *= GetEffectiveMultiplier(levelUpMultiplier);

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }

        /// <summary>
        /// Evaluates the pattern at a normalized level (0-1)
        /// </summary>
        protected float EvaluatePattern(float normalizedLevel)
        {
            // Validate points list
            if (points == null || points.Count < 2)
            {
                // Ensure we have valid points
                Validate();

                // If still invalid after validation, return default
                if (points == null || points.Count < 2)
                    return 1.1f; // Default multiplier if no points
            }

            // Sort points by X value
            List<Vector2> sortedPoints = new List<Vector2>(points);
            sortedPoints.Sort((a, b) => a.x.CompareTo(b.x));

            // Clamp normalized level to the range of the points
            normalizedLevel = Mathf.Clamp(normalizedLevel, sortedPoints[0].x, sortedPoints[sortedPoints.Count - 1].x);

            // Find the two points to interpolate between
            int index = 1;
            while (index < sortedPoints.Count && sortedPoints[index].x < normalizedLevel)
            {
                index++;
            }

            // Get the two points
            Vector2 p0 = sortedPoints[index - 1];
            Vector2 p1 = sortedPoints[index];

            // Calculate the interpolation factor
            float t = (normalizedLevel - p0.x) / (p1.x - p0.x);

            // Interpolate between the two points
            if (interpolationMethod == AlgorithmEnums.InterpolationMethod.Linear)
            {
                // Linear interpolation
                return Mathf.Lerp(p0.y, p1.y, t);
            }
            else
            {
                // Cubic interpolation
                // Get additional points for cubic interpolation
                Vector2 p_1 = index > 1 ? sortedPoints[index - 2] : p0 - (p1 - p0);
                Vector2 p2 = index < sortedPoints.Count - 1 ? sortedPoints[index + 1] : p1 + (p1 - p0);

                // Cubic interpolation
                return CubicInterpolate(p_1.y, p0.y, p1.y, p2.y, t);
            }
        }

        /// <summary>
        /// Performs cubic interpolation between four points
        /// </summary>
        protected float CubicInterpolate(float y0, float y1, float y2, float y3, float t)
        {
            float a = -0.5f * y0 + 1.5f * y1 - 1.5f * y2 + 0.5f * y3;
            float b = y0 - 2.5f * y1 + 2f * y2 - 0.5f * y3;
            float c = -0.5f * y0 + 0.5f * y2;
            float d = y1;

            return a * t * t * t + b * t * t + c * t + d;
        }
        #endregion

        #region Coded Formula Implementation
        /// <summary>
        /// Calculates the next experience requirement using the coded formula method
        /// </summary>
        protected virtual int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Calculate the base multiplier with level factor
            float multiplier = baseMultiplier;

            // Add level factor if not zero
            if (Mathf.Abs(levelMultiplierFactor) > 0.001f)
            {
                multiplier += levelMultiplierFactor * (currentLevel - startingLevel);
            }

            // Apply sine wave modulation if enabled
            if (useSineWave && sinePeriod > 0)
            {
                multiplier += sineAmplitude * Mathf.Sin(currentLevel * Mathf.PI / sinePeriod);
            }

            // Apply heartbeat pattern if enabled
            if (useHeartbeat && heartbeatPeriod > 0)
            {
                multiplier += heartbeatAmplitude * Mathf.Abs(Mathf.Sin(currentLevel * Mathf.PI / heartbeatPeriod));
            }

            // Apply zigzag pattern if enabled
            if (useZigzag && zigzagPeriod > 0)
            {
                // Zigzag is implemented as a triangle wave
                float phase = (currentLevel % zigzagPeriod) / zigzagPeriod;
                float triangleWave = phase < 0.5f ? phase * 2 : 2 - phase * 2;
                multiplier += zigzagAmplitude * (triangleWave * 2 - 1); // Range -1 to 1
            }

            // Apply random fluctuations if enabled
            if (useRandomFluctuations)
            {
                // Use a deterministic random based on level and seed
                System.Random random = new System.Random(randomSeed + currentLevel);
                float randomValue = (float)random.NextDouble() * 2 - 1; // Range -1 to 1
                multiplier += randomAmplitude * randomValue;
            }

            // Apply the level up multiplier
            multiplier *= GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * multiplier);

            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the coded formula method
        /// </summary>
        protected virtual List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the base multiplier with level factor
                float rawValue = baseMultiplier;

                // Add level factor if not zero
                if (Mathf.Abs(levelMultiplierFactor) > 0.001f)
                {
                    rawValue += levelMultiplierFactor * (level - startingLevel);
                }

                // Apply sine wave modulation if enabled
                if (useSineWave && sinePeriod > 0)
                {
                    rawValue += sineAmplitude * Mathf.Sin(level * Mathf.PI / sinePeriod);
                }

                // Apply heartbeat pattern if enabled
                if (useHeartbeat && heartbeatPeriod > 0)
                {
                    rawValue += heartbeatAmplitude * Mathf.Abs(Mathf.Sin(level * Mathf.PI / heartbeatPeriod));
                }

                // Apply zigzag pattern if enabled
                if (useZigzag && zigzagPeriod > 0)
                {
                    // Zigzag is implemented as a triangle wave
                    float phase = (level % zigzagPeriod) / zigzagPeriod;
                    float triangleWave = phase < 0.5f ? phase * 2 : 2 - phase * 2;
                    rawValue += zigzagAmplitude * (triangleWave * 2 - 1); // Range -1 to 1
                }

                // Apply random fluctuations if enabled
                if (useRandomFluctuations)
                {
                    // Use a deterministic random based on level and seed
                    System.Random random = new System.Random(randomSeed + level);
                    float randomValue = (float)random.NextDouble() * 2 - 1; // Range -1 to 1
                    rawValue += randomAmplitude * randomValue;
                }

                // Apply the level up multiplier
                rawValue *= effectiveMultiplier;

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
        #endregion

        /// <summary>
        /// Validates the algorithm data to ensure it's usable
        /// </summary>
        public virtual bool Validate()
        {
            // Base validation
            if (string.IsNullOrEmpty(algorithmName))
            {
                Debug.LogWarning("Algorithm name is empty. Setting to 'Unnamed Algorithm'.");
                algorithmName = "Unnamed Algorithm";
            }

            // Validate drawn pattern data if using that algorithm type
            if (algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
            {
                // Ensure we have at least two points
                if (points == null || points.Count < 2)
                {
                    Debug.LogWarning($"DrawnPatternAlgorithm '{algorithmName}' requires at least two points. Adding default points.");

                    // Initialize points list if it's null
                    if (points == null)
                    {
                        points = new List<Vector2>();
                    }
                    else
                    {
                        points.Clear();
                    }

                    // Add default points
                    points.Add(new Vector2(0.0f, 1.1f));
                    points.Add(new Vector2(1.0f, 1.3f));
                }

                // Sort points by X value
                points.Sort((a, b) => a.x.CompareTo(b.x));
            }

            return true;
        }

        /// <summary>
        /// Called when the scriptable object is created or enabled
        /// </summary>
        protected virtual void OnEnable()
        {
            // Validate the algorithm data to ensure it's usable
            Validate();

            // If the cache is empty, pre-calculate points with default values
            if (cachedRequirementCurve.Count == 0 || cachedRawFormulaCurve.Count == 0)
            {
                PreCalculatePoints(cachedStartingExperience, cachedStartingLevel, cachedMaxLevel, cachedLevelUpMultiplier);
            }

            // Register with the algorithm registry
            RegisterWithRegistry();
        }

        /// <summary>
        /// Registers this algorithm with the registry
        /// </summary>
        public virtual void RegisterWithRegistry()
        {
            // Register with the static algorithm registry
            ScriptableAlgorithmRegistry.RegisterAlgorithm(this);
        }
        #endregion
    }
}
