using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Adaptive algorithm
    /// </summary>
    public static class AdaptiveAlgorithmExtension
    {
        // Default parameters
        private const float DefaultEarlyGameMultiplier = 1.05f;
        private const float DefaultMidGameMultiplier = 1.15f;
        private const float DefaultLateGameMultiplier = 1.1f;
        private const float DefaultEarlyGameScalingFactor = 0.9f;
        private const float DefaultMidGameScalingFactor = 1.1f;
        private const float DefaultLateGameScalingFactor = 1.0f;
        
        /// <summary>
        /// Calculates the next experience requirement using the adaptive formula method
        /// </summary>
        public static int CalculateAdaptiveRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the adaptive factor
            // This creates a curve that adjusts based on progress through the levels
            float adaptiveFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure adaptive pattern
                // Start easy, get harder in the middle, then easier again at the end
                if (progressRatio < 0.3f)
                {
                    // Early game - easy progression
                    adaptiveFactor = DefaultEarlyGameMultiplier;
                }
                else if (progressRatio < 0.7f)
                {
                    // Mid game - harder progression
                    adaptiveFactor = DefaultMidGameMultiplier;
                }
                else
                {
                    // Late game - easier progression again
                    adaptiveFactor = DefaultLateGameMultiplier;
                }
            }
            else
            {
                // Scale the adaptive effect based on the levelUpMultiplier
                if (progressRatio < 0.3f)
                {
                    // Early game - easier progression
                    adaptiveFactor = effectiveMultiplier * DefaultEarlyGameScalingFactor;
                }
                else if (progressRatio < 0.7f)
                {
                    // Mid game - harder progression
                    adaptiveFactor = effectiveMultiplier * DefaultMidGameScalingFactor;
                }
                else
                {
                    // Late game - medium progression
                    adaptiveFactor = effectiveMultiplier * DefaultLateGameScalingFactor;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * adaptiveFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the adaptive formula method
        /// </summary>
        public static List<float> CalculateAdaptiveRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Calculate the adaptive factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure adaptive pattern
                    if (progressRatio < 0.3f)
                    {
                        rawValue = DefaultEarlyGameMultiplier;
                    }
                    else if (progressRatio < 0.7f)
                    {
                        rawValue = DefaultMidGameMultiplier;
                    }
                    else
                    {
                        rawValue = DefaultLateGameMultiplier;
                    }
                }
                else
                {
                    // Scale the adaptive effect based on the levelUpMultiplier
                    if (progressRatio < 0.3f)
                    {
                        rawValue = effectiveMultiplier * DefaultEarlyGameScalingFactor;
                    }
                    else if (progressRatio < 0.7f)
                    {
                        rawValue = effectiveMultiplier * DefaultMidGameScalingFactor;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultLateGameScalingFactor;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
