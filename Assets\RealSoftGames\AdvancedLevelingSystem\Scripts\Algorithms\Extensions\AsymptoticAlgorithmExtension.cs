using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Asymptotic algorithm
    /// </summary>
    public static class AsymptoticAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroMaxMultiplier = 1.5f;
        private const float DefaultMultiplierScale = 2.0f;
        private const float DefaultApproachRate = 0.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the asymptotic formula method
        /// </summary>
        public static int CalculateAsymptoticRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Define the maximum multiplier and the rate of approach
            float maxMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a fixed maximum
                maxMultiplier = DefaultZeroMaxMultiplier;
            }
            else
            {
                // Scale the maximum multiplier based on the levelUpMultiplier
                // This allows for adjustment while maintaining the asymptotic behavior
                maxMultiplier = 1f + (effectiveMultiplier - 1f) * DefaultMultiplierScale; // Scale the effect
            }
            
            // Calculate the asymptotic factor using an exponential approach
            // This creates a curve that approaches maxMultiplier as levels increase
            float approachFactor = 1f - Mathf.Exp(-DefaultApproachRate * currentLevel);
            
            // Calculate the actual multiplier with asymptotic approach
            float actualMultiplier = 1f + (maxMultiplier - 1f) * approachFactor;
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the asymptotic formula method
        /// </summary>
        public static List<float> CalculateAsymptoticRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Define the maximum multiplier and the rate of approach
            float maxMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a fixed maximum
                maxMultiplier = DefaultZeroMaxMultiplier;
            }
            else
            {
                // Scale the maximum multiplier based on the levelUpMultiplier
                maxMultiplier = 1f + (effectiveMultiplier - 1f) * DefaultMultiplierScale; // Scale the effect
            }
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the asymptotic factor
                float approachFactor = 1f - Mathf.Exp(-DefaultApproachRate * level);
                
                // Calculate the actual multiplier with asymptotic approach
                float rawValue = 1f + (maxMultiplier - 1f) * approachFactor;
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
