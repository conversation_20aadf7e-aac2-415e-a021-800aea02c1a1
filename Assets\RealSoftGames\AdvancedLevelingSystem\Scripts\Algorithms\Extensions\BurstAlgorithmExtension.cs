using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Burst algorithm
    /// </summary>
    public static class BurstAlgorithmExtension
    {
        // Default parameters
        private const int DefaultBurstCycle = 7;
        private const int DefaultBurstDuration = 2;
        private const float DefaultBaseBurstIntensity = 1.5f;
        private const float DefaultMaxBurstIntensity = 2.5f;
        private const float DefaultZeroBetweenBurstsMultiplier = 1.03f;
        private const float DefaultZeroBurstMultiplier = 1.15f;
        private const float DefaultZeroLevelProgressionFactor = 0.05f;
        private const float DefaultBetweenBurstsScale = 0.8f;
        
        /// <summary>
        /// Calculates the next experience requirement using the burst formula method
        /// </summary>
        public static int CalculateBurstRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate position in the burst cycle
            int cyclePosition = (currentLevel - startingLevel) % DefaultBurstCycle;
            bool isInBurst = cyclePosition < DefaultBurstDuration;
            
            // Calculate the burst intensity that increases with level
            float currentBurstIntensity = Mathf.Lerp(DefaultBaseBurstIntensity, DefaultMaxBurstIntensity, normalizedPosition);
            
            // Calculate the burst factor
            float burstFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure burst pattern
                // with a smaller range to avoid excessive growth
                if (isInBurst)
                {
                    // During burst, use a higher multiplier
                    burstFactor = DefaultZeroBurstMultiplier + (DefaultZeroLevelProgressionFactor * normalizedPosition); // Increases slightly with level
                }
                else
                {
                    // Between bursts, use a lower multiplier
                    burstFactor = DefaultZeroBetweenBurstsMultiplier;
                }
            }
            else
            {
                // Scale the burst effect based on the levelUpMultiplier
                if (isInBurst)
                {
                    // During burst, use a higher multiplier that increases with level
                    burstFactor = effectiveMultiplier * currentBurstIntensity;
                }
                else
                {
                    // Between bursts, use a lower multiplier
                    burstFactor = effectiveMultiplier * DefaultBetweenBurstsScale;
                }
                
                // Ensure we have at least some increase
                burstFactor = Mathf.Max(burstFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * burstFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the burst formula method
        /// </summary>
        public static List<float> CalculateBurstRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate position in the burst cycle
                int cyclePosition = (level - startingLevel) % DefaultBurstCycle;
                bool isInBurst = cyclePosition < DefaultBurstDuration;
                
                // Calculate the burst intensity that increases with level
                float currentBurstIntensity = Mathf.Lerp(DefaultBaseBurstIntensity, DefaultMaxBurstIntensity, normalizedPosition);
                
                // Calculate the burst factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure burst pattern
                    if (isInBurst)
                    {
                        rawValue = DefaultZeroBurstMultiplier + (DefaultZeroLevelProgressionFactor * normalizedPosition);
                    }
                    else
                    {
                        rawValue = DefaultZeroBetweenBurstsMultiplier;
                    }
                }
                else
                {
                    // Scale the burst effect based on the levelUpMultiplier
                    if (isInBurst)
                    {
                        rawValue = effectiveMultiplier * currentBurstIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultBetweenBurstsScale;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
