using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Cosine algorithm
    /// </summary>
    public static class CosineAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultZeroAmplitude = 0.04f;
        private const float DefaultAmplitudeScale = 0.2f;
        private const float DefaultCycleCount = 1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the cosine formula method
        /// </summary>
        public static int CalculateCosineRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Apply cosine function to create a wave pattern
            // Cosine starts at 1, goes to -1, and back to 1 over 2π
            // We'll use one cycle for the entire level range
            float cosineValue = Mathf.Cos(normalizedPosition * DefaultCycleCount * 2f * Mathf.PI);
            
            // Map the cosine value from [-1, 1] to a suitable multiplier range
            float cosineFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure cosine pattern
                // with a smaller range to avoid excessive growth
                cosineFactor = DefaultZeroBaseMultiplier + (cosineValue * DefaultZeroAmplitude);
                
                // Ensure we have at least some increase
                cosineFactor = Mathf.Max(cosineFactor, 1.01f);
            }
            else
            {
                // Scale the cosine effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float amplitude = DefaultAmplitudeScale * (effectiveMultiplier - 1.0f); // Scale amplitude with multiplier
                
                cosineFactor = baseMultiplier + (cosineValue * amplitude);
                
                // Ensure we have at least some increase
                cosineFactor = Mathf.Max(cosineFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * cosineFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the cosine formula method
        /// </summary>
        public static List<float> CalculateCosineRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression (0 to 1 range)
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Apply cosine function to create a wave pattern
                float cosineValue = Mathf.Cos(normalizedPosition * DefaultCycleCount * 2f * Mathf.PI);
                
                // Map the cosine value from [-1, 1] to a suitable multiplier range
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure cosine pattern
                    rawValue = DefaultZeroBaseMultiplier + (cosineValue * DefaultZeroAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the cosine effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float amplitude = DefaultAmplitudeScale * (effectiveMultiplier - 1.0f); // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (cosineValue * amplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
