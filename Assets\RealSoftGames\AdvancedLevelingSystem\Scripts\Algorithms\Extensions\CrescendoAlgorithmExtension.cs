using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Crescendo algorithm
    /// </summary>
    public static class CrescendoAlgorithmExtension
    {
        // Default parameters
        private const float DefaultCrescendoExponent = 2.5f;
        private const float DefaultNoteFrequency = 20f;
        private const float DefaultNoteAmplitude = 0.02f;
        private const float DefaultZeroBaseMultiplier = 1.02f;
        private const float DefaultZeroMaxGrowth = 0.2f;
        private const float DefaultBaseMultiplierScale = 0.8f;
        private const float DefaultMaxGrowthScale = 0.4f;
        
        /// <summary>
        /// Calculates the next experience requirement using the crescendo formula method
        /// </summary>
        public static int CalculateCrescendoRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the crescendo effect - starts slow, then builds dramatically
            float crescendoValue = Mathf.Pow(normalizedPosition, DefaultCrescendoExponent);
            
            // Add small oscillations to simulate musical notes in the crescendo
            float noteEffect = Mathf.Sin(normalizedPosition * DefaultNoteFrequency * Mathf.PI) * DefaultNoteAmplitude * crescendoValue;
            
            // Calculate the crescendo factor
            float crescendoFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure crescendo pattern
                // with a smaller range to avoid excessive growth
                crescendoFactor = DefaultZeroBaseMultiplier + (crescendoValue * DefaultZeroMaxGrowth) + noteEffect;
                
                // Ensure we have at least some increase
                crescendoFactor = Mathf.Max(crescendoFactor, 1.01f);
            }
            else
            {
                // Scale the crescendo effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier * DefaultBaseMultiplierScale; // Start lower
                float maxGrowth = effectiveMultiplier * DefaultMaxGrowthScale; // Maximum additional growth
                
                crescendoFactor = baseMultiplier + (crescendoValue * maxGrowth) + noteEffect;
                
                // Ensure we have at least some increase
                crescendoFactor = Mathf.Max(crescendoFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * crescendoFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the crescendo formula method
        /// </summary>
        public static List<float> CalculateCrescendoRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the crescendo effect - starts slow, then builds dramatically
                float crescendoValue = Mathf.Pow(normalizedPosition, DefaultCrescendoExponent);
                
                // Add small oscillations to simulate musical notes in the crescendo
                float noteEffect = Mathf.Sin(normalizedPosition * DefaultNoteFrequency * Mathf.PI) * DefaultNoteAmplitude * crescendoValue;
                
                // Calculate the crescendo factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure crescendo pattern
                    rawValue = DefaultZeroBaseMultiplier + (crescendoValue * DefaultZeroMaxGrowth) + noteEffect;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the crescendo effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier * DefaultBaseMultiplierScale; // Start lower
                    float maxGrowth = effectiveMultiplier * DefaultMaxGrowthScale; // Maximum additional growth
                    
                    rawValue = baseMultiplier + (crescendoValue * maxGrowth) + noteEffect;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
