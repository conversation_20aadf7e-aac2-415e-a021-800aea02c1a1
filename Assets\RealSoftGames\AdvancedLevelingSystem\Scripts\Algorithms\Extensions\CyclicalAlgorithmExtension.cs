using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Cyclical algorithm
    /// </summary>
    public static class CyclicalAlgorithmExtension
    {
        // Default parameters
        private const float DefaultPrimaryFrequency = 6.0f;
        private const float DefaultSecondaryFrequency = 3.0f;
        private const float DefaultSecondaryAmplitudeScale = 0.5f;
        private const float DefaultNormalizationFactor = 1.5f;
        private const float DefaultAmplificationFactor = 1.5f;
        private const float DefaultMaxAmplitude = 0.5f;
        private const float DefaultZeroBaseMultiplier = 1.02f;
        private const float DefaultZeroVariationScale = 0.3f;
        private const float DefaultVariationScale = 0.5f;
        
        /// <summary>
        /// Calculates the next experience requirement using the cyclical formula method
        /// </summary>
        public static int CalculateCyclicalRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Create a more pronounced cyclical pattern for better visualization
            // Use normalized level position to ensure consistent cycles across different level ranges
            float normalizedLevel = (float)(currentLevel - startingLevel) / (maxLevel - startingLevel);

            // Create multiple waves with different frequencies for a complex pattern
            // Use higher frequencies to ensure we see multiple cycles in the preview
            float primaryWave = Mathf.Sin(normalizedLevel * DefaultPrimaryFrequency * Mathf.PI);
            float secondaryWave = Mathf.Sin(normalizedLevel * DefaultSecondaryFrequency * Mathf.PI) * DefaultSecondaryAmplitudeScale;
            float combinedWave = (primaryWave + secondaryWave) / DefaultNormalizationFactor; // Normalize

            // Amplify the wave effect to make it more visible in the graph
            // This makes the cyclical nature much more pronounced
            float amplifiedWave = combinedWave * DefaultAmplificationFactor;
            amplifiedWave = Mathf.Clamp(amplifiedWave, -DefaultMaxAmplitude, DefaultMaxAmplitude); // Limit the range to avoid extreme values

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the actual multiplier with the cyclical component
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure cyclical pattern
                // with a small base increase to ensure progression
                // Use the absolute value to create a wave that only goes up (for better visibility)
                float cyclicalComponent = 1.0f + Mathf.Abs(amplifiedWave) * DefaultZeroVariationScale;
                actualMultiplier = DefaultZeroBaseMultiplier * cyclicalComponent;
            }
            else
            {
                // Use the specified multiplier with the cyclical pattern
                // Allow both positive and negative variations to create a true wave pattern
                float cyclicalComponent = 1.0f + amplifiedWave * DefaultVariationScale;
                actualMultiplier = effectiveMultiplier * cyclicalComponent;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the cyclical formula method
        /// </summary>
        public static List<float> CalculateCyclicalRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Create a more pronounced cyclical pattern for better visualization
                // Use normalized level position to ensure consistent cycles across different level ranges
                float normalizedLevel = (float)(level - startingLevel) / (maxLevel - startingLevel);

                // Create multiple waves with different frequencies for a complex pattern
                float primaryWave = Mathf.Sin(normalizedLevel * DefaultPrimaryFrequency * Mathf.PI);
                float secondaryWave = Mathf.Sin(normalizedLevel * DefaultSecondaryFrequency * Mathf.PI) * DefaultSecondaryAmplitudeScale;
                float combinedWave = (primaryWave + secondaryWave) / DefaultNormalizationFactor; // Normalize

                // Amplify the wave effect to make it more visible in the graph
                float amplifiedWave = combinedWave * DefaultAmplificationFactor;
                amplifiedWave = Mathf.Clamp(amplifiedWave, -DefaultMaxAmplitude, DefaultMaxAmplitude); // Limit the range to avoid extreme values

                // Calculate the actual multiplier with the cyclical component
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure cyclical pattern
                    // Use the absolute value to create a wave that only goes up (for better visibility)
                    float cyclicalComponent = 1.0f + Mathf.Abs(amplifiedWave) * DefaultZeroVariationScale;
                    rawValue = DefaultZeroBaseMultiplier * cyclicalComponent;
                }
                else
                {
                    // Use the specified multiplier with the cyclical pattern
                    // Allow both positive and negative variations to create a true wave pattern
                    float cyclicalComponent = 1.0f + amplifiedWave * DefaultVariationScale;
                    rawValue = effectiveMultiplier * cyclicalComponent;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
