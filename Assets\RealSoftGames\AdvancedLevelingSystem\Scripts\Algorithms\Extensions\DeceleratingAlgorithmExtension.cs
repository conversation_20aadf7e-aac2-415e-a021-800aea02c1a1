using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Decelerating algorithm
    /// </summary>
    public static class DeceleratingAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroInitialMultiplier = 1.2f;
        private const float DefaultZeroMinMultiplier = 1.02f;
        private const float DefaultZeroDecelerationFactor = 0.005f;
        private const float DefaultInitialMultiplierScale = 1.2f;
        private const float DefaultMinMultiplierScale = 0.8f;
        
        /// <summary>
        /// Calculates the next experience requirement using the decelerating formula method
        /// </summary>
        public static int CalculateDeceleratingRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the decelerating factor
            // This creates a curve that decreases the multiplier as levels increase
            float initialMultiplier;
            float minMultiplier;
            float decelerationFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed parameters
                initialMultiplier = DefaultZeroInitialMultiplier;
                minMultiplier = DefaultZeroMinMultiplier;
                decelerationFactor = DefaultZeroDecelerationFactor;
            }
            else
            {
                // Scale the parameters based on the levelUpMultiplier
                initialMultiplier = effectiveMultiplier * DefaultInitialMultiplierScale; // Start higher
                minMultiplier = effectiveMultiplier * DefaultMinMultiplierScale; // Don't go below scale% of the multiplier
                decelerationFactor = (initialMultiplier - minMultiplier) / maxLevel; // Gradual deceleration
            }
            
            // Calculate the actual multiplier with deceleration
            float actualMultiplier = Mathf.Max(minMultiplier, initialMultiplier - (decelerationFactor * currentLevel));
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the decelerating formula method
        /// </summary>
        public static List<float> CalculateDeceleratingRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the decelerating factor
                float initialMultiplier;
                float minMultiplier;
                float decelerationFactor;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed parameters
                    initialMultiplier = DefaultZeroInitialMultiplier;
                    minMultiplier = DefaultZeroMinMultiplier;
                    decelerationFactor = DefaultZeroDecelerationFactor;
                }
                else
                {
                    // Scale the parameters based on the levelUpMultiplier
                    initialMultiplier = effectiveMultiplier * DefaultInitialMultiplierScale;
                    minMultiplier = effectiveMultiplier * DefaultMinMultiplierScale;
                    decelerationFactor = (initialMultiplier - minMultiplier) / maxLevel;
                }
                
                // Calculate the actual multiplier with deceleration
                float rawValue = Mathf.Max(minMultiplier, initialMultiplier - (decelerationFactor * level));
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
