using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Decremental algorithm
    /// </summary>
    public static class DecrementalAlgorithmExtension
    {
        // Default parameters
        private const float DefaultInitialZeroMultiplier = 1.1f;
        private const float DefaultFinalZeroMultiplier = 1.05f;
        private const float DefaultMaxDecreaseFactor = 0.2f;
        
        /// <summary>
        /// Calculates the next experience requirement using the decremental formula method
        /// </summary>
        public static int CalculateDecrementalRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Decremental - higher multiplier at the beginning, gradually decreasing
            // The decrease factor increases as we progress through levels
            float decreaseFactor = progressRatio * DefaultMaxDecreaseFactor; // Maximum decrease at max level
            
            // Calculate the actual multiplier with the decreasing effect
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure decremental pattern
                // Start at DefaultInitialZeroMultiplier and decrease to DefaultFinalZeroMultiplier
                actualMultiplier = DefaultInitialZeroMultiplier - ((DefaultInitialZeroMultiplier - DefaultFinalZeroMultiplier) * progressRatio);
            }
            else
            {
                // Start at the full multiplier and decrease based on progress
                float multiplierRange = effectiveMultiplier - 1f; // The amount above 1.0
                actualMultiplier = 1f + (multiplierRange * (1f - decreaseFactor));
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the decremental formula method
        /// </summary>
        public static List<float> CalculateDecrementalRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are (0 to 1)
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Decremental - higher multiplier at the beginning, gradually decreasing
                float decreaseFactor = progressRatio * DefaultMaxDecreaseFactor; // Maximum decrease at max level
                
                // Calculate the actual multiplier with the decreasing effect
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure decremental pattern
                    rawValue = DefaultInitialZeroMultiplier - ((DefaultInitialZeroMultiplier - DefaultFinalZeroMultiplier) * progressRatio);
                }
                else
                {
                    // Start at the full multiplier and decrease based on progress
                    float multiplierRange = effectiveMultiplier - 1f; // The amount above 1.0
                    rawValue = 1f + (multiplierRange * (1f - decreaseFactor));
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
