using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Early Boost algorithm
    /// </summary>
    public static class EarlyBoostAlgorithmExtension
    {
        // Default parameters
        private const float DefaultInitialBoostMultiplier = 1.05f;
        private const float DefaultMinBlendFactor = 0.2f;
        private const float DefaultMaxBoostFactor = 0.05f;
        
        /// <summary>
        /// Calculates the next experience requirement using the early boost formula method
        /// </summary>
        public static int CalculateEarlyBoostRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Early boost - higher boost at the beginning, gradually transitioning to normal
            // The boost factor decreases as we progress through levels
            float boostFactor = 1f - progressRatio;
            
            // Calculate the actual multiplier with the boost effect
            // At the start, the multiplier will be close to DefaultInitialBoostMultiplier
            // Near the end, it will approach the effectiveMultiplier
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure early boost pattern
                actualMultiplier = 1f + (DefaultMaxBoostFactor * boostFactor);
            }
            else
            {
                // Blend between a small multiplier and the full multiplier based on progress
                float baseMultiplier = DefaultInitialBoostMultiplier;
                float blendFactor = DefaultMinBlendFactor + ((1f - DefaultMinBlendFactor) * progressRatio); // Start at DefaultMinBlendFactor, go to 100%
                actualMultiplier = baseMultiplier * (1f - blendFactor) + effectiveMultiplier * blendFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the early boost formula method
        /// </summary>
        public static List<float> CalculateEarlyBoostRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are (0 to 1)
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Early boost - higher boost at the beginning, gradually transitioning to normal
                float boostFactor = 1f - progressRatio;
                
                // Calculate the actual multiplier with the boost effect
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure early boost pattern
                    rawValue = 1f + (DefaultMaxBoostFactor * boostFactor);
                }
                else
                {
                    // Blend between a small multiplier and the full multiplier based on progress
                    float baseMultiplier = DefaultInitialBoostMultiplier;
                    float blendFactor = DefaultMinBlendFactor + ((1f - DefaultMinBlendFactor) * progressRatio); // Start at DefaultMinBlendFactor, go to 100%
                    rawValue = baseMultiplier * (1f - blendFactor) + effectiveMultiplier * blendFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
