using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Elastic algorithm
    /// </summary>
    public static class ElasticAlgorithmExtension
    {
        // Default parameters
        private const float DefaultFrequency = 8f;
        private const float DefaultDampingFactor = 5f;
        private const float DefaultBaseAmplitude = 0.15f;
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultZeroGrowthFactor = 0.05f;
        private const float DefaultGrowthFactor = 0.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the elastic formula method
        /// </summary>
        public static int CalculateElasticRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the elastic effect - damped oscillation with exponential decay
            float oscillation = Mathf.Sin(normalizedPosition * DefaultFrequency * Mathf.PI);
            float damping = Mathf.Exp(-normalizedPosition * DefaultDampingFactor);
            float elasticValue = oscillation * damping * DefaultBaseAmplitude;
            
            // Calculate the elastic factor
            float elasticFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure elastic pattern
                // with a smaller range to avoid excessive growth
                float baseMultiplier = DefaultZeroBaseMultiplier;
                float growthComponent = DefaultZeroGrowthFactor * normalizedPosition; // Small growth component
                
                elasticFactor = baseMultiplier + elasticValue + growthComponent;
                
                // Ensure we have at least some increase
                elasticFactor = Mathf.Max(elasticFactor, 1.01f);
            }
            else
            {
                // Scale the elastic effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                float growthComponent = DefaultGrowthFactor * effectiveMultiplier * normalizedPosition; // Growth component
                
                elasticFactor = baseMultiplier + (elasticValue * scaledAmplitude) + growthComponent;
                
                // Ensure we have at least some increase
                elasticFactor = Mathf.Max(elasticFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * elasticFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the elastic formula method
        /// </summary>
        public static List<float> CalculateElasticRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the elastic effect - damped oscillation with exponential decay
                float oscillation = Mathf.Sin(normalizedPosition * DefaultFrequency * Mathf.PI);
                float damping = Mathf.Exp(-normalizedPosition * DefaultDampingFactor);
                float elasticValue = oscillation * damping * DefaultBaseAmplitude;
                
                // Calculate the elastic factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure elastic pattern
                    float baseMultiplier = DefaultZeroBaseMultiplier;
                    float growthComponent = DefaultZeroGrowthFactor * normalizedPosition; // Small growth component
                    
                    rawValue = baseMultiplier + elasticValue + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the elastic effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                    float growthComponent = DefaultGrowthFactor * effectiveMultiplier * normalizedPosition; // Growth component
                    
                    rawValue = baseMultiplier + (elasticValue * scaledAmplitude) + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
