using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Exponential algorithm
    /// </summary>
    public static class ExponentialAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultLevelMultiplierCoefficient = 0.01f;
        
        /// <summary>
        /// Calculates the next experience requirement using the exponential formula method
        /// </summary>
        public static int CalculateExponentialRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the exponential growth factor
            // This creates a curve that increases the multiplier as levels increase
            float growthFactor = 1f + DefaultLevelMultiplierCoefficient * currentLevel;

            // Calculate the actual multiplier with exponential growth
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure exponential pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the exponential growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the exponential formula method
        /// </summary>
        public static List<float> CalculateExponentialRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the exponential growth factor
                float growthFactor = 1f + DefaultLevelMultiplierCoefficient * level;

                // Calculate the actual multiplier with exponential growth
                float rawValue;

                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure exponential pattern
                    rawValue = DefaultZeroBaseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the exponential growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
