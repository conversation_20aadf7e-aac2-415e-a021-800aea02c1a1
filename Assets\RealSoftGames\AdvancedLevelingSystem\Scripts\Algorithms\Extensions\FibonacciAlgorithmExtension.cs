using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Fibonacci algorithm
    /// </summary>
    public static class FibonacciAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroScaleFactor = 0.1f;
        private const float DefaultMinScaleFactor = 0.1f;
        private const float DefaultMaxScaleFactor = 0.5f;
        
        // Cache for Fibonacci numbers to avoid recalculating
        private static Dictionary<int, float> fibonacciCache = new Dictionary<int, float>();
        
        /// <summary>
        /// Calculates the next experience requirement using the Fibonacci formula method
        /// </summary>
        public static int CalculateFibonacciRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the Fibonacci multiplier
            // We use a normalized Fibonacci ratio to keep the growth reasonable
            float fibonacciRatio = GetNormalizedFibonacciRatio(currentLevel - startingLevel + 1);
            
            // Calculate the actual multiplier with Fibonacci pattern
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure Fibonacci pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = 1f + (fibonacciRatio - 1f) * DefaultZeroScaleFactor;
            }
            else
            {
                // Apply the Fibonacci pattern to the effective multiplier
                // Scale the effect based on the levelUpMultiplier
                float scaleFactor = Mathf.Lerp(DefaultMinScaleFactor, DefaultMaxScaleFactor, (effectiveMultiplier - 1f) / 0.5f);
                actualMultiplier = 1f + (fibonacciRatio - 1f) * scaleFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the Fibonacci formula method
        /// </summary>
        public static List<float> CalculateFibonacciRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the Fibonacci multiplier
                float fibonacciRatio = GetNormalizedFibonacciRatio(level - startingLevel + 1);
                
                // Calculate the actual multiplier with Fibonacci pattern
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure Fibonacci pattern
                    rawValue = 1f + (fibonacciRatio - 1f) * DefaultZeroScaleFactor;
                }
                else
                {
                    // Apply the Fibonacci pattern to the effective multiplier
                    float scaleFactor = Mathf.Lerp(DefaultMinScaleFactor, DefaultMaxScaleFactor, (effectiveMultiplier - 1f) / 0.5f);
                    rawValue = 1f + (fibonacciRatio - 1f) * scaleFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Gets a normalized Fibonacci ratio for the given index
        /// </summary>
        private static float GetNormalizedFibonacciRatio(int index)
        {
            // Ensure index is positive
            index = Mathf.Max(1, index);
            
            // For the first two indices, return fixed values
            if (index <= 2)
                return index;
            
            // Calculate the Fibonacci number at the given index
            float fibNumber = GetFibonacciNumber(index);
            float prevFibNumber = GetFibonacciNumber(index - 1);
            
            // Return the ratio between consecutive Fibonacci numbers
            // This approaches the golden ratio (approximately 1.618) as index increases
            return fibNumber / prevFibNumber;
        }
        
        /// <summary>
        /// Gets the Fibonacci number at the given index
        /// </summary>
        private static float GetFibonacciNumber(int index)
        {
            // Check if we've already calculated this Fibonacci number
            if (fibonacciCache.TryGetValue(index, out float cachedValue))
                return cachedValue;
            
            // Calculate the Fibonacci number
            float result;
            
            if (index <= 0)
                result = 0;
            else if (index == 1 || index == 2)
                result = index;
            else
                result = GetFibonacciNumber(index - 1) + GetFibonacciNumber(index - 2);
            
            // Cache the result for future use
            fibonacciCache[index] = result;
            
            return result;
        }
    }
}
