using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Fractional algorithm
    /// </summary>
    public static class FractionalAlgorithmExtension
    {
        // Default parameters
        private const float DefaultBasePower = 1.5f;
        private const float DefaultExponentNumerator = 1.3f;
        
        /// <summary>
        /// Calculates the next experience requirement using the fractional formula method
        /// </summary>
        public static int CalculateFractionalRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the fractional power factor
            // This creates a curve that starts steep and gradually flattens
            float exponent = DefaultExponentNumerator / Mathf.Max(1, currentLevel);
            
            // Calculate the actual multiplier with fractional power
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure fractional pattern
                actualMultiplier = Mathf.Pow(DefaultBasePower, exponent);
            }
            else
            {
                // Scale the fractional effect based on the levelUpMultiplier
                float scaledBase = 1f + (DefaultBasePower - 1f) * (effectiveMultiplier - 1f) / 0.5f;
                actualMultiplier = Mathf.Pow(scaledBase, exponent);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the fractional formula method
        /// </summary>
        public static List<float> CalculateFractionalRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the fractional power factor
                float exponent = DefaultExponentNumerator / Mathf.Max(1, level);
                
                // Calculate the actual multiplier with fractional power
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure fractional pattern
                    rawValue = Mathf.Pow(DefaultBasePower, exponent);
                }
                else
                {
                    // Scale the fractional effect based on the levelUpMultiplier
                    float scaledBase = 1f + (DefaultBasePower - 1f) * (effectiveMultiplier - 1f) / 0.5f;
                    rawValue = Mathf.Pow(scaledBase, exponent);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
