using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Geometric algorithm
    /// </summary>
    public static class GeometricAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.1f;
        private const float DefaultLevelScalingFactor = 0.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the geometric formula method
        /// </summary>
        public static int CalculateGeometricRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the geometric growth factor
            // This creates a curve that increases the multiplier as a power of the level
            float levelFactor = (float)currentLevel * DefaultLevelScalingFactor; // Scale by the default factor
            float growthFactor = Mathf.Pow(effectiveMultiplier, levelFactor);
            
            // Calculate the actual multiplier with geometric growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure geometric pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = Mathf.Pow(DefaultZeroBaseMultiplier, levelFactor);
            }
            else
            {
                // Use the calculated growth factor
                actualMultiplier = growthFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the geometric formula method
        /// </summary>
        public static List<float> CalculateGeometricRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the geometric growth factor
                float levelFactor = (float)level * DefaultLevelScalingFactor;
                float growthFactor = Mathf.Pow(effectiveMultiplier, levelFactor);
                
                // Calculate the actual multiplier with geometric growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure geometric pattern
                    rawValue = Mathf.Pow(DefaultZeroBaseMultiplier, levelFactor);
                }
                else
                {
                    // Use the calculated growth factor
                    rawValue = growthFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
