using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Harmonic algorithm
    /// </summary>
    public static class HarmonicAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        
        /// <summary>
        /// Calculates the next experience requirement using the harmonic formula method
        /// </summary>
        public static int CalculateHarmonicRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the harmonic factor
            // This creates a curve that decreases the growth rate as levels increase
            float harmonicFactor = 1f + (1f / (currentLevel + 1f));
            
            // Calculate the actual multiplier with harmonic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure harmonic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * harmonicFactor;
            }
            else
            {
                // Apply the harmonic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * harmonicFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the harmonic formula method
        /// </summary>
        public static List<float> CalculateHarmonicRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the harmonic factor
                float harmonicFactor = 1f + (1f / (level + 1f));
                
                // Calculate the actual multiplier with harmonic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure harmonic pattern
                    rawValue = DefaultZeroBaseMultiplier * harmonicFactor;
                }
                else
                {
                    // Apply the harmonic growth to the effective multiplier
                    rawValue = effectiveMultiplier * harmonicFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
