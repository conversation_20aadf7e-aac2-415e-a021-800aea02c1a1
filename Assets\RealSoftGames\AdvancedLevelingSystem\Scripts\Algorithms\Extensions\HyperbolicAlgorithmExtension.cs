using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Hyperbolic algorithm
    /// </summary>
    public static class HyperbolicAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the hyperbolic formula method
        /// </summary>
        public static int CalculateHyperbolicRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the hyperbolic growth factor
            // This creates a curve that approaches 1 as levels increase
            float hyperbolicFactor = 1f - (1f / (currentLevel + 1f));
            
            // Calculate the actual multiplier with hyperbolic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure hyperbolic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * hyperbolicFactor;
            }
            else
            {
                // Apply the hyperbolic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * hyperbolicFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the hyperbolic formula method
        /// </summary>
        public static List<float> CalculateHyperbolicRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the hyperbolic growth factor
                float hyperbolicFactor = 1f - (1f / (level + 1f));
                
                // Calculate the actual multiplier with hyperbolic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure hyperbolic pattern
                    rawValue = DefaultZeroBaseMultiplier * hyperbolicFactor;
                }
                else
                {
                    // Apply the hyperbolic growth to the effective multiplier
                    rawValue = effectiveMultiplier * hyperbolicFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
