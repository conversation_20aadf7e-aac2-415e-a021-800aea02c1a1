using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Inverse Logarithmic algorithm
    /// </summary>
    public static class InverseLogarithmicAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultMaxIncreasePercentage = 0.5f;
        
        /// <summary>
        /// Calculates the next experience requirement using the inverse logarithmic formula method
        /// </summary>
        public static int CalculateInverseLogarithmicRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the inverse logarithmic growth factor
            // This creates a curve that increases the multiplier as levels increase
            float logFactor = Mathf.Log10(currentLevel + 1) / Mathf.Log10(maxLevel + 1);
            
            // The increase is capped at the specified percentage of the multiplier
            float increaseAmount = DefaultMaxIncreasePercentage * logFactor;
            
            // Calculate the actual multiplier with inverse logarithmic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure inverse logarithmic pattern
                // Start at the base multiplier and increase logarithmically
                actualMultiplier = DefaultZeroBaseMultiplier * (1f + increaseAmount);
            }
            else
            {
                // Apply the inverse logarithmic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f + increaseAmount);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the inverse logarithmic formula method
        /// </summary>
        public static List<float> CalculateInverseLogarithmicRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the inverse logarithmic growth factor
                float logFactor = Mathf.Log10(level + 1) / Mathf.Log10(maxLevel + 1);
                float increaseAmount = DefaultMaxIncreasePercentage * logFactor;
                
                // Calculate the actual multiplier with inverse logarithmic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure inverse logarithmic pattern
                    rawValue = DefaultZeroBaseMultiplier * (1f + increaseAmount);
                }
                else
                {
                    // Apply the inverse logarithmic growth to the effective multiplier
                    rawValue = effectiveMultiplier * (1f + increaseAmount);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
