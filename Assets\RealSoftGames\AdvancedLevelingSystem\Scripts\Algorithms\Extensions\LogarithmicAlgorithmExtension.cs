using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Logarithmic algorithm
    /// </summary>
    public static class LogarithmicAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.1f;
        private const float DefaultMaxReductionPercentage = 0.2f;
        
        /// <summary>
        /// Calculates the next experience requirement using the logarithmic formula method
        /// </summary>
        public static int CalculateLogarithmicRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the logarithmic reduction factor
            // This creates a curve that decreases the multiplier as levels increase
            float logFactor = Mathf.Log10(Mathf.Max(1, currentLevel)) / Mathf.Log10(maxLevel);
            
            // The reduction is capped at the specified percentage of the multiplier
            float reductionAmount = DefaultMaxReductionPercentage * logFactor;
            
            // Calculate the actual multiplier with logarithmic reduction
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure logarithmic pattern
                // Start at the base multiplier and decrease logarithmically
                actualMultiplier = DefaultZeroBaseMultiplier * (1f - reductionAmount);
            }
            else
            {
                // Apply the logarithmic reduction to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f - reductionAmount);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the logarithmic formula method
        /// </summary>
        public static List<float> CalculateLogarithmicRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the logarithmic reduction factor
                float logFactor = Mathf.Log10(Mathf.Max(1, level)) / Mathf.Log10(maxLevel);
                float reductionAmount = DefaultMaxReductionPercentage * logFactor;
                
                // Calculate the actual multiplier with logarithmic reduction
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure logarithmic pattern
                    rawValue = DefaultZeroBaseMultiplier * (1f - reductionAmount);
                }
                else
                {
                    // Apply the logarithmic reduction to the effective multiplier
                    rawValue = effectiveMultiplier * (1f - reductionAmount);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
