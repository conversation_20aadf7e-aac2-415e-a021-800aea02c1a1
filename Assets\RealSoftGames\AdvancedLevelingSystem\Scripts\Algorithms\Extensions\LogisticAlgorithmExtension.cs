using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Logistic algorithm
    /// </summary>
    public static class LogisticAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultScaleFactor = 0.1f;
        private const float DefaultMidpointFactor = 0.5f;
        
        /// <summary>
        /// Calculates the next experience requirement using the logistic formula method
        /// </summary>
        public static int CalculateLogisticRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the logistic S-curve factor
            // This creates a curve that starts slow, accelerates in the middle, then slows down again
            
            // Calculate the midpoint and scale for the logistic function
            float midpoint = startingLevel + (maxLevel - startingLevel) * DefaultMidpointFactor;
            float scale = (maxLevel - startingLevel) * DefaultScaleFactor; // Controls the steepness of the S-curve
            
            // Apply the logistic function: 1/(1+e^(-(x-midpoint)/scale))
            // This creates a smooth S-curve from 0 to 1
            float x = currentLevel - midpoint;
            float logisticFactor = 1f / (1f + Mathf.Exp(-x / scale));
            
            // Scale the logistic factor to create a more pronounced effect
            float scaledLogisticFactor = 1f + logisticFactor;
            
            // Calculate the actual multiplier with logistic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure logistic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * scaledLogisticFactor;
            }
            else
            {
                // Apply the logistic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * scaledLogisticFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the logistic formula method
        /// </summary>
        public static List<float> CalculateLogisticRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the midpoint and scale for the logistic function
            float midpoint = startingLevel + (maxLevel - startingLevel) * DefaultMidpointFactor;
            float scale = (maxLevel - startingLevel) * DefaultScaleFactor;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Apply the logistic function
                float x = level - midpoint;
                float logisticFactor = 1f / (1f + Mathf.Exp(-x / scale));
                
                // Scale the logistic factor
                float scaledLogisticFactor = 1f + logisticFactor;
                
                // Calculate the actual multiplier with logistic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure logistic pattern
                    rawValue = DefaultZeroBaseMultiplier * scaledLogisticFactor;
                }
                else
                {
                    // Apply the logistic growth to the effective multiplier
                    rawValue = effectiveMultiplier * scaledLogisticFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
