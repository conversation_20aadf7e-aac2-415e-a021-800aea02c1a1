using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the MicroProgression algorithm
    /// </summary>
    public static class MicroProgressionAlgorithmExtension
    {
        // Default parameters
        private const int DefaultLogBaseOffset = 10;
        private const float DefaultFixedAdditionCoefficient = 0.005f;
        
        /// <summary>
        /// Calculates the next experience requirement using the micro progression formula method
        /// </summary>
        public static int CalculateMicroProgressionRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // MicroProgression uses logarithmic scaling to keep increases very small
            // The formula is designed to give tiny increases that get smaller as levels increase
            
            // Calculate the logarithmic ratio - this gives a value just slightly above 1.0
            float logRatio = Mathf.Log10(currentLevel + DefaultLogBaseOffset) / Mathf.Log10(currentLevel + (DefaultLogBaseOffset - 1));
            
            // Add a tiny fixed amount based on the current experience
            float fixedAddition = Mathf.Min(1, currentExperience * DefaultFixedAdditionCoefficient);
            
            // Apply the levelUpMultiplier if it's not effectively zero
            if (levelUpMultiplier > 0.01f)
            {
                // Scale the log ratio effect by the multiplier
                // This keeps the micro-progression nature but allows for adjustment
                float scaledLogEffect = (logRatio - 1f) * levelUpMultiplier + 1f;
                return Mathf.RoundToInt(currentExperience * scaledLogEffect + fixedAddition);
            }
            else
            {
                // Use the pure micro-progression formula
                return Mathf.RoundToInt(currentExperience * logRatio + fixedAddition);
            }
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the micro progression formula method
        /// </summary>
        public static List<float> CalculateMicroProgressionRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the logarithmic ratio
                float logRatio = Mathf.Log10(level + DefaultLogBaseOffset) / Mathf.Log10(level + (DefaultLogBaseOffset - 1));
                
                // Apply the levelUpMultiplier if it's not effectively zero
                float rawValue;
                
                if (levelUpMultiplier > 0.01f)
                {
                    // Scale the log ratio effect by the multiplier
                    float scaledLogEffect = (logRatio - 1f) * levelUpMultiplier + 1f;
                    rawValue = scaledLogEffect;
                }
                else
                {
                    // Use the pure micro-progression formula
                    rawValue = logRatio;
                }
                
                // Add a small fixed amount to visualize that component
                // For visualization purposes, we'll scale this up to make it visible
                float fixedAdditionEffect = 0.001f;
                rawValue += fixedAdditionEffect;
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
