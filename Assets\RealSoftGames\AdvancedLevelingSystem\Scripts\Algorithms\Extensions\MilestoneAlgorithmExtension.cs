using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Milestone algorithm
    /// </summary>
    public static class MilestoneAlgorithmExtension
    {
        // Default parameters
        private const int DefaultMilestoneInterval = 5;
        private const float DefaultZeroMilestoneMultiplier = 1.2f;
        private const float DefaultZeroBetweenMilestoneMultiplier = 1.05f;
        private const float DefaultMilestoneIntensity = 1.5f;
        private const float DefaultBetweenMilestoneIntensity = 0.8f;
        
        /// <summary>
        /// Calculates the next experience requirement using the milestone formula method
        /// </summary>
        public static int CalculateMilestoneRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Check if the next level is a milestone
            bool isNextLevelMilestone = ((currentLevel + 1) % DefaultMilestoneInterval == 0);
            
            // Calculate the actual multiplier with milestone jumps
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed milestone parameters
                if (isNextLevelMilestone)
                {
                    // At milestone levels, use a higher multiplier
                    actualMultiplier = DefaultZeroMilestoneMultiplier;
                }
                else
                {
                    // Between milestones, use a lower multiplier
                    actualMultiplier = DefaultZeroBetweenMilestoneMultiplier;
                }
            }
            else
            {
                // Scale the milestone effect based on the levelUpMultiplier
                if (isNextLevelMilestone)
                {
                    // At milestone levels, use a higher multiplier
                    actualMultiplier = effectiveMultiplier * DefaultMilestoneIntensity;
                }
                else
                {
                    // Between milestones, use a lower multiplier
                    actualMultiplier = effectiveMultiplier * DefaultBetweenMilestoneIntensity;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the milestone formula method
        /// </summary>
        public static List<float> CalculateMilestoneRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Check if this level is a milestone
                bool isLevelMilestone = (level % DefaultMilestoneInterval == 0);
                
                // Calculate the actual multiplier with milestone jumps
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed milestone parameters
                    if (isLevelMilestone)
                    {
                        rawValue = DefaultZeroMilestoneMultiplier;
                    }
                    else
                    {
                        rawValue = DefaultZeroBetweenMilestoneMultiplier;
                    }
                }
                else
                {
                    // Scale the milestone effect based on the levelUpMultiplier
                    if (isLevelMilestone)
                    {
                        rawValue = effectiveMultiplier * DefaultMilestoneIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultBetweenMilestoneIntensity;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
