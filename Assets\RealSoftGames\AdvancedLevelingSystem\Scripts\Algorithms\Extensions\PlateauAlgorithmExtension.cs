using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Plateau algorithm
    /// </summary>
    public static class PlateauAlgorithmExtension
    {
        // Default parameters
        private const int DefaultPlateauCycle = 10;
        private const int DefaultClimbDuration = 3;
        private const float DefaultZeroClimbMultiplier = 1.12f;
        private const float DefaultZeroPlateauMultiplier = 1.01f;
        private const float DefaultClimbIntensityBase = 1.5f;
        private const float DefaultClimbIntensityGrowth = 0.5f;
        private const float DefaultPlateauIntensity = 0.6f;
        
        /// <summary>
        /// Calculates the next experience requirement using the plateau formula method
        /// </summary>
        public static int CalculatePlateauRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate position in the plateau cycle
            int cyclePosition = (currentLevel - startingLevel) % DefaultPlateauCycle;
            bool isClimbing = cyclePosition < DefaultClimbDuration;
            
            // Calculate the plateau factor
            float plateauFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure plateau pattern
                // with a smaller range to avoid excessive growth
                if (isClimbing)
                {
                    // During climb, use a higher multiplier
                    plateauFactor = DefaultZeroClimbMultiplier;
                }
                else
                {
                    // During plateau, use a very low multiplier
                    plateauFactor = DefaultZeroPlateauMultiplier;
                }
            }
            else
            {
                // Scale the plateau effect based on the levelUpMultiplier
                if (isClimbing)
                {
                    // During climb, use a higher multiplier that increases with level
                    float climbIntensity = DefaultClimbIntensityBase + normalizedPosition * DefaultClimbIntensityGrowth; // Climbs get steeper
                    plateauFactor = effectiveMultiplier * climbIntensity;
                }
                else
                {
                    // During plateau, use a very low multiplier
                    plateauFactor = effectiveMultiplier * DefaultPlateauIntensity;
                }
                
                // Ensure we have at least some increase
                plateauFactor = Mathf.Max(plateauFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * plateauFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the plateau formula method
        /// </summary>
        public static List<float> CalculatePlateauRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate position in the plateau cycle
                int cyclePosition = (level - startingLevel) % DefaultPlateauCycle;
                bool isClimbing = cyclePosition < DefaultClimbDuration;
                
                // Calculate the plateau factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure plateau pattern
                    if (isClimbing)
                    {
                        rawValue = DefaultZeroClimbMultiplier;
                    }
                    else
                    {
                        rawValue = DefaultZeroPlateauMultiplier;
                    }
                }
                else
                {
                    // Scale the plateau effect based on the levelUpMultiplier
                    if (isClimbing)
                    {
                        float climbIntensity = DefaultClimbIntensityBase + normalizedPosition * DefaultClimbIntensityGrowth; // Climbs get steeper
                        rawValue = effectiveMultiplier * climbIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultPlateauIntensity;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
