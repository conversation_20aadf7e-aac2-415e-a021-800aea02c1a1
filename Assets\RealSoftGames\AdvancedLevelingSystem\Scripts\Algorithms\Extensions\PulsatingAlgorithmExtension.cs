using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Pulsating algorithm
    /// </summary>
    public static class PulsatingAlgorithmExtension
    {
        // Default parameters
        private const float DefaultFrequency = 8f;
        private const float DefaultBaseAmplitude = 0.05f;
        private const float DefaultAmplitudeGrowth = 3f;
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultAmplitudeScalingFactor = 0.5f;
        
        /// <summary>
        /// Calculates the next experience requirement using the pulsating formula method
        /// </summary>
        public static int CalculatePulsatingRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the pulse value with increasing amplitude
            float pulseValue = Mathf.Sin(normalizedPosition * DefaultFrequency * Mathf.PI);
            float growingAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
            float scaledPulse = pulseValue * growingAmplitude;
            
            // Calculate the pulsating factor
            float pulsatingFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure pulsating pattern
                // with a smaller range to avoid excessive growth
                pulsatingFactor = DefaultZeroBaseMultiplier + scaledPulse;
                
                // Ensure we have at least some increase
                pulsatingFactor = Mathf.Max(pulsatingFactor, 1.01f);
            }
            else
            {
                // Scale the pulsating effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                
                pulsatingFactor = baseMultiplier + (scaledPulse * scaledAmplitude);
                
                // Ensure we have at least some increase
                pulsatingFactor = Mathf.Max(pulsatingFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * pulsatingFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the pulsating formula method
        /// </summary>
        public static List<float> CalculatePulsatingRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression (0 to 1 range)
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the pulse value with increasing amplitude
                float pulseValue = Mathf.Sin(normalizedPosition * DefaultFrequency * Mathf.PI);
                float growingAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
                float scaledPulse = pulseValue * growingAmplitude;
                
                // Calculate the pulsating factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure pulsating pattern
                    rawValue = DefaultZeroBaseMultiplier + scaledPulse;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the pulsating effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (scaledPulse * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
