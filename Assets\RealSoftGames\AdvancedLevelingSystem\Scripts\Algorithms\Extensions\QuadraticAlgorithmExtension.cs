using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Quadratic algorithm
    /// </summary>
    public static class QuadraticAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultQuadraticCoefficient = 0.005f;
        
        /// <summary>
        /// Calculates the next experience requirement using the quadratic formula method
        /// </summary>
        public static int CalculateQuadraticRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the quadratic growth factor
            // This creates a curve that increases the multiplier as the square of the level
            float growthFactor = 1f + DefaultQuadraticCoefficient * (currentLevel * currentLevel);
            
            // Calculate the actual multiplier with quadratic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure quadratic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the quadratic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the quadratic formula method
        /// </summary>
        public static List<float> CalculateQuadraticRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the quadratic growth factor
                float growthFactor = 1f + DefaultQuadraticCoefficient * (level * level);
                
                // Calculate the actual multiplier with quadratic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure quadratic pattern
                    rawValue = DefaultZeroBaseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the quadratic growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
