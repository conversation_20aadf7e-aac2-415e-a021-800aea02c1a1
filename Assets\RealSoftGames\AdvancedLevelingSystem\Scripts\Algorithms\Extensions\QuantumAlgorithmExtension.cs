using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Quantum algorithm
    /// </summary>
    public static class QuantumAlgorithmExtension
    {
        // Default parameters
        private const float DefaultBaseGrowth = 0.05f;
        private const float DefaultQuantumAmplitude = 0.1f;
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultAmplitudeScalingFactor = 0.5f;
        private const float DefaultNormalizationFactor = 1.25f;
        
        // Default quantum parameters
        private static readonly float[] DefaultFrequencies = { 7.3f, 11.7f, 19.3f, 23.1f, 29.9f };
        private static readonly float[] DefaultPhases = { 0.1f, 0.7f, 1.3f, 2.1f, 2.9f };
        private static readonly float[] DefaultWeights = { 0.5f, 0.3f, 0.2f, 0.15f, 0.1f };
        
        /// <summary>
        /// Calculates the next experience requirement using the quantum formula method
        /// </summary>
        public static int CalculateQuantumRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the deterministic component - smooth growth curve
            float deterministicComponent = DefaultBaseGrowth * normalizedPosition;
            
            // Calculate the quantum fluctuation component
            float quantumFluctuation = CalculateQuantumFluctuation(normalizedPosition);
            
            // Calculate the quantum factor
            float quantumFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure quantum pattern
                // with a smaller range to avoid excessive growth
                quantumFactor = DefaultZeroBaseMultiplier * (1f + deterministicComponent + quantumFluctuation);
                
                // Ensure we have at least some increase
                quantumFactor = Mathf.Max(quantumFactor, 1.01f);
            }
            else
            {
                // Scale the quantum effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                
                quantumFactor = baseMultiplier * (1f + deterministicComponent + (quantumFluctuation * scaledAmplitude));
                
                // Ensure we have at least some increase
                quantumFactor = Mathf.Max(quantumFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * quantumFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the quantum formula method
        /// </summary>
        public static List<float> CalculateQuantumRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the deterministic component - smooth growth curve
                float deterministicComponent = DefaultBaseGrowth * normalizedPosition;
                
                // Calculate the quantum fluctuation component
                float quantumFluctuation = CalculateQuantumFluctuation(normalizedPosition);
                
                // Calculate the quantum factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure quantum pattern
                    rawValue = DefaultZeroBaseMultiplier * (1f + deterministicComponent + quantumFluctuation);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the quantum effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier * (1f + deterministicComponent + (quantumFluctuation * scaledAmplitude));
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Calculates the quantum fluctuation component
        /// </summary>
        private static float CalculateQuantumFluctuation(float normalizedPosition)
        {
            // Calculate the quantum fluctuation component
            float quantumFluctuation = 0f;
            
            // Use the minimum length of the arrays to avoid index out of range errors
            int componentCount = Mathf.Min(
                DefaultFrequencies.Length,
                DefaultPhases.Length,
                DefaultWeights.Length
            );
            
            for (int i = 0; i < componentCount; i++)
            {
                // Each wave contributes to the fluctuation
                float wave = Mathf.Sin((normalizedPosition * DefaultFrequencies[i] * Mathf.PI) + DefaultPhases[i]);
                quantumFluctuation += wave * DefaultWeights[i];
            }
            
            // Normalize the fluctuation to the range [-1, 1] and scale by amplitude
            quantumFluctuation = (quantumFluctuation / DefaultNormalizationFactor) * DefaultQuantumAmplitude;
            
            return quantumFluctuation;
        }
    }
}
