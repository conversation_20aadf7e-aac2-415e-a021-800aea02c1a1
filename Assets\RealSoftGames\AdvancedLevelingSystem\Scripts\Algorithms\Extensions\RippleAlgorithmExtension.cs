using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Ripple algorithm
    /// </summary>
    public static class RippleAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultAmplitudeScalingFactor = 0.5f;
        private const float DefaultAmplitudeGrowthFactor = 2f;
        
        // Default wave parameters
        private static readonly float[] DefaultFrequencies = { 3f, 7f, 11f };
        private static readonly float[] DefaultAmplitudes = { 0.05f, 0.03f, 0.02f };
        private static readonly float[] DefaultPhases = { 0f, 0.33f, 0.67f };
        
        /// <summary>
        /// Calculates the next experience requirement using the ripple formula method
        /// </summary>
        public static int CalculateRippleRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the combined ripple effect
            float rippleValue = CalculateRippleValue(normalizedPosition);
            
            // Calculate the ripple factor
            float rippleFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure ripple pattern
                // with a smaller range to avoid excessive growth
                rippleFactor = DefaultZeroBaseMultiplier + rippleValue;
                
                // Ensure we have at least some increase
                rippleFactor = Mathf.Max(rippleFactor, 1.01f);
            }
            else
            {
                // Scale the ripple effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                
                rippleFactor = baseMultiplier + (rippleValue * scaledAmplitude);
                
                // Ensure we have at least some increase
                rippleFactor = Mathf.Max(rippleFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * rippleFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the ripple formula method
        /// </summary>
        public static List<float> CalculateRippleRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the combined ripple effect
                float rippleValue = CalculateRippleValue(normalizedPosition);
                
                // Calculate the ripple factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure ripple pattern
                    rawValue = DefaultZeroBaseMultiplier + rippleValue;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the ripple effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (rippleValue * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Calculates the combined ripple value from multiple wave components
        /// </summary>
        private static float CalculateRippleValue(float normalizedPosition)
        {
            // Calculate the combined ripple effect
            float rippleValue = 0f;
            
            // Use the minimum length of the arrays to avoid index out of range errors
            int waveCount = Mathf.Min(DefaultFrequencies.Length, DefaultAmplitudes.Length, DefaultPhases.Length);
            
            for (int i = 0; i < waveCount; i++)
            {
                // Each wave has a different frequency, amplitude, and phase
                float wave = Mathf.Sin((normalizedPosition * DefaultFrequencies[i] * Mathf.PI) + (DefaultPhases[i] * Mathf.PI));
                
                // Scale the amplitude based on level progression
                float scaledAmplitude = DefaultAmplitudes[i] * (1f + normalizedPosition * DefaultAmplitudeGrowthFactor);
                
                // Add this wave to the combined ripple effect
                rippleValue += wave * scaledAmplitude;
            }
            
            return rippleValue;
        }
    }
}
