using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Square Root algorithm
    /// </summary>
    public static class RootAlgorithmExtension
    {
        // Default parameters
        private const float DefaultBaseMultiplierFactor = 1.0f;
        private const float DefaultRootPower = 2.0f;
        
        /// <summary>
        /// Calculates the next experience requirement using the square root formula method
        /// </summary>
        public static int CalculateRootRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the root factor
            float rootFactor;
            
            if (DefaultRootPower == 2.0f)
            {
                // Square root growth
                rootFactor = Mathf.Sqrt(currentLevel * effectiveMultiplier * DefaultBaseMultiplierFactor);
            }
            else
            {
                // General nth root growth
                rootFactor = Mathf.Pow(currentLevel * effectiveMultiplier * DefaultBaseMultiplierFactor, 1.0f / DefaultRootPower);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * rootFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the square root formula method
        /// </summary>
        public static List<float> CalculateRootRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the root factor
                float rawValue;
                
                if (DefaultRootPower == 2.0f)
                {
                    // Square root growth
                    rawValue = Mathf.Sqrt(level * effectiveMultiplier * DefaultBaseMultiplierFactor);
                }
                else
                {
                    // General nth root growth
                    rawValue = Mathf.Pow(level * effectiveMultiplier * DefaultBaseMultiplierFactor, 1.0f / DefaultRootPower);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
