using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Sawtooth algorithm
    /// </summary>
    public static class SawtoothAlgorithmExtension
    {
        // Default cycle length
        private const int DefaultCycleLength = 5;
        
        // Default multipliers
        private const float DefaultMinMultiplier = 0.9f;
        private const float DefaultMaxMultiplier = 1.1f;
        private const float DefaultZeroMultiplierMin = 1.02f;
        private const float DefaultZeroMultiplierMax = 1.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the sawtooth formula method
        /// </summary>
        public static int CalculateSawtoothRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the position within the current cycle (0 to cycleLength-1)
            int cyclePosition = (currentLevel - startingLevel) % DefaultCycleLength;
            
            // Calculate the sawtooth factor
            // This creates a pattern that increases linearly then drops
            float sawtoothFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure sawtooth pattern
                // with a smaller range to avoid excessive growth
                float range = DefaultZeroMultiplierMax - DefaultZeroMultiplierMin;
                
                // Linear increase within the cycle
                sawtoothFactor = DefaultZeroMultiplierMin + (range * cyclePosition / (DefaultCycleLength - 1));
            }
            else
            {
                // Scale the sawtooth effect based on the levelUpMultiplier
                float baseMultiplier = DefaultMinMultiplier * effectiveMultiplier;
                float peakMultiplier = DefaultMaxMultiplier * effectiveMultiplier;
                float range = peakMultiplier - baseMultiplier;
                
                // Linear increase within the cycle
                sawtoothFactor = baseMultiplier + (range * cyclePosition / (DefaultCycleLength - 1));
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * sawtoothFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the sawtooth formula method
        /// </summary>
        public static List<float> CalculateSawtoothRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the position within the current cycle
                int cyclePosition = (level - startingLevel) % DefaultCycleLength;
                
                // Calculate the sawtooth factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure sawtooth pattern
                    float range = DefaultZeroMultiplierMax - DefaultZeroMultiplierMin;
                    
                    rawValue = DefaultZeroMultiplierMin + (range * cyclePosition / (DefaultCycleLength - 1));
                }
                else
                {
                    // Scale the sawtooth effect based on the levelUpMultiplier
                    float baseMultiplier = DefaultMinMultiplier * effectiveMultiplier;
                    float peakMultiplier = DefaultMaxMultiplier * effectiveMultiplier;
                    float range = peakMultiplier - baseMultiplier;
                    
                    rawValue = baseMultiplier + (range * cyclePosition / (DefaultCycleLength - 1));
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
