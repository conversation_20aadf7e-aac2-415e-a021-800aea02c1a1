using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Seasonal algorithm
    /// </summary>
    public static class SeasonalAlgorithmExtension
    {
        // Default seasonal parameters
        private const int DefaultSeasonLength = 4;
        
        // Default multipliers
        private const float DefaultZeroEasySeasonMultiplier = 1.03f;
        private const float DefaultZeroHardSeasonMultiplier = 1.08f;
        private const float DefaultEasySeasonScalingFactor = 0.9f;
        private const float DefaultHardSeasonScalingFactor = 1.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the seasonal formula method
        /// </summary>
        public static int CalculateSeasonalRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the position within the current season (0 to seasonLength-1)
            int seasonPosition = (currentLevel - startingLevel) % DefaultSeasonLength;
            
            // Calculate the seasonal factor
            // This creates a pattern that alternates between easy and hard periods
            float seasonalFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure seasonal pattern
                // with a smaller range to avoid excessive growth
                if (seasonPosition < DefaultSeasonLength / 2)
                {
                    // Easy season
                    seasonalFactor = DefaultZeroEasySeasonMultiplier;
                }
                else
                {
                    // Hard season
                    seasonalFactor = DefaultZeroHardSeasonMultiplier;
                }
            }
            else
            {
                // Scale the seasonal effect based on the levelUpMultiplier
                if (seasonPosition < DefaultSeasonLength / 2)
                {
                    // Easy season
                    seasonalFactor = effectiveMultiplier * DefaultEasySeasonScalingFactor;
                }
                else
                {
                    // Hard season
                    seasonalFactor = effectiveMultiplier * DefaultHardSeasonScalingFactor;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * seasonalFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the seasonal formula method
        /// </summary>
        public static List<float> CalculateSeasonalRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the position within the current season
                int seasonPosition = (level - startingLevel) % DefaultSeasonLength;
                
                // Calculate the seasonal factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure seasonal pattern
                    if (seasonPosition < DefaultSeasonLength / 2)
                    {
                        // Easy season
                        rawValue = DefaultZeroEasySeasonMultiplier;
                    }
                    else
                    {
                        // Hard season
                        rawValue = DefaultZeroHardSeasonMultiplier;
                    }
                }
                else
                {
                    // Scale the seasonal effect based on the levelUpMultiplier
                    if (seasonPosition < DefaultSeasonLength / 2)
                    {
                        // Easy season
                        rawValue = effectiveMultiplier * DefaultEasySeasonScalingFactor;
                    }
                    else
                    {
                        // Hard season
                        rawValue = effectiveMultiplier * DefaultHardSeasonScalingFactor;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
