using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Spiral algorithm
    /// </summary>
    public static class SpiralAlgorithmExtension
    {
        // Default spiral parameters
        private const float DefaultBaseFrequency = 4f;
        private const float DefaultFrequencyGrowth = 6f;
        private const float DefaultBaseAmplitude = 0.03f;
        private const float DefaultAmplitudeGrowth = 5f;
        
        // Default multipliers
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultZeroGrowthComponent = 0.05f;
        private const float DefaultAmplitudeScalingFactor = 0.5f;
        private const float DefaultGrowthScalingFactor = 0.1f;
        private const float DefaultExponentialBase = 1.1f;
        private const float DefaultExponentialScaling = 10f;
        
        /// <summary>
        /// Calculates the next experience requirement using the spiral formula method
        /// </summary>
        public static int CalculateSpiralRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current frequency and amplitude
            float currentFrequency = DefaultBaseFrequency + (normalizedPosition * DefaultFrequencyGrowth);
            float currentAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
            
            // Calculate the spiral value - combination of sine wave and exponential growth
            float sineValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
            float exponentialGrowth = Mathf.Pow(DefaultExponentialBase, normalizedPosition * DefaultExponentialScaling) - 1f; // Exponential component
            float spiralValue = sineValue * currentAmplitude * (1f + exponentialGrowth);
            
            // Calculate the spiral factor
            float spiralFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure spiral pattern
                // with a smaller range to avoid excessive growth
                float baseMultiplier = DefaultZeroBaseMultiplier;
                float growthComponent = DefaultZeroGrowthComponent * normalizedPosition; // Small growth component
                
                spiralFactor = baseMultiplier + spiralValue + growthComponent;
                
                // Ensure we have at least some increase
                spiralFactor = Mathf.Max(spiralFactor, 1.01f);
            }
            else
            {
                // Scale the spiral effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                float growthComponent = DefaultGrowthScalingFactor * effectiveMultiplier * normalizedPosition; // Growth component
                
                spiralFactor = baseMultiplier + (spiralValue * scaledAmplitude) + growthComponent;
                
                // Ensure we have at least some increase
                spiralFactor = Mathf.Max(spiralFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * spiralFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the spiral formula method
        /// </summary>
        public static List<float> CalculateSpiralRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current frequency and amplitude
                float currentFrequency = DefaultBaseFrequency + (normalizedPosition * DefaultFrequencyGrowth);
                float currentAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
                
                // Calculate the spiral value - combination of sine wave and exponential growth
                float sineValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
                float exponentialGrowth = Mathf.Pow(DefaultExponentialBase, normalizedPosition * DefaultExponentialScaling) - 1f; // Exponential component
                float spiralValue = sineValue * currentAmplitude * (1f + exponentialGrowth);
                
                // Calculate the spiral factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure spiral pattern
                    float baseMultiplier = DefaultZeroBaseMultiplier;
                    float growthComponent = DefaultZeroGrowthComponent * normalizedPosition; // Small growth component
                    
                    rawValue = baseMultiplier + spiralValue + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the spiral effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * DefaultAmplitudeScalingFactor; // Scale amplitude with multiplier
                    float growthComponent = DefaultGrowthScalingFactor * effectiveMultiplier * normalizedPosition; // Growth component
                    
                    rawValue = baseMultiplier + (spiralValue * scaledAmplitude) + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
