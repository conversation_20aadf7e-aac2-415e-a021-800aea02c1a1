using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Staircase algorithm
    /// </summary>
    public static class StaircaseAlgorithmExtension
    {
        // Default staircase parameters
        private const int DefaultInitialStepSize = 3;
        private const int DefaultMaxStepSize = 5;
        private const float DefaultStepGrowthRate = 0.2f;
        
        // Default multipliers
        private const float DefaultZeroBoundaryMultiplier = 1.15f;
        private const float DefaultZeroPlateauMultiplier = 1.01f;
        private const float DefaultBaseStepHeight = 1.5f;
        private const float DefaultStepHeightGrowth = 0.5f;
        private const float DefaultPlateauMultiplierFactor = 0.7f;
        
        /// <summary>
        /// Calculates the next experience requirement using the staircase formula method
        /// </summary>
        public static int CalculateStaircaseRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current step size based on level progression
            int currentStepSize = Mathf.FloorToInt(DefaultInitialStepSize + (DefaultMaxStepSize - DefaultInitialStepSize) * normalizedPosition * DefaultStepGrowthRate);
            currentStepSize = Mathf.Clamp(currentStepSize, DefaultInitialStepSize, DefaultMaxStepSize);
            
            // Determine if we're at a step boundary
            bool isStepBoundary = (currentLevel - startingLevel) % currentStepSize == 0;
            
            // Calculate the staircase factor
            float staircaseFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure staircase pattern
                // with smaller steps to avoid excessive growth
                if (isStepBoundary)
                {
                    // At step boundaries, use a higher multiplier
                    staircaseFactor = DefaultZeroBoundaryMultiplier;
                }
                else
                {
                    // Within steps, use a very small multiplier for the plateau effect
                    staircaseFactor = DefaultZeroPlateauMultiplier;
                }
            }
            else
            {
                // Scale the staircase effect based on the levelUpMultiplier
                if (isStepBoundary)
                {
                    // At step boundaries, use a higher multiplier that increases with level
                    float stepHeight = DefaultBaseStepHeight + normalizedPosition * DefaultStepHeightGrowth; // Steps get taller
                    staircaseFactor = effectiveMultiplier * stepHeight;
                }
                else
                {
                    // Within steps, use a lower multiplier for the plateau effect
                    staircaseFactor = effectiveMultiplier * DefaultPlateauMultiplierFactor;
                }
                
                // Ensure we have at least some increase
                staircaseFactor = Mathf.Max(staircaseFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * staircaseFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the staircase formula method
        /// </summary>
        public static List<float> CalculateStaircaseRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current step size based on level progression
                int currentStepSize = Mathf.FloorToInt(DefaultInitialStepSize + (DefaultMaxStepSize - DefaultInitialStepSize) * normalizedPosition * DefaultStepGrowthRate);
                currentStepSize = Mathf.Clamp(currentStepSize, DefaultInitialStepSize, DefaultMaxStepSize);
                
                // Determine if we're at a step boundary
                bool isStepBoundary = (level - startingLevel) % currentStepSize == 0;
                
                // Calculate the staircase factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure staircase pattern
                    if (isStepBoundary)
                    {
                        rawValue = DefaultZeroBoundaryMultiplier;
                    }
                    else
                    {
                        rawValue = DefaultZeroPlateauMultiplier;
                    }
                }
                else
                {
                    // Scale the staircase effect based on the levelUpMultiplier
                    if (isStepBoundary)
                    {
                        float stepHeight = DefaultBaseStepHeight + normalizedPosition * DefaultStepHeightGrowth; // Steps get taller
                        rawValue = effectiveMultiplier * stepHeight;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultPlateauMultiplierFactor;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
