using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Stepped algorithm
    /// </summary>
    public static class SteppedAlgorithmExtension
    {
        // Default step size
        private const int DefaultStepSize = 5;
        
        // Default multipliers
        private const float DefaultStepThresholdMultiplier = 1.5f;
        private const float DefaultBetweenStepsMultiplier = 0.8f;
        private const float DefaultZeroMultiplierStepThreshold = 1.15f;
        private const float DefaultZeroMultiplierBetweenSteps = 1.03f;
        
        /// <summary>
        /// Calculates the next experience requirement using the stepped formula method
        /// </summary>
        public static int CalculateSteppedRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the actual multiplier with stepped progression
            float actualMultiplier;
            
            // Check if we're at a step threshold
            bool isStepThreshold = (currentLevel % DefaultStepSize == 0);
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure stepped pattern
                // with smaller steps to avoid excessive growth
                if (isStepThreshold)
                {
                    // At step thresholds, use a higher multiplier
                    actualMultiplier = DefaultZeroMultiplierStepThreshold;
                }
                else
                {
                    // Between thresholds, use a lower multiplier
                    actualMultiplier = DefaultZeroMultiplierBetweenSteps;
                }
            }
            else
            {
                // Scale the step effect based on the levelUpMultiplier
                if (isStepThreshold)
                {
                    // At step thresholds, use a higher multiplier
                    actualMultiplier = effectiveMultiplier * DefaultStepThresholdMultiplier;
                }
                else
                {
                    // Between thresholds, use a lower multiplier
                    actualMultiplier = effectiveMultiplier * DefaultBetweenStepsMultiplier;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the stepped formula method
        /// </summary>
        public static List<float> CalculateSteppedRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Check if we're at a step threshold
                bool isStepThreshold = (level % DefaultStepSize == 0);
                
                // Calculate the actual multiplier with stepped progression
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure stepped pattern
                    if (isStepThreshold)
                    {
                        rawValue = DefaultZeroMultiplierStepThreshold;
                    }
                    else
                    {
                        rawValue = DefaultZeroMultiplierBetweenSteps;
                    }
                }
                else
                {
                    // Scale the step effect based on the levelUpMultiplier
                    if (isStepThreshold)
                    {
                        rawValue = effectiveMultiplier * DefaultStepThresholdMultiplier;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * DefaultBetweenStepsMultiplier;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
