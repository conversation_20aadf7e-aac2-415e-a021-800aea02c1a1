using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Tiered algorithm
    /// </summary>
    public static class TieredAlgorithmExtension
    {
        // Default tier multipliers
        private const float DefaultBeginnerTierMultiplier = 1.05f;
        private const float DefaultIntermediateTierMultiplier = 1.1f;
        private const float DefaultAdvancedTierMultiplier = 1.15f;
        private const float DefaultExpertTierMultiplier = 1.2f;
        private const float DefaultMasterTierMultiplier = 1.25f;
        
        // Default scaling factors
        private const float DefaultBaseMultiplierScale = 0.8f;
        private const float DefaultTierIncrementScale = 0.1f;
        
        /// <summary>
        /// Calculates the next experience requirement using the tiered formula method
        /// </summary>
        public static int CalculateTieredRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate which tier the current level falls into
            int tier = GetTier(currentLevel, startingLevel, maxLevel);
            
            // Calculate the actual multiplier based on the tier
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed tier multipliers
                switch (tier)
                {
                    case 0: // Beginner tier
                        actualMultiplier = DefaultBeginnerTierMultiplier;
                        break;
                    case 1: // Intermediate tier
                        actualMultiplier = DefaultIntermediateTierMultiplier;
                        break;
                    case 2: // Advanced tier
                        actualMultiplier = DefaultAdvancedTierMultiplier;
                        break;
                    case 3: // Expert tier
                        actualMultiplier = DefaultExpertTierMultiplier;
                        break;
                    default: // Master tier
                        actualMultiplier = DefaultMasterTierMultiplier;
                        break;
                }
            }
            else
            {
                // Scale the tier multipliers based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier * DefaultBaseMultiplierScale;
                float tierIncrement = effectiveMultiplier * DefaultTierIncrementScale;
                
                actualMultiplier = baseMultiplier + (tier * tierIncrement);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the tiered formula method
        /// </summary>
        public static List<float> CalculateTieredRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate which tier the level falls into
                int tier = GetTier(level, startingLevel, maxLevel);
                
                // Calculate the actual multiplier based on the tier
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed tier multipliers
                    switch (tier)
                    {
                        case 0: // Beginner tier
                            rawValue = DefaultBeginnerTierMultiplier;
                            break;
                        case 1: // Intermediate tier
                            rawValue = DefaultIntermediateTierMultiplier;
                            break;
                        case 2: // Advanced tier
                            rawValue = DefaultAdvancedTierMultiplier;
                            break;
                        case 3: // Expert tier
                            rawValue = DefaultExpertTierMultiplier;
                            break;
                        default: // Master tier
                            rawValue = DefaultMasterTierMultiplier;
                            break;
                    }
                }
                else
                {
                    // Scale the tier multipliers based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier * DefaultBaseMultiplierScale;
                    float tierIncrement = effectiveMultiplier * DefaultTierIncrementScale;
                    
                    rawValue = baseMultiplier + (tier * tierIncrement);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Determines which tier a level falls into
        /// </summary>
        private static int GetTier(int level, int startingLevel, int maxLevel)
        {
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Define 5 tiers (0-4)
            int tierSize = Mathf.Max(1, totalLevels / 5);
            
            // Calculate the tier based on the level's position in the range
            int levelPosition = level - startingLevel;
            int tier = Mathf.Min(4, levelPosition / tierSize);
            
            return tier;
        }
    }
}
