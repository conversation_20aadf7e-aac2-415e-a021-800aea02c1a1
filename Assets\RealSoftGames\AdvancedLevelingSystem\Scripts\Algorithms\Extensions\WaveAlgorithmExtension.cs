using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Wave algorithm
    /// </summary>
    public static class WaveAlgorithmExtension
    {
        // Default parameters
        private const float DefaultBaseFrequency = 5f;
        private const float DefaultFrequencyGrowth = 3f;
        private const float DefaultBaseAmplitude = 0.05f;
        private const float DefaultAmplitudeGrowth = 4f;
        private const float DefaultZeroBaseMultiplier = 1.05f;
        
        /// <summary>
        /// Calculates the next experience requirement using the wave formula method
        /// </summary>
        public static int CalculateWaveRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current frequency and amplitude
            float currentFrequency = DefaultBaseFrequency + (normalizedPosition * DefaultFrequencyGrowth);
            float currentAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
            
            // Calculate the wave value
            float waveValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
            float scaledWave = waveValue * currentAmplitude;
            
            // Calculate the wave factor
            float waveFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure wave pattern
                // with a smaller range to avoid excessive growth
                waveFactor = DefaultZeroBaseMultiplier + scaledWave;
                
                // Ensure we have at least some increase
                waveFactor = Mathf.Max(waveFactor, 1.01f);
            }
            else
            {
                // Scale the wave effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                
                waveFactor = baseMultiplier + (scaledWave * scaledAmplitude);
                
                // Ensure we have at least some increase
                waveFactor = Mathf.Max(waveFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * waveFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the wave formula method
        /// </summary>
        public static List<float> CalculateWaveRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current frequency and amplitude
                float currentFrequency = DefaultBaseFrequency + (normalizedPosition * DefaultFrequencyGrowth);
                float currentAmplitude = DefaultBaseAmplitude * (1f + normalizedPosition * DefaultAmplitudeGrowth);
                
                // Calculate the wave value
                float waveValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
                float scaledWave = waveValue * currentAmplitude;
                
                // Calculate the wave factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure wave pattern
                    rawValue = DefaultZeroBaseMultiplier + scaledWave;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the wave effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (scaledWave * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
