using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Adaptive leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Adaptive Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Adaptive Algorithm", order = 125)]
    public class AdaptiveAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Early game multiplier (0-30% of progression)")]
        [Range(0.8f, 1.2f)]
        public float earlyGameMultiplier = 1.05f;
        
        [Tooltip("Mid game multiplier (30-70% of progression)")]
        [Range(0.8f, 1.3f)]
        public float midGameMultiplier = 1.15f;
        
        [Tooltip("Late game multiplier (70-100% of progression)")]
        [Range(0.8f, 1.2f)]
        public float lateGameMultiplier = 1.1f;
        
        [Tooltip("Early game scaling factor for non-zero levelUpMultiplier")]
        [Range(0.7f, 1.0f)]
        public float earlyGameScalingFactor = 0.9f;
        
        [Toolt<PERSON>("Mid game scaling factor for non-zero levelUpMultiplier")]
        [Range(1.0f, 1.3f)]
        public float midGameScalingFactor = 1.1f;
        
        [Tooltip("Late game scaling factor for non-zero levelUpMultiplier")]
        [Range(0.8f, 1.2f)]
        public float lateGameScalingFactor = 1.0f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Adaptive";
            description = "Adjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.";
            formulaExplanation = "Formula: Varies based on progress through total levels\n\nAdjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the adaptive formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the adaptive factor
            // This creates a curve that adjusts based on progress through the levels
            float adaptiveFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure adaptive pattern
                // Start easy, get harder in the middle, then easier again at the end
                if (progressRatio < 0.3f)
                {
                    // Early game - easy progression
                    adaptiveFactor = earlyGameMultiplier;
                }
                else if (progressRatio < 0.7f)
                {
                    // Mid game - harder progression
                    adaptiveFactor = midGameMultiplier;
                }
                else
                {
                    // Late game - easier progression again
                    adaptiveFactor = lateGameMultiplier;
                }
            }
            else
            {
                // Scale the adaptive effect based on the levelUpMultiplier
                if (progressRatio < 0.3f)
                {
                    // Early game - easier progression
                    adaptiveFactor = effectiveMultiplier * earlyGameScalingFactor;
                }
                else if (progressRatio < 0.7f)
                {
                    // Mid game - harder progression
                    adaptiveFactor = effectiveMultiplier * midGameScalingFactor;
                }
                else
                {
                    // Late game - medium progression
                    adaptiveFactor = effectiveMultiplier * lateGameScalingFactor;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * adaptiveFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the adaptive formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Calculate the adaptive factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure adaptive pattern
                    if (progressRatio < 0.3f)
                    {
                        rawValue = earlyGameMultiplier;
                    }
                    else if (progressRatio < 0.7f)
                    {
                        rawValue = midGameMultiplier;
                    }
                    else
                    {
                        rawValue = lateGameMultiplier;
                    }
                }
                else
                {
                    // Scale the adaptive effect based on the levelUpMultiplier
                    if (progressRatio < 0.3f)
                    {
                        rawValue = effectiveMultiplier * earlyGameScalingFactor;
                    }
                    else if (progressRatio < 0.7f)
                    {
                        rawValue = effectiveMultiplier * midGameScalingFactor;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * lateGameScalingFactor;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
