using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Asymptotic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Asymptotic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Asymptotic Algorithm", order = 134)]
    public class AsymptoticAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Maximum multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.1f, 2.0f)]
        public float zeroMaxMultiplier = 1.5f;
        
        [Tooltip("Multiplier scale factor (relative to levelUpMultiplier)")]
        [Range(1.0f, 3.0f)]
        public float multiplierScale = 2.0f;
        
        [Tooltip("Rate of approach to the maximum (higher values approach faster)")]
        [Range(0.01f, 0.5f)]
        public float approachRate = 0.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Asymptotic";
            description = "Approaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (maxMultiplier * (1 - e^(-rate*level)))\n\nApproaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the asymptotic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Define the maximum multiplier and the rate of approach
            float maxMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a fixed maximum
                maxMultiplier = zeroMaxMultiplier;
            }
            else
            {
                // Scale the maximum multiplier based on the levelUpMultiplier
                // This allows for adjustment while maintaining the asymptotic behavior
                maxMultiplier = 1f + (effectiveMultiplier - 1f) * multiplierScale; // Scale the effect
            }
            
            // Calculate the asymptotic factor using an exponential approach
            // This creates a curve that approaches maxMultiplier as levels increase
            float approachFactor = 1f - Mathf.Exp(-approachRate * currentLevel);
            
            // Calculate the actual multiplier with asymptotic approach
            float actualMultiplier = 1f + (maxMultiplier - 1f) * approachFactor;
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the asymptotic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Define the maximum multiplier and the rate of approach
            float maxMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a fixed maximum
                maxMultiplier = zeroMaxMultiplier;
            }
            else
            {
                // Scale the maximum multiplier based on the levelUpMultiplier
                maxMultiplier = 1f + (effectiveMultiplier - 1f) * multiplierScale; // Scale the effect
            }
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the asymptotic factor
                float approachFactor = 1f - Mathf.Exp(-approachRate * level);
                
                // Calculate the actual multiplier with asymptotic approach
                float rawValue = 1f + (maxMultiplier - 1f) * approachFactor;
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
