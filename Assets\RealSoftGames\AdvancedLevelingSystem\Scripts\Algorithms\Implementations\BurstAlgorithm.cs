using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Burst Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Burst Algorithm", order = 135)]
    public class BurstAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("How many levels between bursts")]
        [Range(3, 15)]
        public int burstCycle = 7;
        
        [Tooltip("How many levels each burst lasts")]
        [Range(1, 5)]
        public int burstDuration = 2;
        
        [Tooltip("Base burst intensity (multiplier during bursts)")]
        [Range(1.1f, 2.0f)]
        public float baseBurstIntensity = 1.5f;
        
        [Tooltip("Maximum burst intensity at highest levels")]
        [Range(1.5f, 3.0f)]
        public float maxBurstIntensity = 2.5f;
        
        [Tooltip("Base multiplier between bursts when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroBetweenBurstsMultiplier = 1.03f;
        
        [Tooltip("Multiplier during bursts when levelUpMultiplier is effectively zero")]
        [Range(1.05f, 1.3f)]
        public float zeroBurstMultiplier = 1.15f;
        
        [Tooltip("Level progression factor for zero multiplier burst intensity")]
        [Range(0.01f, 0.2f)]
        public float zeroLevelProgressionFactor = 0.05f;
        
        [Tooltip("Scale factor for between-burst periods (relative to levelUpMultiplier)")]
        [Range(0.5f, 0.9f)]
        public float betweenBurstsScale = 0.8f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Burst";
            description = "Creates sudden bursts of high difficulty followed by periods of easier progression, like a series of explosions with calm periods in between.";
            formulaExplanation = "Formula: Uses exponential bursts at regular intervals\n\nCreates a pattern with sudden spikes of high difficulty followed by periods of more gradual progression, with bursts becoming more intense as levels increase.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        
        /// <summary>
        /// Calculates the next experience requirement using the burst formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate position in the burst cycle
            int cyclePosition = (currentLevel - startingLevel) % burstCycle;
            bool isInBurst = cyclePosition < burstDuration;
            
            // Calculate the burst intensity that increases with level
            float currentBurstIntensity = Mathf.Lerp(baseBurstIntensity, maxBurstIntensity, normalizedPosition);
            
            // Calculate the burst factor
            float burstFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure burst pattern
                // with a smaller range to avoid excessive growth
                if (isInBurst)
                {
                    // During burst, use a higher multiplier
                    burstFactor = zeroBurstMultiplier + (zeroLevelProgressionFactor * normalizedPosition); // Increases slightly with level
                }
                else
                {
                    // Between bursts, use a lower multiplier
                    burstFactor = zeroBetweenBurstsMultiplier;
                }
            }
            else
            {
                // Scale the burst effect based on the levelUpMultiplier
                if (isInBurst)
                {
                    // During burst, use a higher multiplier that increases with level
                    burstFactor = effectiveMultiplier * currentBurstIntensity;
                }
                else
                {
                    // Between bursts, use a lower multiplier
                    burstFactor = effectiveMultiplier * betweenBurstsScale;
                }
                
                // Ensure we have at least some increase
                burstFactor = Mathf.Max(burstFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * burstFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the burst formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate position in the burst cycle
                int cyclePosition = (level - startingLevel) % burstCycle;
                bool isInBurst = cyclePosition < burstDuration;
                
                // Calculate the burst intensity that increases with level
                float currentBurstIntensity = Mathf.Lerp(baseBurstIntensity, maxBurstIntensity, normalizedPosition);
                
                // Calculate the burst factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure burst pattern
                    if (isInBurst)
                    {
                        rawValue = zeroBurstMultiplier + (zeroLevelProgressionFactor * normalizedPosition);
                    }
                    else
                    {
                        rawValue = zeroBetweenBurstsMultiplier;
                    }
                }
                else
                {
                    // Scale the burst effect based on the levelUpMultiplier
                    if (isInBurst)
                    {
                        rawValue = effectiveMultiplier * currentBurstIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * betweenBurstsScale;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
