using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Cosine leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Cosine Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Cosine Algorithm", order = 133)]
    public class CosineAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroBaseMultiplier = 1.05f;

        [Tooltip("Amplitude when levelUpMultiplier is effectively zero")]
        [Range(0.01f, 0.1f)]
        public float zeroAmplitude = 0.04f;

        [Tooltip("Amplitude scale factor (relative to levelUpMultiplier)")]
        [Range(0.1f, 0.5f)]
        public float amplitudeScale = 0.2f;

        [Tooltip("Number of complete cosine cycles over the level range")]
        [Range(0.5f, 3f)]
        public float cycleCount = 1f;

        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Cosine";
            description = "Creates a smooth wave pattern that starts high, dips in the middle, and rises again, creating a balanced progression with alternating easy and challenging phases.";
            formulaExplanation = "Formula: Uses cosine function to create a smooth wave pattern\n\nCreates a progression that starts with higher multipliers, decreases in the middle, and increases again toward the end, following a cosine curve.";

            // Call base implementation
            base.OnEnable();
        }


        /// <summary>
        /// Calculates the next experience requirement using the cosine formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));

            // Apply cosine function to create a wave pattern
            // Cosine starts at 1, goes to -1, and back to 1 over 2π
            // We'll use the specified number of cycles for the entire level range
            float cosineValue = Mathf.Cos(normalizedPosition * cycleCount * 2f * Mathf.PI);

            // Map the cosine value from [-1, 1] to a suitable multiplier range
            float cosineFactor;

            if (Mathf.Approximately(levelUpMultiplier, 0f))
            {
                // When levelUpMultiplier is exactly zero, use a pure cosine pattern
                // with a smaller range to avoid excessive growth
                cosineFactor = zeroBaseMultiplier + (cosineValue * zeroAmplitude);

                // Ensure we have at least some increase
                cosineFactor = Mathf.Max(cosineFactor, 1.01f);
            }
            else if (levelUpMultiplier > 0f && levelUpMultiplier < 1.0f)
            {
                // For multipliers between 0 and 1, we need special handling
                // Scale the amplitude based on the levelUpMultiplier (0-1 range)
                // As levelUpMultiplier approaches 0, use smaller amplitude
                // As levelUpMultiplier approaches 1.0, use larger amplitude
                float baseMultiplier = effectiveMultiplier;
                float amplitude = zeroAmplitude * levelUpMultiplier;

                // Use the multiplier directly as the base
                cosineFactor = baseMultiplier + (cosineValue * amplitude);

                // Ensure we have at least some increase
                cosineFactor = Mathf.Max(cosineFactor, 1.01f);
            }
            else
            {
                // For multipliers >= 1.0, scale the cosine effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float amplitude = amplitudeScale * (effectiveMultiplier - 1.0f); // Scale amplitude with multiplier

                cosineFactor = baseMultiplier + (cosineValue * amplitude);

                // Ensure we have at least some increase
                cosineFactor = Mathf.Max(cosineFactor, 1.01f);
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * cosineFactor);

            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the cosine formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression (0 to 1 range)
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);

                // Apply cosine function to create a wave pattern
                float cosineValue = Mathf.Cos(normalizedPosition * cycleCount * 2f * Mathf.PI);

                // Map the cosine value from [-1, 1] to a suitable multiplier range
                float rawValue;

                if (Mathf.Approximately(levelUpMultiplier, 0f))
                {
                    // When levelUpMultiplier is exactly zero, use a pure cosine pattern
                    rawValue = zeroBaseMultiplier + (cosineValue * zeroAmplitude);

                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else if (levelUpMultiplier > 0f && levelUpMultiplier < 1.0f)
                {
                    // For multipliers between 0 and 1, we need special handling
                    // Scale the amplitude based on the levelUpMultiplier (0-1 range)
                    // As levelUpMultiplier approaches 0, use smaller amplitude
                    // As levelUpMultiplier approaches 1.0, use larger amplitude
                    float baseMultiplier = effectiveMultiplier;
                    float amplitude = zeroAmplitude * levelUpMultiplier;

                    // Use the multiplier directly as the base
                    rawValue = baseMultiplier + (cosineValue * amplitude);

                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // For multipliers >= 1.0, scale the cosine effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float amplitude = amplitudeScale * (effectiveMultiplier - 1.0f); // Scale amplitude with multiplier

                    rawValue = baseMultiplier + (cosineValue * amplitude);

                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
