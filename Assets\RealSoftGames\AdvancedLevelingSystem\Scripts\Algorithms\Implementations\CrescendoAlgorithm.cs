using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Crescendo leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Crescendo Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Crescendo Algorithm", order = 132)]
    public class CrescendoAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Crescendo exponent (higher values create a more dramatic build-up)")]
        [Range(1.5f, 4.0f)]
        public float crescendoExponent = 2.5f;
        
        [Tooltip("Note frequency (higher values create more musical 'notes')")]
        [Range(5f, 30f)]
        public float noteFrequency = 20f;
        
        [Tooltip("Note amplitude (higher values create more pronounced 'notes')")]
        [Range(0.01f, 0.1f)]
        public float noteAmplitude = 0.02f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroBaseMultiplier = 1.02f;
        
        [Tooltip("Maximum additional growth when levelUpMultiplier is effectively zero")]
        [Range(0.1f, 0.5f)]
        public float zeroMaxGrowth = 0.2f;
        
        [Tooltip("Base multiplier scale factor (relative to levelUpMultiplier)")]
        [Range(0.5f, 1.0f)]
        public float baseMultiplierScale = 0.8f;
        
        [Tooltip("Maximum additional growth scale factor (relative to levelUpMultiplier)")]
        [Range(0.1f, 1.0f)]
        public float maxGrowthScale = 0.4f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Crescendo";
            description = "Creates a musical crescendo effect where difficulty builds gradually then dramatically increases toward the end, like the climax of a symphony.";
            formulaExplanation = "Formula: Uses exponential growth with accelerating rate\n\nCreates a musical crescendo effect where difficulty builds gradually at first, then accelerates more dramatically toward the end, like the climax of a symphony building to its finale.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        
        /// <summary>
        /// Calculates the next experience requirement using the crescendo formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the crescendo effect - starts slow, then builds dramatically
            float crescendoValue = Mathf.Pow(normalizedPosition, crescendoExponent);
            
            // Add small oscillations to simulate musical notes in the crescendo
            float noteEffect = Mathf.Sin(normalizedPosition * noteFrequency * Mathf.PI) * noteAmplitude * crescendoValue;
            
            // Calculate the crescendo factor
            float crescendoFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure crescendo pattern
                // with a smaller range to avoid excessive growth
                crescendoFactor = zeroBaseMultiplier + (crescendoValue * zeroMaxGrowth) + noteEffect;
                
                // Ensure we have at least some increase
                crescendoFactor = Mathf.Max(crescendoFactor, 1.01f);
            }
            else
            {
                // Scale the crescendo effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier * baseMultiplierScale; // Start lower
                float maxGrowth = effectiveMultiplier * maxGrowthScale; // Maximum additional growth
                
                crescendoFactor = baseMultiplier + (crescendoValue * maxGrowth) + noteEffect;
                
                // Ensure we have at least some increase
                crescendoFactor = Mathf.Max(crescendoFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * crescendoFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the crescendo formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the crescendo effect - starts slow, then builds dramatically
                float crescendoValue = Mathf.Pow(normalizedPosition, crescendoExponent);
                
                // Add small oscillations to simulate musical notes in the crescendo
                float noteEffect = Mathf.Sin(normalizedPosition * noteFrequency * Mathf.PI) * noteAmplitude * crescendoValue;
                
                // Calculate the crescendo factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure crescendo pattern
                    rawValue = zeroBaseMultiplier + (crescendoValue * zeroMaxGrowth) + noteEffect;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the crescendo effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier * baseMultiplierScale; // Start lower
                    float maxGrowth = effectiveMultiplier * maxGrowthScale; // Maximum additional growth
                    
                    rawValue = baseMultiplier + (crescendoValue * maxGrowth) + noteEffect;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
