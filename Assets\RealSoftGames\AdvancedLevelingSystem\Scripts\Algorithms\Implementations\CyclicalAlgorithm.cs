using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Cyclical leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Cyclical Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Cyclical Algorithm", order = 131)]
    public class CyclicalAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Primary wave frequency (higher values create more cycles)")]
        [Range(1f, 10f)]
        public float primaryFrequency = 6.0f;
        
        [Tooltip("Secondary wave frequency (higher values create more cycles)")]
        [Range(1f, 10f)]
        public float secondaryFrequency = 3.0f;
        
        [Tooltip("Secondary wave amplitude scale (relative to primary wave)")]
        [Range(0.1f, 1.0f)]
        public float secondaryAmplitudeScale = 0.5f;
        
        [Tooltip("Combined wave normalization factor")]
        [Range(1f, 2f)]
        public float normalizationFactor = 1.5f;
        
        [Tooltip("Wave amplification factor (higher values create more pronounced cycles)")]
        [Range(1f, 3f)]
        public float amplificationFactor = 1.5f;
        
        [Tooltip("Maximum wave amplitude (limits the range to avoid extreme values)")]
        [Range(0.1f, 1.0f)]
        public float maxAmplitude = 0.5f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroBaseMultiplier = 1.02f;
        
        [Tooltip("Cyclical component variation scale for zero levelUpMultiplier")]
        [Range(0.1f, 0.5f)]
        public float zeroVariationScale = 0.3f;
        
        [Tooltip("Cyclical component variation scale for non-zero levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float variationScale = 0.5f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Cyclical";
            description = "Creates a repeating pattern of difficulty that rises and falls in cycles.";
            formulaExplanation = "Formula: Uses multiple sine waves to create a complex cyclical pattern\n\nCreates a repeating pattern where difficulty increases and decreases in cycles, providing periods of challenge followed by relief.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the cyclical formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Create a more pronounced cyclical pattern for better visualization
            // Use normalized level position to ensure consistent cycles across different level ranges
            float normalizedLevel = (float)(currentLevel - startingLevel) / (maxLevel - startingLevel);

            // Create multiple waves with different frequencies for a complex pattern
            // Use higher frequencies to ensure we see multiple cycles in the preview
            float primaryWave = Mathf.Sin(normalizedLevel * primaryFrequency * Mathf.PI);
            float secondaryWave = Mathf.Sin(normalizedLevel * secondaryFrequency * Mathf.PI) * secondaryAmplitudeScale;
            float combinedWave = (primaryWave + secondaryWave) / normalizationFactor; // Normalize

            // Amplify the wave effect to make it more visible in the graph
            // This makes the cyclical nature much more pronounced
            float amplifiedWave = combinedWave * amplificationFactor;
            amplifiedWave = Mathf.Clamp(amplifiedWave, -maxAmplitude, maxAmplitude); // Limit the range to avoid extreme values

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the actual multiplier with the cyclical component
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure cyclical pattern
                // with a small base increase to ensure progression
                // Use the absolute value to create a wave that only goes up (for better visibility)
                float cyclicalComponent = 1.0f + Mathf.Abs(amplifiedWave) * zeroVariationScale;
                actualMultiplier = zeroBaseMultiplier * cyclicalComponent;
            }
            else
            {
                // Use the specified multiplier with the cyclical pattern
                // Allow both positive and negative variations to create a true wave pattern
                float cyclicalComponent = 1.0f + amplifiedWave * variationScale;
                actualMultiplier = effectiveMultiplier * cyclicalComponent;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the cyclical formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Create a more pronounced cyclical pattern for better visualization
                // Use normalized level position to ensure consistent cycles across different level ranges
                float normalizedLevel = (float)(level - startingLevel) / (maxLevel - startingLevel);

                // Create multiple waves with different frequencies for a complex pattern
                float primaryWave = Mathf.Sin(normalizedLevel * primaryFrequency * Mathf.PI);
                float secondaryWave = Mathf.Sin(normalizedLevel * secondaryFrequency * Mathf.PI) * secondaryAmplitudeScale;
                float combinedWave = (primaryWave + secondaryWave) / normalizationFactor; // Normalize

                // Amplify the wave effect to make it more visible in the graph
                float amplifiedWave = combinedWave * amplificationFactor;
                amplifiedWave = Mathf.Clamp(amplifiedWave, -maxAmplitude, maxAmplitude); // Limit the range to avoid extreme values

                // Calculate the actual multiplier with the cyclical component
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure cyclical pattern
                    // Use the absolute value to create a wave that only goes up (for better visibility)
                    float cyclicalComponent = 1.0f + Mathf.Abs(amplifiedWave) * zeroVariationScale;
                    rawValue = zeroBaseMultiplier * cyclicalComponent;
                }
                else
                {
                    // Use the specified multiplier with the cyclical pattern
                    // Allow both positive and negative variations to create a true wave pattern
                    float cyclicalComponent = 1.0f + amplifiedWave * variationScale;
                    rawValue = effectiveMultiplier * cyclicalComponent;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
