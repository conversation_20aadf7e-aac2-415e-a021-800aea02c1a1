using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Decelerating leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Decelerating Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Decelerating Algorithm", order = 130)]
    public class DeceleratingAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Initial multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.05f, 1.5f)]
        public float zeroInitialMultiplier = 1.2f;
        
        [Tooltip("Minimum multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroMinMultiplier = 1.02f;
        
        [Tooltip("Deceleration factor when levelUpMultiplier is effectively zero")]
        [Range(0.001f, 0.01f)]
        public float zeroDecelerationFactor = 0.005f;
        
        [Tooltip("Initial multiplier scale factor (relative to levelUpMultiplier)")]
        [Range(1.0f, 1.5f)]
        public float initialMultiplierScale = 1.2f;
        
        [Tooltip("Minimum multiplier scale factor (relative to levelUpMultiplier)")]
        [Range(0.5f, 1.0f)]
        public float minMultiplierScale = 0.8f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Decelerating";
            description = "Starts fast, then slows down. Creates a steep early curve that gradually flattens out.";
            formulaExplanation = "Formula: requiredExp = requiredExp * max(minMultiplier, initialMultiplier - decelerationFactor*level)\n\nStarts fast, then slows down. Creates a steep early curve that gradually flattens out.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the decelerating formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the decelerating factor
            // This creates a curve that decreases the multiplier as levels increase
            float initialMultiplier;
            float minMultiplier;
            float decelerationFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed parameters
                initialMultiplier = zeroInitialMultiplier;
                minMultiplier = zeroMinMultiplier;
                decelerationFactor = zeroDecelerationFactor;
            }
            else
            {
                // Scale the parameters based on the levelUpMultiplier
                initialMultiplier = effectiveMultiplier * initialMultiplierScale; // Start higher
                minMultiplier = effectiveMultiplier * minMultiplierScale; // Don't go below scale% of the multiplier
                decelerationFactor = (initialMultiplier - minMultiplier) / maxLevel; // Gradual deceleration
            }
            
            // Calculate the actual multiplier with deceleration
            float actualMultiplier = Mathf.Max(minMultiplier, initialMultiplier - (decelerationFactor * currentLevel));
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the decelerating formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the decelerating factor
                float initialMultiplier;
                float minMultiplier;
                float decelerationFactor;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed parameters
                    initialMultiplier = zeroInitialMultiplier;
                    minMultiplier = zeroMinMultiplier;
                    decelerationFactor = zeroDecelerationFactor;
                }
                else
                {
                    // Scale the parameters based on the levelUpMultiplier
                    initialMultiplier = effectiveMultiplier * initialMultiplierScale;
                    minMultiplier = effectiveMultiplier * minMultiplierScale;
                    decelerationFactor = (initialMultiplier - minMultiplier) / maxLevel;
                }
                
                // Calculate the actual multiplier with deceleration
                float rawValue = Mathf.Max(minMultiplier, initialMultiplier - (decelerationFactor * level));
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
