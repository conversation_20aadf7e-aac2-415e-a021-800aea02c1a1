using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Decremental leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Decremental Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Decremental Algorithm", order = 129)]
    public class DecrementalAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Initial multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float initialZeroMultiplier = 1.1f;
        
        [Tooltip("Final multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float finalZeroMultiplier = 1.05f;
        
        [Tooltip("Maximum decrease factor at max level (0-1)")]
        [Range(0.1f, 0.5f)]
        public float maxDecreaseFactor = 0.2f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Decremental";
            description = "Gets easier as you level up, with the multiplier decreasing as you progress through the game.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (1 + (levelUpMultiplier-1) * (1-(level/maxLevel)*0.2))\n\nGets easier as you level up, with the multiplier decreasing as you progress through the game.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the decremental formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Decremental - higher multiplier at the beginning, gradually decreasing
            // The decrease factor increases as we progress through levels
            float decreaseFactor = progressRatio * maxDecreaseFactor; // Maximum decrease at max level
            
            // Calculate the actual multiplier with the decreasing effect
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure decremental pattern
                // Start at initialZeroMultiplier and decrease to finalZeroMultiplier
                actualMultiplier = initialZeroMultiplier - ((initialZeroMultiplier - finalZeroMultiplier) * progressRatio);
            }
            else
            {
                // Start at the full multiplier and decrease based on progress
                float multiplierRange = effectiveMultiplier - 1f; // The amount above 1.0
                actualMultiplier = 1f + (multiplierRange * (1f - decreaseFactor));
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the decremental formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are (0 to 1)
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Decremental - higher multiplier at the beginning, gradually decreasing
                float decreaseFactor = progressRatio * maxDecreaseFactor; // Maximum decrease at max level
                
                // Calculate the actual multiplier with the decreasing effect
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure decremental pattern
                    rawValue = initialZeroMultiplier - ((initialZeroMultiplier - finalZeroMultiplier) * progressRatio);
                }
                else
                {
                    // Start at the full multiplier and decrease based on progress
                    float multiplierRange = effectiveMultiplier - 1f; // The amount above 1.0
                    rawValue = 1f + (multiplierRange * (1f - decreaseFactor));
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
