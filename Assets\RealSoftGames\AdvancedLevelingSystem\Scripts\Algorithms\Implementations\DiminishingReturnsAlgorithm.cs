using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// DiminishingReturns leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Diminishing Returns Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Diminishing Returns Algorithm", order = 126)]
    public class DiminishingReturnsAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base value for the denominator in the diminishing factor calculation")]
        [Range(5f, 20f)]
        public float diminishingDenominator = 10f;
        
        [Tooltip("Scale factor for the diminishing effect (higher values create stronger diminishing)")]
        [Range(0.1f, 1.0f)]
        public float diminishingScaleFactor = 0.5f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Diminishing Returns";
            description = "As levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - level/(level+10)))\n\nAs levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the diminishing returns formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the diminishing returns factor
            // This creates a curve that approaches 0.5 as levels increase
            float diminishingFactor = currentLevel / (currentLevel + diminishingDenominator);
            
            // Calculate the actual multiplier with diminishing returns
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure diminishing returns pattern
                // Start at base value and decrease with diminishing returns
                actualMultiplier = zeroBaseMultiplier * (1f - diminishingFactor * diminishingScaleFactor);
            }
            else
            {
                // Apply the diminishing returns to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f - diminishingFactor * diminishingScaleFactor);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the diminishing returns formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the diminishing returns factor
                float diminishingFactor = level / (level + diminishingDenominator);
                
                // Calculate the actual multiplier with diminishing returns
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure diminishing returns pattern
                    rawValue = zeroBaseMultiplier * (1f - diminishingFactor * diminishingScaleFactor);
                }
                else
                {
                    // Apply the diminishing returns to the effective multiplier
                    rawValue = effectiveMultiplier * (1f - diminishingFactor * diminishingScaleFactor);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
