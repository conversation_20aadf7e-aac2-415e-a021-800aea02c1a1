using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// EarlyBoost leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Early Boost Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Early Boost Algorithm", order = 127)]
    public class EarlyBoostAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Initial boost multiplier for early levels")]
        [Range(1.01f, 1.2f)]
        public float initialBoostMultiplier = 1.05f;
        
        [Tooltip("Minimum blend factor for the transition (0-1)")]
        [Range(0.1f, 0.5f)]
        public float minBlendFactor = 0.2f;
        
        [Tooltip("Maximum boost factor (higher values create stronger early boost)")]
        [Range(0.01f, 0.2f)]
        public float maxBoostFactor = 0.05f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Early Boost";
            description = "Makes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (1 + 0.05 * (1 - (level/maxLevel)))\n\nMakes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        
        /// <summary>
        /// Calculates the next experience requirement using the early boost formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate how far through the level progression we are (0 to 1)
            float progressRatio = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Early boost - higher boost at the beginning, gradually transitioning to normal
            // The boost factor decreases as we progress through levels
            float boostFactor = 1f - progressRatio;
            
            // Calculate the actual multiplier with the boost effect
            // At the start, the multiplier will be close to initialBoostMultiplier
            // Near the end, it will approach the effectiveMultiplier
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure early boost pattern
                actualMultiplier = 1f + (maxBoostFactor * boostFactor);
            }
            else
            {
                // Blend between a small multiplier and the full multiplier based on progress
                float baseMultiplier = initialBoostMultiplier;
                float blendFactor = minBlendFactor + ((1f - minBlendFactor) * progressRatio); // Start at minBlendFactor, go to 100%
                actualMultiplier = baseMultiplier * (1f - blendFactor) + effectiveMultiplier * blendFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the early boost formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate how far through the level progression we are (0 to 1)
                float progressRatio = Mathf.Clamp01((float)(level - startingLevel) / (maxLevel - startingLevel));
                
                // Early boost - higher boost at the beginning, gradually transitioning to normal
                float boostFactor = 1f - progressRatio;
                
                // Calculate the actual multiplier with the boost effect
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure early boost pattern
                    rawValue = 1f + (maxBoostFactor * boostFactor);
                }
                else
                {
                    // Blend between a small multiplier and the full multiplier based on progress
                    float baseMultiplier = initialBoostMultiplier;
                    float blendFactor = minBlendFactor + ((1f - minBlendFactor) * progressRatio); // Start at minBlendFactor, go to 100%
                    rawValue = baseMultiplier * (1f - blendFactor) + effectiveMultiplier * blendFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
