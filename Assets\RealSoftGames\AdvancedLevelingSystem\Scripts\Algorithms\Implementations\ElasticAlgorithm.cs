using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Elastic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Elastic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Elastic Algorithm", order = 128)]
    public class ElasticAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Oscillation frequency (higher values create more oscillations)")]
        [Range(1f, 20f)]
        public float frequency = 8f;
        
        [Tooltip("Damping factor (higher values make oscillations diminish faster)")]
        [Range(1f, 10f)]
        public float dampingFactor = 5f;
        
        [Tooltip("Base amplitude of the oscillations (higher values create larger oscillations)")]
        [Range(0.01f, 0.5f)]
        public float baseAmplitude = 0.15f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Growth component factor for zero levelUpMultiplier")]
        [Range(0.01f, 0.2f)]
        public float zeroGrowthFactor = 0.05f;
        
        [Tooltip("Growth component factor for non-zero levelUpMultiplier")]
        [Range(0.01f, 0.5f)]
        public float growthFactor = 0.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Elastic";
            description = "Creates an elastic bouncing effect where progression speeds up then slows down like a rubber band stretching and contracting.";
            formulaExplanation = "Formula: Uses damped oscillation with exponential decay\n\nCreates an elastic bouncing effect where progression speeds up then slows down, with the oscillations gradually diminishing over time, like a rubber band that stretches and contracts with decreasing intensity.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        
        /// <summary>
        /// Calculates the next experience requirement using the elastic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the elastic effect - damped oscillation with exponential decay
            float oscillation = Mathf.Sin(normalizedPosition * frequency * Mathf.PI);
            float damping = Mathf.Exp(-normalizedPosition * dampingFactor);
            float elasticValue = oscillation * damping * baseAmplitude;
            
            // Calculate the elastic factor
            float elasticFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure elastic pattern
                // with a smaller range to avoid excessive growth
                float baseMultiplier = zeroBaseMultiplier;
                float growthComponent = zeroGrowthFactor * normalizedPosition; // Small growth component
                
                elasticFactor = baseMultiplier + elasticValue + growthComponent;
                
                // Ensure we have at least some increase
                elasticFactor = Mathf.Max(elasticFactor, 1.01f);
            }
            else
            {
                // Scale the elastic effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                float growthComponent = growthFactor * effectiveMultiplier * normalizedPosition; // Growth component
                
                elasticFactor = baseMultiplier + (elasticValue * scaledAmplitude) + growthComponent;
                
                // Ensure we have at least some increase
                elasticFactor = Mathf.Max(elasticFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * elasticFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the elastic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the elastic effect - damped oscillation with exponential decay
                float oscillation = Mathf.Sin(normalizedPosition * frequency * Mathf.PI);
                float damping = Mathf.Exp(-normalizedPosition * dampingFactor);
                float elasticValue = oscillation * damping * baseAmplitude;
                
                // Calculate the elastic factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure elastic pattern
                    float baseMultiplier = zeroBaseMultiplier;
                    float growthComponent = zeroGrowthFactor * normalizedPosition; // Small growth component
                    
                    rawValue = baseMultiplier + elasticValue + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the elastic effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                    float growthComponent = growthFactor * effectiveMultiplier * normalizedPosition; // Growth component
                    
                    rawValue = baseMultiplier + (elasticValue * scaledAmplitude) + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
