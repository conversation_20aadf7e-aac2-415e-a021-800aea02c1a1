using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Fibonacci leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Fibonacci Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Fibonacci Algorithm", order = 123)]
    public class FibonacciAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Scale factor for zero levelUpMultiplier (lower values create gentler curves)")]
        [Range(0.05f, 0.5f)]
        public float zeroScaleFactor = 0.1f;
        
        [Tooltip("Minimum scale factor for non-zero levelUpMultiplier")]
        [Range(0.05f, 0.5f)]
        public float minScaleFactor = 0.1f;
        
        [Tooltip("Maximum scale factor for non-zero levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float maxScaleFactor = 0.5f;
        
        // Cache for Fibonacci numbers to avoid recalculating
        private Dictionary<int, float> fibonacciCache = new Dictionary<int, float>();
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Fibonacci";
            description = "Uses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.";
            formulaExplanation = "Formula: Uses Fibonacci sequence for multiplier\n\nUses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the Fibonacci formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the Fibonacci multiplier
            // We use a normalized Fibonacci ratio to keep the growth reasonable
            float fibonacciRatio = GetNormalizedFibonacciRatio(currentLevel - startingLevel + 1);
            
            // Calculate the actual multiplier with Fibonacci pattern
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure Fibonacci pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = 1f + (fibonacciRatio - 1f) * zeroScaleFactor;
            }
            else
            {
                // Apply the Fibonacci pattern to the effective multiplier
                // Scale the effect based on the levelUpMultiplier
                float scaleFactor = Mathf.Lerp(minScaleFactor, maxScaleFactor, (effectiveMultiplier - 1f) / 0.5f);
                actualMultiplier = 1f + (fibonacciRatio - 1f) * scaleFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the Fibonacci formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the Fibonacci multiplier
                float fibonacciRatio = GetNormalizedFibonacciRatio(level - startingLevel + 1);
                
                // Calculate the actual multiplier with Fibonacci pattern
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure Fibonacci pattern
                    rawValue = 1f + (fibonacciRatio - 1f) * zeroScaleFactor;
                }
                else
                {
                    // Apply the Fibonacci pattern to the effective multiplier
                    float scaleFactor = Mathf.Lerp(minScaleFactor, maxScaleFactor, (effectiveMultiplier - 1f) / 0.5f);
                    rawValue = 1f + (fibonacciRatio - 1f) * scaleFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Gets a normalized Fibonacci ratio for the given index
        /// </summary>
        private float GetNormalizedFibonacciRatio(int index)
        {
            // Ensure index is positive
            index = Mathf.Max(1, index);
            
            // For the first two indices, return fixed values
            if (index <= 2)
                return index;
            
            // Calculate the Fibonacci number at the given index
            float fibNumber = GetFibonacciNumber(index);
            float prevFibNumber = GetFibonacciNumber(index - 1);
            
            // Return the ratio between consecutive Fibonacci numbers
            // This approaches the golden ratio (approximately 1.618) as index increases
            return fibNumber / prevFibNumber;
        }
        
        /// <summary>
        /// Gets the Fibonacci number at the given index
        /// </summary>
        private float GetFibonacciNumber(int index)
        {
            // Check if we've already calculated this Fibonacci number
            if (fibonacciCache.TryGetValue(index, out float cachedValue))
                return cachedValue;
            
            // Calculate the Fibonacci number
            float result;
            
            if (index <= 0)
                result = 0;
            else if (index == 1 || index == 2)
                result = index;
            else
                result = GetFibonacciNumber(index - 1) + GetFibonacciNumber(index - 2);
            
            // Cache the result for future use
            fibonacciCache[index] = result;
            
            return result;
        }
    }
}
