using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Fractional leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Fractional Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Fractional Algorithm", order = 121)]
    public class FractionalAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base power value (higher values create steeper initial curves)")]
        [Range(1.1f, 2.0f)]
        public float basePower = 1.5f;

        [Tooltip("Exponent numerator (higher values create steeper curves)")]
        [Range(0.5f, 2.0f)]
        public float exponentNumerator = 1.3f;

        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Fractional";
            description = "Uses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (1.5^(1.3/level))\n\nUses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.";

            // Call base implementation
            base.OnEnable();
        }

        /// <summary>
        /// Calculates the next experience requirement using the fractional formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the fractional power factor
            // This creates a curve that starts steep and gradually flattens
            float exponent = exponentNumerator / Mathf.Max(1, currentLevel);

            // Calculate the actual multiplier with fractional power
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure fractional pattern
                actualMultiplier = Mathf.Pow(basePower, exponent);
            }
            else
            {
                // Scale the fractional effect based on the levelUpMultiplier
                float scaledBase = 1f + (basePower - 1f) * (effectiveMultiplier - 1f) / 0.5f;
                actualMultiplier = Mathf.Pow(scaledBase, exponent);
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the fractional formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the fractional power factor
                float exponent = exponentNumerator / Mathf.Max(1, level);

                // Calculate the actual multiplier with fractional power
                float rawValue;

                if (Mathf.Approximately(levelUpMultiplier, 0f))
                {
                    // When levelUpMultiplier is exactly zero, use a pure fractional pattern
                    rawValue = Mathf.Pow(basePower, exponent);
                }
                else if (levelUpMultiplier > 0f && levelUpMultiplier < 1.0f)
                {
                    // For multipliers between 0 and 1, we need special handling
                    // Scale between 1.0 and basePower based on the levelUpMultiplier
                    float scaledBase = Mathf.Lerp(1.0f, basePower, levelUpMultiplier);
                    rawValue = Mathf.Pow(scaledBase, exponent);
                }
                else
                {
                    // Scale the fractional effect based on the levelUpMultiplier
                    float scaledBase = 1f + (basePower - 1f) * (effectiveMultiplier - 1f) / 0.5f;
                    rawValue = Mathf.Pow(scaledBase, exponent);
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
