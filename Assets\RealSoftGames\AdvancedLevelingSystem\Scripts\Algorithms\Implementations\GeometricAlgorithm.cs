using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Geometric leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Geometric Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Geometric Algorithm", order = 120)]
    public class GeometricAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.1f;
        
        [Tooltip("Level scaling factor (higher values create steeper curves)")]
        [Range(0.05f, 0.5f)]
        public float levelScalingFactor = 0.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Geometric";
            description = "Similar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier^(level/10))\n\nSimilar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the geometric formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the geometric growth factor
            // This creates a curve that increases the multiplier as a power of the level
            float levelFactor = (float)currentLevel * levelScalingFactor; // Scale by the configurable factor
            float growthFactor = Mathf.Pow(effectiveMultiplier, levelFactor);
            
            // Calculate the actual multiplier with geometric growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure geometric pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = Mathf.Pow(zeroBaseMultiplier, levelFactor);
            }
            else
            {
                // Use the calculated growth factor
                actualMultiplier = growthFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the geometric formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the geometric growth factor
                float levelFactor = (float)level * levelScalingFactor;
                float growthFactor = Mathf.Pow(effectiveMultiplier, levelFactor);
                
                // Calculate the actual multiplier with geometric growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure geometric pattern
                    rawValue = Mathf.Pow(zeroBaseMultiplier, levelFactor);
                }
                else
                {
                    // Use the calculated growth factor
                    rawValue = growthFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
