using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Harmonic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Harmonic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Harmonic Algorithm", order = 119)]
    public class HarmonicAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Harmonic";
            description = "Uses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(level+1)))\n\nUses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the harmonic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the harmonic factor
            // This creates a curve that decreases the growth rate as levels increase
            float harmonicFactor = 1f + (1f / (currentLevel + 1f));
            
            // Calculate the actual multiplier with harmonic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure harmonic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * harmonicFactor;
            }
            else
            {
                // Apply the harmonic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * harmonicFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the harmonic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the harmonic factor
                float harmonicFactor = 1f + (1f / (level + 1f));
                
                // Calculate the actual multiplier with harmonic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure harmonic pattern
                    rawValue = zeroBaseMultiplier * harmonicFactor;
                }
                else
                {
                    // Apply the harmonic growth to the effective multiplier
                    rawValue = effectiveMultiplier * harmonicFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
