using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Hyperbolic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Hyperbolic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Hyperbolic Algorithm", order = 118)]
    public class HyperbolicAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Hyperbolic";
            description = "Uses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - 1/(level+1)))\n\nUses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the hyperbolic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the hyperbolic growth factor
            // This creates a curve that approaches 1 as levels increase
            float hyperbolicFactor = 1f - (1f / (currentLevel + 1f));
            
            // Calculate the actual multiplier with hyperbolic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure hyperbolic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * hyperbolicFactor;
            }
            else
            {
                // Apply the hyperbolic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * hyperbolicFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the hyperbolic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the hyperbolic growth factor
                float hyperbolicFactor = 1f - (1f / (level + 1f));
                
                // Calculate the actual multiplier with hyperbolic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure hyperbolic pattern
                    rawValue = zeroBaseMultiplier * hyperbolicFactor;
                }
                else
                {
                    // Apply the hyperbolic growth to the effective multiplier
                    rawValue = effectiveMultiplier * hyperbolicFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
