using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// InverseLogarithmic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Inverse Logarithmic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Inverse Logarithmic Algorithm", order = 117)]
    public class InverseLogarithmicAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Maximum increase percentage (0.5 = 50%)")]
        [Range(0.1f, 1.0f)]
        public float maxIncreasePercentage = 0.5f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Inverse Logarithmic";
            description = "Inverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + log10(level+1)/log10(maxLevel+1) * 0.5))\n\nInverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the inverse logarithmic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the inverse logarithmic growth factor
            // This creates a curve that increases the multiplier as levels increase
            float logFactor = Mathf.Log10(currentLevel + 1) / Mathf.Log10(maxLevel + 1);
            
            // The increase is capped at the specified percentage of the multiplier
            float increaseAmount = maxIncreasePercentage * logFactor;
            
            // Calculate the actual multiplier with inverse logarithmic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure inverse logarithmic pattern
                // Start at the base multiplier and increase logarithmically
                actualMultiplier = zeroBaseMultiplier * (1f + increaseAmount);
            }
            else
            {
                // Apply the inverse logarithmic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f + increaseAmount);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the inverse logarithmic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the inverse logarithmic growth factor
                float logFactor = Mathf.Log10(level + 1) / Mathf.Log10(maxLevel + 1);
                float increaseAmount = maxIncreasePercentage * logFactor;
                
                // Calculate the actual multiplier with inverse logarithmic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure inverse logarithmic pattern
                    rawValue = zeroBaseMultiplier * (1f + increaseAmount);
                }
                else
                {
                    // Apply the inverse logarithmic growth to the effective multiplier
                    rawValue = effectiveMultiplier * (1f + increaseAmount);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
