using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Logarithmic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Logarithmic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Logarithmic Algorithm", order = 116)]
    public class LogarithmicAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.1f;

        [Tooltip("Maximum reduction percentage (0.2 = 20%)")]
        [Range(0.05f, 0.5f)]
        public float maxReductionPercentage = 0.2f;

        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Logarithmic";
            description = "Experience requirements grow more slowly at higher levels, making progression easier over time.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - log10(level)/log10(maxLevel) * 0.2))\n\nExperience requirements grow more slowly at higher levels, making progression easier over time.";

            // Call base implementation
            base.OnEnable();
        }

        /// <summary>
        /// Calculates the next experience requirement using the logarithmic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the logarithmic reduction factor
            // This creates a curve that decreases the multiplier as levels increase
            float logFactor = Mathf.Log10(Mathf.Max(1, currentLevel)) / Mathf.Log10(maxLevel);

            // The reduction is capped at the specified percentage of the multiplier
            float reductionAmount = maxReductionPercentage * logFactor;

            // Calculate the actual multiplier with logarithmic reduction
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure logarithmic pattern
                // Start at the base multiplier and decrease logarithmically
                actualMultiplier = zeroBaseMultiplier * (1f - reductionAmount);
            }
            else
            {
                // Apply the logarithmic reduction to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f - reductionAmount);
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the logarithmic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the logarithmic reduction factor
                float logFactor = Mathf.Log10(Mathf.Max(1, level)) / Mathf.Log10(maxLevel);
                float reductionAmount = maxReductionPercentage * logFactor;

                // Calculate the actual multiplier with logarithmic reduction
                float rawValue;

                if (Mathf.Approximately(levelUpMultiplier, 0f))
                {
                    // When levelUpMultiplier is exactly zero, use a pure logarithmic pattern
                    rawValue = zeroBaseMultiplier * (1f - reductionAmount);
                }
                else if (levelUpMultiplier > 0f && levelUpMultiplier < 1.0f)
                {
                    // For multipliers between 0 and 1, we need special handling
                    // Scale between 1.0 and the zero base multiplier based on the levelUpMultiplier
                    float baseMultiplier = Mathf.Lerp(1.0f, zeroBaseMultiplier, levelUpMultiplier);
                    rawValue = baseMultiplier * (1f - reductionAmount);
                }
                else
                {
                    // Apply the logarithmic reduction to the effective multiplier
                    rawValue = effectiveMultiplier * (1f - reductionAmount);
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
