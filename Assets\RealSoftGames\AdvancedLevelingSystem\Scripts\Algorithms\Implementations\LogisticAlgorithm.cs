using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Logistic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Logistic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Logistic Algorithm", order = 115)]
    public class LogisticAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Controls the steepness of the S-curve (as a fraction of max level)")]
        [Range(0.05f, 0.2f)]
        public float scaleFactor = 0.1f;
        
        [Tooltip("Midpoint position as a fraction of max level")]
        [Range(0.3f, 0.7f)]
        public float midpointFactor = 0.5f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Logistic";
            description = "Creates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(1+e^(-(level-maxLevel/2)/(maxLevel/10)))))\n\nCreates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the logistic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the logistic S-curve factor
            // This creates a curve that starts slow, accelerates in the middle, then slows down again
            
            // Calculate the midpoint and scale for the logistic function
            float midpoint = startingLevel + (maxLevel - startingLevel) * midpointFactor;
            float scale = (maxLevel - startingLevel) * scaleFactor; // Controls the steepness of the S-curve
            
            // Apply the logistic function: 1/(1+e^(-(x-midpoint)/scale))
            // This creates a smooth S-curve from 0 to 1
            float x = currentLevel - midpoint;
            float logisticFactor = 1f / (1f + Mathf.Exp(-x / scale));
            
            // Scale the logistic factor to create a more pronounced effect
            float scaledLogisticFactor = 1f + logisticFactor;
            
            // Calculate the actual multiplier with logistic growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure logistic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * scaledLogisticFactor;
            }
            else
            {
                // Apply the logistic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * scaledLogisticFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the logistic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the midpoint and scale for the logistic function
            float midpoint = startingLevel + (maxLevel - startingLevel) * midpointFactor;
            float scale = (maxLevel - startingLevel) * scaleFactor;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Apply the logistic function
                float x = level - midpoint;
                float logisticFactor = 1f / (1f + Mathf.Exp(-x / scale));
                
                // Scale the logistic factor
                float scaledLogisticFactor = 1f + logisticFactor;
                
                // Calculate the actual multiplier with logistic growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure logistic pattern
                    rawValue = zeroBaseMultiplier * scaledLogisticFactor;
                }
                else
                {
                    // Apply the logistic growth to the effective multiplier
                    rawValue = effectiveMultiplier * scaledLogisticFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
