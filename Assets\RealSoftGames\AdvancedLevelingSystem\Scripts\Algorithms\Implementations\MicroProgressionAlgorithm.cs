using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// MicroProgression leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "MicroProgression Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/MicroProgression Algorithm", order = 114)]
    public class MicroProgressionAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base offset for logarithmic calculation")]
        [Range(5, 20)]
        public int logBaseOffset = 10;
        
        [Tooltip("Fixed addition coefficient")]
        [Range(0.001f, 0.01f)]
        public float fixedAdditionCoefficient = 0.005f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Micro Progression";
            description = "Tiny increments in difficulty using logarithmic scaling to keep increases very small.";
            formulaExplanation = "Formula: requiredExp = requiredExp * log10(level+10)/log10(level+9) + min(1, requiredExp*0.005)\n\nTiny increments in difficulty using logarithmic scaling to keep increases very small.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the micro progression formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // MicroProgression uses logarithmic scaling to keep increases very small
            // The formula is designed to give tiny increases that get smaller as levels increase
            
            // Calculate the logarithmic ratio - this gives a value just slightly above 1.0
            float logRatio = Mathf.Log10(currentLevel + logBaseOffset) / Mathf.Log10(currentLevel + (logBaseOffset - 1));
            
            // Add a tiny fixed amount based on the current experience
            float fixedAddition = Mathf.Min(1, currentExperience * fixedAdditionCoefficient);
            
            // Apply the levelUpMultiplier if it's not effectively zero
            if (levelUpMultiplier > 0.01f)
            {
                // Scale the log ratio effect by the multiplier
                // This keeps the micro-progression nature but allows for adjustment
                float scaledLogEffect = (logRatio - 1f) * levelUpMultiplier + 1f;
                return Mathf.RoundToInt(currentExperience * scaledLogEffect + fixedAddition);
            }
            else
            {
                // Use the pure micro-progression formula
                return Mathf.RoundToInt(currentExperience * logRatio + fixedAddition);
            }
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the micro progression formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the logarithmic ratio
                float logRatio = Mathf.Log10(level + logBaseOffset) / Mathf.Log10(level + (logBaseOffset - 1));
                
                // Apply the levelUpMultiplier if it's not effectively zero
                float rawValue;
                
                if (levelUpMultiplier > 0.01f)
                {
                    // Scale the log ratio effect by the multiplier
                    float scaledLogEffect = (logRatio - 1f) * levelUpMultiplier + 1f;
                    rawValue = scaledLogEffect;
                }
                else
                {
                    // Use the pure micro-progression formula
                    rawValue = logRatio;
                }
                
                // Add a small fixed amount to visualize that component
                // For visualization purposes, we'll scale this up to make it visible
                float fixedAdditionEffect = 0.001f;
                rawValue += fixedAdditionEffect;
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
