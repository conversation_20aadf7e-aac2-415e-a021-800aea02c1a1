using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Milestone leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Milestone Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Milestone Algorithm", order = 113)]
    public class MilestoneAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Every X levels is a milestone")]
        [Range(2, 10)]
        public int milestoneInterval = 5;
        
        [Tooltip("Base multiplier for milestones when levelUpMultiplier is effectively zero")]
        [Range(1.1f, 1.5f)]
        public float zeroMilestoneMultiplier = 1.2f;
        
        [Tooltip("Base multiplier between milestones when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroBetweenMilestoneMultiplier = 1.05f;
        
        [Tooltip("Milestone intensity multiplier (relative to levelUpMultiplier)")]
        [Range(1.0f, 2.0f)]
        public float milestoneIntensity = 1.5f;
        
        [Tooltip("Between-milestone intensity multiplier (relative to levelUpMultiplier)")]
        [Range(0.5f, 1.0f)]
        public float betweenMilestoneIntensity = 0.8f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Milestone";
            description = "Big jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.";
            formulaExplanation = "Formula: Uses higher multiplier at milestone levels\n\nBig jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the milestone formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Check if the next level is a milestone
            bool isNextLevelMilestone = ((currentLevel + 1) % milestoneInterval == 0);
            
            // Calculate the actual multiplier with milestone jumps
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed milestone parameters
                if (isNextLevelMilestone)
                {
                    // At milestone levels, use a higher multiplier
                    actualMultiplier = zeroMilestoneMultiplier;
                }
                else
                {
                    // Between milestones, use a lower multiplier
                    actualMultiplier = zeroBetweenMilestoneMultiplier;
                }
            }
            else
            {
                // Scale the milestone effect based on the levelUpMultiplier
                if (isNextLevelMilestone)
                {
                    // At milestone levels, use a higher multiplier
                    actualMultiplier = effectiveMultiplier * milestoneIntensity;
                }
                else
                {
                    // Between milestones, use a lower multiplier
                    actualMultiplier = effectiveMultiplier * betweenMilestoneIntensity;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the milestone formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Check if this level is a milestone
                bool isLevelMilestone = (level % milestoneInterval == 0);
                
                // Calculate the actual multiplier with milestone jumps
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed milestone parameters
                    if (isLevelMilestone)
                    {
                        rawValue = zeroMilestoneMultiplier;
                    }
                    else
                    {
                        rawValue = zeroBetweenMilestoneMultiplier;
                    }
                }
                else
                {
                    // Scale the milestone effect based on the levelUpMultiplier
                    if (isLevelMilestone)
                    {
                        rawValue = effectiveMultiplier * milestoneIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * betweenMilestoneIntensity;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
