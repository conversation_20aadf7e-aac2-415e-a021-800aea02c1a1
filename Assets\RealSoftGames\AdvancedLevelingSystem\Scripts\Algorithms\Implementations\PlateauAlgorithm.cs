using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Plateau leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Plateau Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Plateau Algorithm", order = 112)]
    public class PlateauAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Total cycle length (climb + plateau)")]
        [Range(5, 20)]
        public int plateauCycle = 10;
        
        [Tooltip("How many levels the climb lasts")]
        [Range(1, 5)]
        public int climbDuration = 3;
        
        [Tooltip("Base multiplier for climbing when levelUpMultiplier is effectively zero")]
        [Range(1.05f, 1.5f)]
        public float zeroClimbMultiplier = 1.12f;
        
        [Tooltip("Base multiplier for plateau when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroPlateauMultiplier = 1.01f;
        
        [<PERSON>lt<PERSON>("Climb intensity multiplier")]
        [Range(1.0f, 2.0f)]
        public float climbIntensityBase = 1.5f;
        
        [Tooltip("How much climb intensity increases with level progression")]
        [Range(0.0f, 1.0f)]
        public float climbIntensityGrowth = 0.5f;
        
        [Tooltip("Plateau intensity multiplier (relative to levelUpMultiplier)")]
        [Range(0.1f, 0.9f)]
        public float plateauIntensity = 0.6f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Plateau";
            description = "Creates a pattern with steep climbs followed by extended plateaus, giving players time to enjoy their achievements before facing the next challenge.";
            formulaExplanation = "Formula: Combines steep growth with extended flat periods\n\nCreates a progression with short periods of rapid growth followed by longer plateaus of consistent difficulty, like climbing a mountain with flat resting areas.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the plateau formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate position in the plateau cycle
            int cyclePosition = (currentLevel - startingLevel) % plateauCycle;
            bool isClimbing = cyclePosition < climbDuration;
            
            // Calculate the plateau factor
            float plateauFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure plateau pattern
                // with a smaller range to avoid excessive growth
                if (isClimbing)
                {
                    // During climb, use a higher multiplier
                    plateauFactor = zeroClimbMultiplier;
                }
                else
                {
                    // During plateau, use a very low multiplier
                    plateauFactor = zeroPlateauMultiplier;
                }
            }
            else
            {
                // Scale the plateau effect based on the levelUpMultiplier
                if (isClimbing)
                {
                    // During climb, use a higher multiplier that increases with level
                    float climbIntensity = climbIntensityBase + normalizedPosition * climbIntensityGrowth; // Climbs get steeper
                    plateauFactor = effectiveMultiplier * climbIntensity;
                }
                else
                {
                    // During plateau, use a very low multiplier
                    plateauFactor = effectiveMultiplier * plateauIntensity;
                }
                
                // Ensure we have at least some increase
                plateauFactor = Mathf.Max(plateauFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * plateauFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the plateau formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate position in the plateau cycle
                int cyclePosition = (level - startingLevel) % plateauCycle;
                bool isClimbing = cyclePosition < climbDuration;
                
                // Calculate the plateau factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure plateau pattern
                    if (isClimbing)
                    {
                        rawValue = zeroClimbMultiplier;
                    }
                    else
                    {
                        rawValue = zeroPlateauMultiplier;
                    }
                }
                else
                {
                    // Scale the plateau effect based on the levelUpMultiplier
                    if (isClimbing)
                    {
                        float climbIntensity = climbIntensityBase + normalizedPosition * climbIntensityGrowth; // Climbs get steeper
                        rawValue = effectiveMultiplier * climbIntensity;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * plateauIntensity;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
