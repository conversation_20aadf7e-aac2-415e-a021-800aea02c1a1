using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Polynomial leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Polynomial Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Polynomial Algorithm", order = 111)]
    public class PolynomialAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Coefficient for the polynomial term")]
        [Range(0.0001f, 0.01f)]
        public float polynomialCoefficient = 0.002f;
        
        [<PERSON><PERSON><PERSON>("Power for the polynomial term (between 2 and 3)")]
        [Range(2f, 3f)]
        public float polynomialPower = 2.5f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Polynomial";
            description = "Uses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.002 * level^2.5))\n\nUses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the polynomial formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the polynomial growth factor
            // This creates a curve that increases the multiplier as a power of the level
            float levelPower = Mathf.Pow(currentLevel, polynomialPower); // Use a power between quadratic and cubic
            float growthFactor = 1f + polynomialCoefficient * levelPower;
            
            // Calculate the actual multiplier with polynomial growth
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure polynomial pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the polynomial growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the polynomial formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the polynomial growth factor
                float levelPower = Mathf.Pow(level, polynomialPower);
                float growthFactor = 1f + polynomialCoefficient * levelPower;
                
                // Calculate the actual multiplier with polynomial growth
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure polynomial pattern
                    rawValue = zeroBaseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the polynomial growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
