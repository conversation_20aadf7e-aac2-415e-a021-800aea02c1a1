using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Pulsating leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Pulsating Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Pulsating Algorithm", order = 110)]
    public class PulsatingAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Number of pulses over the level range")]
        [Range(1f, 20f)]
        public float frequency = 8f;
        
        [Tooltip("Base amplitude of the pulses")]
        [Range(0.01f, 0.2f)]
        public float baseAmplitude = 0.05f;
        
        [Tooltip("How much the amplitude grows over time")]
        [Range(0f, 10f)]
        public float amplitudeGrowth = 3f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Amplitude scaling factor based on levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float amplitudeScalingFactor = 0.5f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Pulsating";
            description = "Creates a pulsating pattern with gradually increasing intensity, like a drumbeat that gets stronger over time.";
            formulaExplanation = "Formula: Combines sine waves with increasing amplitude\n\nCreates a pulsating pattern where the intensity of pulses increases as levels progress, creating a rhythm that becomes more pronounced over time.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the pulsating formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the pulse value with increasing amplitude
            float pulseValue = Mathf.Sin(normalizedPosition * frequency * Mathf.PI);
            float growingAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
            float scaledPulse = pulseValue * growingAmplitude;
            
            // Calculate the pulsating factor
            float pulsatingFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure pulsating pattern
                // with a smaller range to avoid excessive growth
                pulsatingFactor = zeroBaseMultiplier + scaledPulse;
                
                // Ensure we have at least some increase
                pulsatingFactor = Mathf.Max(pulsatingFactor, 1.01f);
            }
            else
            {
                // Scale the pulsating effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                
                pulsatingFactor = baseMultiplier + (scaledPulse * scaledAmplitude);
                
                // Ensure we have at least some increase
                pulsatingFactor = Mathf.Max(pulsatingFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * pulsatingFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the pulsating formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression (0 to 1 range)
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the pulse value with increasing amplitude
                float pulseValue = Mathf.Sin(normalizedPosition * frequency * Mathf.PI);
                float growingAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
                float scaledPulse = pulseValue * growingAmplitude;
                
                // Calculate the pulsating factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure pulsating pattern
                    rawValue = zeroBaseMultiplier + scaledPulse;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the pulsating effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (scaledPulse * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
