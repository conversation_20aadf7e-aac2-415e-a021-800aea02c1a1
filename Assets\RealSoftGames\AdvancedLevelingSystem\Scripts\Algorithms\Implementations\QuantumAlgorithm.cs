using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Quantum leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Quantum Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Quantum Algorithm", order = 108)]
    public class QuantumAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base growth rate")]
        [Range(0.01f, 0.2f)]
        public float baseGrowth = 0.05f;
        
        [Tooltip("Amplitude of quantum fluctuations")]
        [Range(0.01f, 0.3f)]
        public float quantumAmplitude = 0.1f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [<PERSON>lt<PERSON>("Amplitude scaling factor based on levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float amplitudeScalingFactor = 0.5f;
        
        [Tooltip("Frequencies for quantum fluctuations")]
        public float[] frequencies = { 7.3f, 11.7f, 19.3f, 23.1f, 29.9f };
        
        [<PERSON><PERSON><PERSON>("Phases for quantum fluctuations")]
        public float[] phases = { 0.1f, 0.7f, 1.3f, 2.1f, 2.9f };
        
        [Tooltip("Weights for quantum fluctuations")]
        public float[] weights = { 0.5f, 0.3f, 0.2f, 0.15f, 0.1f };
        
        [Tooltip("Normalization factor for quantum fluctuations")]
        [Range(0.5f, 2.0f)]
        public float normalizationFactor = 1.25f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Quantum";
            description = "Creates a quantum uncertainty pattern with random fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.";
            formulaExplanation = "Formula: Combines deterministic growth with pseudo-random quantum fluctuations\n\nCreates a quantum uncertainty pattern with random-appearing fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.";
            
            // Initialize arrays if they're null
            if (frequencies == null || frequencies.Length == 0)
            {
                frequencies = new float[] { 7.3f, 11.7f, 19.3f, 23.1f, 29.9f };
            }
            
            if (phases == null || phases.Length == 0)
            {
                phases = new float[] { 0.1f, 0.7f, 1.3f, 2.1f, 2.9f };
            }
            
            if (weights == null || weights.Length == 0)
            {
                weights = new float[] { 0.5f, 0.3f, 0.2f, 0.15f, 0.1f };
            }
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the quantum formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the deterministic component - smooth growth curve
            float deterministicComponent = baseGrowth * normalizedPosition;
            
            // Calculate the quantum fluctuation component
            float quantumFluctuation = CalculateQuantumFluctuation(normalizedPosition);
            
            // Calculate the quantum factor
            float quantumFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure quantum pattern
                // with a smaller range to avoid excessive growth
                quantumFactor = zeroBaseMultiplier * (1f + deterministicComponent + quantumFluctuation);
                
                // Ensure we have at least some increase
                quantumFactor = Mathf.Max(quantumFactor, 1.01f);
            }
            else
            {
                // Scale the quantum effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                
                quantumFactor = baseMultiplier * (1f + deterministicComponent + (quantumFluctuation * scaledAmplitude));
                
                // Ensure we have at least some increase
                quantumFactor = Mathf.Max(quantumFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * quantumFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the quantum formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the deterministic component - smooth growth curve
                float deterministicComponent = baseGrowth * normalizedPosition;
                
                // Calculate the quantum fluctuation component
                float quantumFluctuation = CalculateQuantumFluctuation(normalizedPosition);
                
                // Calculate the quantum factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure quantum pattern
                    rawValue = zeroBaseMultiplier * (1f + deterministicComponent + quantumFluctuation);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the quantum effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier * (1f + deterministicComponent + (quantumFluctuation * scaledAmplitude));
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Calculates the quantum fluctuation component
        /// </summary>
        private float CalculateQuantumFluctuation(float normalizedPosition)
        {
            // Ensure arrays are initialized and have valid lengths
            int componentCount = Mathf.Min(
                frequencies != null ? frequencies.Length : 0,
                phases != null ? phases.Length : 0,
                weights != null ? weights.Length : 0
            );
            
            // If no valid components, return 0
            if (componentCount == 0)
            {
                return 0f;
            }
            
            // Calculate the quantum fluctuation component
            float quantumFluctuation = 0f;
            for (int i = 0; i < componentCount; i++)
            {
                // Each wave contributes to the fluctuation
                float wave = Mathf.Sin((normalizedPosition * frequencies[i] * Mathf.PI) + phases[i]);
                quantumFluctuation += wave * weights[i];
            }
            
            // Normalize the fluctuation to the range [-1, 1] and scale by amplitude
            quantumFluctuation = (quantumFluctuation / normalizationFactor) * quantumAmplitude;
            
            return quantumFluctuation;
        }
    }
}
