using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Ripple leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Ripple Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Ripple Algorithm", order = 107)]
    public class RippleAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Amplitude scaling factor based on levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float amplitudeScalingFactor = 0.5f;
        
        [Tooltip("Frequencies for each wave component")]
        public float[] frequencies = { 3f, 7f, 11f };
        
        [Tooltip("Base amplitudes for each wave component")]
        public float[] amplitudes = { 0.05f, 0.03f, 0.02f };
        
        [Tooltip("Phase shifts for each wave component")]
        public float[] phases = { 0f, 0.33f, 0.67f };
        
        [Tooltip("Amplitude growth factor based on level progression")]
        [Range(0f, 5f)]
        public float amplitudeGrowthFactor = 2f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Ripple";
            description = "Creates a ripple effect with multiple overlapping waves of different frequencies, like dropping multiple stones in a pond.";
            formulaExplanation = "Formula: Combines multiple sine waves with different frequencies\n\nCreates a complex pattern by overlaying multiple wave patterns with different frequencies and amplitudes, creating a ripple-like effect that varies throughout the progression.";
            
            // Initialize arrays if they're null
            if (frequencies == null || frequencies.Length == 0)
            {
                frequencies = new float[] { 3f, 7f, 11f };
            }
            
            if (amplitudes == null || amplitudes.Length == 0)
            {
                amplitudes = new float[] { 0.05f, 0.03f, 0.02f };
            }
            
            if (phases == null || phases.Length == 0)
            {
                phases = new float[] { 0f, 0.33f, 0.67f };
            }
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the ripple formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the combined ripple effect
            float rippleValue = CalculateRippleValue(normalizedPosition);
            
            // Calculate the ripple factor
            float rippleFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure ripple pattern
                // with a smaller range to avoid excessive growth
                rippleFactor = zeroBaseMultiplier + rippleValue;
                
                // Ensure we have at least some increase
                rippleFactor = Mathf.Max(rippleFactor, 1.01f);
            }
            else
            {
                // Scale the ripple effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                
                rippleFactor = baseMultiplier + (rippleValue * scaledAmplitude);
                
                // Ensure we have at least some increase
                rippleFactor = Mathf.Max(rippleFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * rippleFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the ripple formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the combined ripple effect
                float rippleValue = CalculateRippleValue(normalizedPosition);
                
                // Calculate the ripple factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure ripple pattern
                    rawValue = zeroBaseMultiplier + rippleValue;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the ripple effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (rippleValue * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Calculates the combined ripple value from multiple wave components
        /// </summary>
        private float CalculateRippleValue(float normalizedPosition)
        {
            // Ensure arrays are initialized and have the same length
            int waveCount = Mathf.Min(
                frequencies != null ? frequencies.Length : 0,
                amplitudes != null ? amplitudes.Length : 0,
                phases != null ? phases.Length : 0
            );
            
            // If no valid waves, return 0
            if (waveCount == 0)
            {
                return 0f;
            }
            
            // Calculate the combined ripple effect
            float rippleValue = 0f;
            for (int i = 0; i < waveCount; i++)
            {
                // Each wave has a different frequency, amplitude, and phase
                float wave = Mathf.Sin((normalizedPosition * frequencies[i] * Mathf.PI) + (phases[i] * Mathf.PI));
                
                // Scale the amplitude based on level progression
                float scaledAmplitude = amplitudes[i] * (1f + normalizedPosition * amplitudeGrowthFactor);
                
                // Add this wave to the combined ripple effect
                rippleValue += wave * scaledAmplitude;
            }
            
            return rippleValue;
        }
    }
}
