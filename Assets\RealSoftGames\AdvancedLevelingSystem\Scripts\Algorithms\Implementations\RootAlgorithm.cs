using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Root leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Square Root Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Square Root Algorithm", order = 106)]
    public class RootAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier to apply before square root calculation")]
        [Range(0.5f, 2.0f)]
        public float baseMultiplierFactor = 1.0f;
        
        [Tooltip("Root power (2 for square root, 3 for cube root, etc.)")]
        [Range(1.5f, 4.0f)]
        public float rootPower = 2.0f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Square Root";
            description = "Uses square root scaling to create a moderate progression curve that slows down at higher levels.";
            formulaExplanation = "Formula: requiredExp = requiredExp * Mathf.Sqrt(level * levelUpMultiplier)\n\nUses square root scaling to create a moderate progression curve that slows down at higher levels.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the square root formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the root factor
            float rootFactor;
            
            if (rootPower == 2.0f)
            {
                // Square root growth
                rootFactor = Mathf.Sqrt(currentLevel * effectiveMultiplier * baseMultiplierFactor);
            }
            else
            {
                // General nth root growth
                rootFactor = Mathf.Pow(currentLevel * effectiveMultiplier * baseMultiplierFactor, 1.0f / rootPower);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * rootFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the square root formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the root factor
                float rawValue;
                
                if (rootPower == 2.0f)
                {
                    // Square root growth
                    rawValue = Mathf.Sqrt(level * effectiveMultiplier * baseMultiplierFactor);
                }
                else
                {
                    // General nth root growth
                    rawValue = Mathf.Pow(level * effectiveMultiplier * baseMultiplierFactor, 1.0f / rootPower);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
