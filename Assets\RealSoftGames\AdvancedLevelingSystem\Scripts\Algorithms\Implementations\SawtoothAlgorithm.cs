using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Sawtooth leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Sawtooth Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Sawtooth Algorithm", order = 101)]
    public class SawtoothAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("The length of each cycle (how many levels before the pattern repeats)")]
        [Range(2, 10)]
        public int cycleLength = 5;
        
        [Toolt<PERSON>("The minimum multiplier in the sawtooth pattern")]
        [Range(0.5f, 1.5f)]
        public float minMultiplier = 0.9f;
        
        [Tooltip("The maximum multiplier in the sawtooth pattern")]
        [Range(1.0f, 2.0f)]
        public float maxMultiplier = 1.1f;
        
        [Toolt<PERSON>("The minimum multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.0f, 1.1f)]
        public float zeroMultiplierMin = 1.02f;
        
        [Tooltip("The maximum multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.0f, 1.2f)]
        public float zeroMultiplierMax = 1.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Sawtooth";
            description = "Creates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.";
            formulaExplanation = "Formula: Uses cyclical pattern with reset\n\nCreates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the sawtooth formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the position within the current cycle (0 to cycleLength-1)
            int cyclePosition = (currentLevel - startingLevel) % cycleLength;
            
            // Calculate the sawtooth factor
            // This creates a pattern that increases linearly then drops
            float sawtoothFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure sawtooth pattern
                // with a smaller range to avoid excessive growth
                float range = zeroMultiplierMax - zeroMultiplierMin;
                
                // Linear increase within the cycle
                sawtoothFactor = zeroMultiplierMin + (range * cyclePosition / (cycleLength - 1));
            }
            else
            {
                // Scale the sawtooth effect based on the levelUpMultiplier
                float baseMultiplier = minMultiplier * effectiveMultiplier;
                float peakMultiplier = maxMultiplier * effectiveMultiplier;
                float range = peakMultiplier - baseMultiplier;
                
                // Linear increase within the cycle
                sawtoothFactor = baseMultiplier + (range * cyclePosition / (cycleLength - 1));
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * sawtoothFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the sawtooth formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the position within the current cycle
                int cyclePosition = (level - startingLevel) % cycleLength;
                
                // Calculate the sawtooth factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure sawtooth pattern
                    float range = zeroMultiplierMax - zeroMultiplierMin;
                    
                    rawValue = zeroMultiplierMin + (range * cyclePosition / (cycleLength - 1));
                }
                else
                {
                    // Scale the sawtooth effect based on the levelUpMultiplier
                    float baseMultiplier = minMultiplier * effectiveMultiplier;
                    float peakMultiplier = maxMultiplier * effectiveMultiplier;
                    float range = peakMultiplier - baseMultiplier;
                    
                    rawValue = baseMultiplier + (range * cyclePosition / (cycleLength - 1));
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
