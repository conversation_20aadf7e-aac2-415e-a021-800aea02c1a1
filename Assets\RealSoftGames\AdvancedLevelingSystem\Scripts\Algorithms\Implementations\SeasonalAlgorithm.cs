using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Seasonal leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Seasonal Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Seasonal Algorithm", order = 105)]
    public class SeasonalAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("How many levels in each season")]
        [Range(2, 10)]
        public int seasonLength = 4;
        
        [Tooltip("Multiplier for easy seasons when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.1f)]
        public float zeroEasySeasonMultiplier = 1.03f;
        
        [Tooltip("Multiplier for hard seasons when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroHardSeasonMultiplier = 1.08f;
        
        [Tooltip("Scaling factor for easy seasons based on levelUpMultiplier")]
        [Range(0.5f, 1.0f)]
        public float easySeasonScalingFactor = 0.9f;
        
        [Tooltip("Scaling factor for hard seasons based on levelUpMultiplier")]
        [Range(1.0f, 1.5f)]
        public float hardSeasonScalingFactor = 1.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Seasonal";
            description = "Cycles between easy and hard periods, creating a rhythm to progression with periods of relief.";
            formulaExplanation = "Formula: Alternates between easy and hard multipliers based on level\n\nCycles between easy and hard periods, creating a rhythm to progression with periods of relief.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the seasonal formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the position within the current season (0 to seasonLength-1)
            int seasonPosition = (currentLevel - startingLevel) % seasonLength;
            
            // Calculate the seasonal factor
            // This creates a pattern that alternates between easy and hard periods
            float seasonalFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure seasonal pattern
                // with a smaller range to avoid excessive growth
                if (seasonPosition < seasonLength / 2)
                {
                    // Easy season
                    seasonalFactor = zeroEasySeasonMultiplier;
                }
                else
                {
                    // Hard season
                    seasonalFactor = zeroHardSeasonMultiplier;
                }
            }
            else
            {
                // Scale the seasonal effect based on the levelUpMultiplier
                if (seasonPosition < seasonLength / 2)
                {
                    // Easy season
                    seasonalFactor = effectiveMultiplier * easySeasonScalingFactor;
                }
                else
                {
                    // Hard season
                    seasonalFactor = effectiveMultiplier * hardSeasonScalingFactor;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * seasonalFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the seasonal formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the position within the current season
                int seasonPosition = (level - startingLevel) % seasonLength;
                
                // Calculate the seasonal factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure seasonal pattern
                    if (seasonPosition < seasonLength / 2)
                    {
                        // Easy season
                        rawValue = zeroEasySeasonMultiplier;
                    }
                    else
                    {
                        // Hard season
                        rawValue = zeroHardSeasonMultiplier;
                    }
                }
                else
                {
                    // Scale the seasonal effect based on the levelUpMultiplier
                    if (seasonPosition < seasonLength / 2)
                    {
                        // Easy season
                        rawValue = effectiveMultiplier * easySeasonScalingFactor;
                    }
                    else
                    {
                        // Hard season
                        rawValue = effectiveMultiplier * hardSeasonScalingFactor;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
