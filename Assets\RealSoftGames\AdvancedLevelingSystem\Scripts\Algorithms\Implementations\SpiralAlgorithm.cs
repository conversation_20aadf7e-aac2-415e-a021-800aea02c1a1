using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Spiral leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Spiral Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Spiral Algorithm", order = 104)]
    public class SpiralAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base frequency of the spiral")]
        [Range(1f, 10f)]
        public float baseFrequency = 4f;
        
        [Tooltip("How much the frequency increases")]
        [Range(0f, 10f)]
        public float frequencyGrowth = 6f;
        
        [Tooltip("Base amplitude of the spiral")]
        [Range(0.01f, 0.1f)]
        public float baseAmplitude = 0.03f;
        
        [Tooltip("How much the amplitude increases")]
        [Range(0f, 10f)]
        public float amplitudeGrowth = 5f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        [Tooltip("Growth component when levelUpMultiplier is effectively zero")]
        [Range(0.01f, 0.2f)]
        public float zeroGrowthComponent = 0.05f;
        
        [Tooltip("Amplitude scaling factor based on levelUpMultiplier")]
        [Range(0.1f, 1.0f)]
        public float amplitudeScalingFactor = 0.5f;
        
        [Tooltip("Growth component scaling factor based on levelUpMultiplier")]
        [Range(0.05f, 0.5f)]
        public float growthScalingFactor = 0.1f;
        
        [Tooltip("Exponential base for the growth component")]
        [Range(1.01f, 1.2f)]
        public float exponentialBase = 1.1f;
        
        [Tooltip("Exponential scaling factor")]
        [Range(1f, 20f)]
        public float exponentialScaling = 10f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Spiral";
            description = "Creates a spiral pattern with gradually increasing oscillations, like a spiral staircase that gets steeper as you climb.";
            formulaExplanation = "Formula: Combines exponential growth with increasing oscillations\n\nCreates a pattern that spirals upward with increasing intensity, combining both overall growth and oscillating difficulty to create a dynamic progression.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the spiral formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current frequency and amplitude
            float currentFrequency = baseFrequency + (normalizedPosition * frequencyGrowth);
            float currentAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
            
            // Calculate the spiral value - combination of sine wave and exponential growth
            float sineValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
            float exponentialGrowth = Mathf.Pow(exponentialBase, normalizedPosition * exponentialScaling) - 1f; // Exponential component
            float spiralValue = sineValue * currentAmplitude * (1f + exponentialGrowth);
            
            // Calculate the spiral factor
            float spiralFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure spiral pattern
                // with a smaller range to avoid excessive growth
                float baseMultiplier = zeroBaseMultiplier;
                float growthComponent = zeroGrowthComponent * normalizedPosition; // Small growth component
                
                spiralFactor = baseMultiplier + spiralValue + growthComponent;
                
                // Ensure we have at least some increase
                spiralFactor = Mathf.Max(spiralFactor, 1.01f);
            }
            else
            {
                // Scale the spiral effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                float growthComponent = growthScalingFactor * effectiveMultiplier * normalizedPosition; // Growth component
                
                spiralFactor = baseMultiplier + (spiralValue * scaledAmplitude) + growthComponent;
                
                // Ensure we have at least some increase
                spiralFactor = Mathf.Max(spiralFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * spiralFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the spiral formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current frequency and amplitude
                float currentFrequency = baseFrequency + (normalizedPosition * frequencyGrowth);
                float currentAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
                
                // Calculate the spiral value - combination of sine wave and exponential growth
                float sineValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
                float exponentialGrowth = Mathf.Pow(exponentialBase, normalizedPosition * exponentialScaling) - 1f; // Exponential component
                float spiralValue = sineValue * currentAmplitude * (1f + exponentialGrowth);
                
                // Calculate the spiral factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure spiral pattern
                    float baseMultiplier = zeroBaseMultiplier;
                    float growthComponent = zeroGrowthComponent * normalizedPosition; // Small growth component
                    
                    rawValue = baseMultiplier + spiralValue + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the spiral effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * amplitudeScalingFactor; // Scale amplitude with multiplier
                    float growthComponent = growthScalingFactor * effectiveMultiplier * normalizedPosition; // Growth component
                    
                    rawValue = baseMultiplier + (spiralValue * scaledAmplitude) + growthComponent;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
