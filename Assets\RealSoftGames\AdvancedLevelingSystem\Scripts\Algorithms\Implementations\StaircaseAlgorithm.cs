using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Staircase leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Staircase Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Staircase Algorithm", order = 103)]
    public class StaircaseAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("How many levels in each initial step")]
        [Range(1, 10)]
        public int initialStepSize = 3;
        
        [Tooltip("Maximum step size")]
        [Range(1, 15)]
        public int maxStepSize = 5;
        
        [Tooltip("How quickly the step size increases")]
        [Range(0.1f, 1.0f)]
        public float stepGrowthRate = 0.2f;
        
        [Tooltip("Multiplier at step boundaries when levelUpMultiplier is effectively zero")]
        [Range(1.05f, 1.5f)]
        public float zeroBoundaryMultiplier = 1.15f;
        
        [Tooltip("Multiplier within steps when levelUpMultiplier is effectively zero")]
        [Range(1.0f, 1.05f)]
        public float zeroPlateauMultiplier = 1.01f;
        
        [Tooltip("Base step height multiplier at boundaries")]
        [Range(1.0f, 2.0f)]
        public float baseStepHeight = 1.5f;
        
        [Tooltip("How much the step height increases with level progression")]
        [Range(0.0f, 1.0f)]
        public float stepHeightGrowth = 0.5f;
        
        [Tooltip("Multiplier within steps as a factor of the levelUpMultiplier")]
        [Range(0.5f, 1.0f)]
        public float plateauMultiplierFactor = 0.7f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Staircase";
            description = "Creates a staircase pattern with plateaus of consistent difficulty followed by significant jumps, giving players time to adjust to each new difficulty tier.";
            formulaExplanation = "Formula: Uses step functions with increasing step heights\n\nCreates a progression with flat plateaus followed by significant jumps, like climbing a staircase with increasingly taller steps.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the staircase formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current step size based on level progression
            int currentStepSize = Mathf.FloorToInt(initialStepSize + (maxStepSize - initialStepSize) * normalizedPosition * stepGrowthRate);
            currentStepSize = Mathf.Clamp(currentStepSize, initialStepSize, maxStepSize);
            
            // Determine if we're at a step boundary
            bool isStepBoundary = (currentLevel - startingLevel) % currentStepSize == 0;
            
            // Calculate the staircase factor
            float staircaseFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure staircase pattern
                // with smaller steps to avoid excessive growth
                if (isStepBoundary)
                {
                    // At step boundaries, use a higher multiplier
                    staircaseFactor = zeroBoundaryMultiplier;
                }
                else
                {
                    // Within steps, use a very small multiplier for the plateau effect
                    staircaseFactor = zeroPlateauMultiplier;
                }
            }
            else
            {
                // Scale the staircase effect based on the levelUpMultiplier
                if (isStepBoundary)
                {
                    // At step boundaries, use a higher multiplier that increases with level
                    float stepHeight = baseStepHeight + normalizedPosition * stepHeightGrowth; // Steps get taller
                    staircaseFactor = effectiveMultiplier * stepHeight;
                }
                else
                {
                    // Within steps, use a lower multiplier for the plateau effect
                    staircaseFactor = effectiveMultiplier * plateauMultiplierFactor;
                }
                
                // Ensure we have at least some increase
                staircaseFactor = Mathf.Max(staircaseFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * staircaseFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the staircase formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current step size based on level progression
                int currentStepSize = Mathf.FloorToInt(initialStepSize + (maxStepSize - initialStepSize) * normalizedPosition * stepGrowthRate);
                currentStepSize = Mathf.Clamp(currentStepSize, initialStepSize, maxStepSize);
                
                // Determine if we're at a step boundary
                bool isStepBoundary = (level - startingLevel) % currentStepSize == 0;
                
                // Calculate the staircase factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure staircase pattern
                    if (isStepBoundary)
                    {
                        rawValue = zeroBoundaryMultiplier;
                    }
                    else
                    {
                        rawValue = zeroPlateauMultiplier;
                    }
                }
                else
                {
                    // Scale the staircase effect based on the levelUpMultiplier
                    if (isStepBoundary)
                    {
                        float stepHeight = baseStepHeight + normalizedPosition * stepHeightGrowth; // Steps get taller
                        rawValue = effectiveMultiplier * stepHeight;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * plateauMultiplierFactor;
                    }
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
