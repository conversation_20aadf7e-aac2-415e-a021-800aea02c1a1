using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Stepped leveling algorithm extension for LevelingAlgorithmBase
    /// </summary>
    [CreateAssetMenu(fileName = "Stepped Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Stepped Algorithm", order = 100)]
    public class SteppedAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("The step size (how many levels between jumps)")]
        [Range(1, 10)]
        public int stepSize = 5;
        
        [Tooltip("The multiplier to use at step thresholds")]
        [Range(1.0f, 2.0f)]
        public float stepThresholdMultiplier = 1.5f;
        
        [Tooltip("The multiplier to use between step thresholds")]
        [Range(0.5f, 1.0f)]
        public float betweenStepsMultiplier = 0.8f;
        
        [Tooltip("The base multiplier to use when levelUpMultiplier is effectively zero")]
        [Range(1.0f, 1.5f)]
        public float zeroMultiplierStepThreshold = 1.15f;
        
        [Tooltip("The base multiplier to use between steps when levelUpMultiplier is effectively zero")]
        [Range(1.0f, 1.1f)]
        public float zeroMultiplierBetweenSteps = 1.03f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Stepped";
            description = "Creates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.";
            formulaExplanation = "Formula: Uses higher multiplier at specific level thresholds\n\nCreates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the stepped formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the actual multiplier with stepped progression
            float actualMultiplier;
            
            // Check if we're at a step threshold
            bool isStepThreshold = (currentLevel % stepSize == 0);
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure stepped pattern
                // with smaller steps to avoid excessive growth
                if (isStepThreshold)
                {
                    // At step thresholds, use a higher multiplier
                    actualMultiplier = zeroMultiplierStepThreshold;
                }
                else
                {
                    // Between thresholds, use a lower multiplier
                    actualMultiplier = zeroMultiplierBetweenSteps;
                }
            }
            else
            {
                // Scale the step effect based on the levelUpMultiplier
                if (isStepThreshold)
                {
                    // At step thresholds, use a higher multiplier
                    actualMultiplier = effectiveMultiplier * stepThresholdMultiplier;
                }
                else
                {
                    // Between thresholds, use a lower multiplier
                    actualMultiplier = effectiveMultiplier * betweenStepsMultiplier;
                }
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the stepped formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Check if we're at a step threshold
                bool isStepThreshold = (level % stepSize == 0);
                
                // Calculate the actual multiplier with stepped progression
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure stepped pattern
                    if (isStepThreshold)
                    {
                        rawValue = zeroMultiplierStepThreshold;
                    }
                    else
                    {
                        rawValue = zeroMultiplierBetweenSteps;
                    }
                }
                else
                {
                    // Scale the step effect based on the levelUpMultiplier
                    if (isStepThreshold)
                    {
                        rawValue = effectiveMultiplier * stepThresholdMultiplier;
                    }
                    else
                    {
                        rawValue = effectiveMultiplier * betweenStepsMultiplier;
                    }
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
