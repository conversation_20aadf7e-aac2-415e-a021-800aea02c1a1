using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Tiered leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Tiered Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Tiered Algorithm", order = 102)]
    public class TieredAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier for the beginner tier")]
        [Range(1.0f, 1.5f)]
        public float beginnerTierMultiplier = 1.05f;
        
        [Tooltip("Base multiplier for the intermediate tier")]
        [Range(1.0f, 1.5f)]
        public float intermediateTierMultiplier = 1.1f;
        
        [Tooltip("Base multiplier for the advanced tier")]
        [Range(1.0f, 1.5f)]
        public float advancedTierMultiplier = 1.15f;
        
        [Tooltip("Base multiplier for the expert tier")]
        [Range(1.0f, 1.5f)]
        public float expertTierMultiplier = 1.2f;
        
        [Tooltip("Base multiplier for the master tier")]
        [Range(1.0f, 1.5f)]
        public float masterTierMultiplier = 1.25f;
        
        [Tooltip("Base multiplier scaling factor when using levelUpMultiplier")]
        [Range(0.5f, 1.0f)]
        public float baseMultiplierScale = 0.8f;
        
        [Tooltip("Tier increment scaling factor when using levelUpMultiplier")]
        [Range(0.05f, 0.2f)]
        public float tierIncrementScale = 0.1f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Tiered";
            description = "Progression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.";
            formulaExplanation = "Formula: Varies based on level tier\n\nProgression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.";
            
            // Call base implementation
            base.OnEnable();
        }
        
        /// <summary>
        /// Calculates the next experience requirement using the tiered formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate which tier the current level falls into
            int tier = GetTier(currentLevel, startingLevel, maxLevel);
            
            // Calculate the actual multiplier based on the tier
            float actualMultiplier;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use fixed tier multipliers
                switch (tier)
                {
                    case 0: // Beginner tier
                        actualMultiplier = beginnerTierMultiplier;
                        break;
                    case 1: // Intermediate tier
                        actualMultiplier = intermediateTierMultiplier;
                        break;
                    case 2: // Advanced tier
                        actualMultiplier = advancedTierMultiplier;
                        break;
                    case 3: // Expert tier
                        actualMultiplier = expertTierMultiplier;
                        break;
                    default: // Master tier
                        actualMultiplier = masterTierMultiplier;
                        break;
                }
            }
            else
            {
                // Scale the tier multipliers based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier * baseMultiplierScale;
                float tierIncrement = effectiveMultiplier * tierIncrementScale;
                
                actualMultiplier = baseMultiplier + (tier * tierIncrement);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the tiered formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate which tier the level falls into
                int tier = GetTier(level, startingLevel, maxLevel);
                
                // Calculate the actual multiplier based on the tier
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use fixed tier multipliers
                    switch (tier)
                    {
                        case 0: // Beginner tier
                            rawValue = beginnerTierMultiplier;
                            break;
                        case 1: // Intermediate tier
                            rawValue = intermediateTierMultiplier;
                            break;
                        case 2: // Advanced tier
                            rawValue = advancedTierMultiplier;
                            break;
                        case 3: // Expert tier
                            rawValue = expertTierMultiplier;
                            break;
                        default: // Master tier
                            rawValue = masterTierMultiplier;
                            break;
                    }
                }
                else
                {
                    // Scale the tier multipliers based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier * baseMultiplierScale;
                    float tierIncrement = effectiveMultiplier * tierIncrementScale;
                    
                    rawValue = baseMultiplier + (tier * tierIncrement);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
        
        /// <summary>
        /// Determines which tier a level falls into
        /// </summary>
        private int GetTier(int level, int startingLevel, int maxLevel)
        {
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Define 5 tiers (0-4)
            int tierSize = Mathf.Max(1, totalLevels / 5);
            
            // Calculate the tier based on the level's position in the range
            int levelPosition = level - startingLevel;
            int tier = Mathf.Min(4, levelPosition / tierSize);
            
            return tier;
        }
    }
}
