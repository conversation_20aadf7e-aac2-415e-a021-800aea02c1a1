using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Wave leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Wave Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Wave Algorithm", order = 124)]
    public class WaveAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base frequency of the waves")]
        [Range(1f, 10f)]
        public float baseFrequency = 5f;
        
        [Tooltip("How much the frequency increases with level progression")]
        [Range(0f, 10f)]
        public float frequencyGrowth = 3f;
        
        [Tooltip("Base amplitude of the waves")]
        [Range(0.01f, 0.2f)]
        public float baseAmplitude = 0.05f;
        
        [Tooltip("How much the amplitude increases with level progression")]
        [Range(0f, 10f)]
        public float amplitudeGrowth = 4f;
        
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;
        
        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Wave";
            description = "Creates a smooth wave pattern with gradually increasing amplitude, like ocean waves that grow larger over time.";
            formulaExplanation = "Formula: Uses sine waves with increasing amplitude and frequency\n\nCreates a wave pattern where both the height and frequency of waves increase as levels progress, creating a more dynamic and challenging progression.";
            
            // Call base implementation
            base.OnEnable();
        }
        
    
        /// <summary>
        /// Calculates the next experience requirement using the wave formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate normalized position in the level progression (0 to 1 range)
            float normalizedPosition = Mathf.Clamp01((float)(currentLevel - startingLevel) / (maxLevel - startingLevel));
            
            // Calculate the current frequency and amplitude
            float currentFrequency = baseFrequency + (normalizedPosition * frequencyGrowth);
            float currentAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
            
            // Calculate the wave value
            float waveValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
            float scaledWave = waveValue * currentAmplitude;
            
            // Calculate the wave factor
            float waveFactor;
            
            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure wave pattern
                // with a smaller range to avoid excessive growth
                waveFactor = zeroBaseMultiplier + scaledWave;
                
                // Ensure we have at least some increase
                waveFactor = Mathf.Max(waveFactor, 1.01f);
            }
            else
            {
                // Scale the wave effect based on the levelUpMultiplier
                float baseMultiplier = effectiveMultiplier;
                float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                
                waveFactor = baseMultiplier + (scaledWave * scaledAmplitude);
                
                // Ensure we have at least some increase
                waveFactor = Mathf.Max(waveFactor, 1.01f);
            }
            
            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * waveFactor);
            
            // Ensure we always increase by at least 1
            return Mathf.Max(nextRequirement, currentExperience + 1);
        }
        
        /// <summary>
        /// Calculates raw formula values for visualization using the wave formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();
            
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);
            
            // Calculate the total number of levels
            int totalLevels = maxLevel - startingLevel + 1;
            
            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate normalized position in the level progression
                float normalizedPosition = (float)(level - startingLevel) / (totalLevels - 1);
                
                // Calculate the current frequency and amplitude
                float currentFrequency = baseFrequency + (normalizedPosition * frequencyGrowth);
                float currentAmplitude = baseAmplitude * (1f + normalizedPosition * amplitudeGrowth);
                
                // Calculate the wave value
                float waveValue = Mathf.Sin(normalizedPosition * currentFrequency * Mathf.PI);
                float scaledWave = waveValue * currentAmplitude;
                
                // Calculate the wave factor
                float rawValue;
                
                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure wave pattern
                    rawValue = zeroBaseMultiplier + scaledWave;
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                else
                {
                    // Scale the wave effect based on the levelUpMultiplier
                    float baseMultiplier = effectiveMultiplier;
                    float scaledAmplitude = (effectiveMultiplier - 1.0f) * 0.5f; // Scale amplitude with multiplier
                    
                    rawValue = baseMultiplier + (scaledWave * scaledAmplitude);
                    
                    // Ensure we have at least some increase
                    rawValue = Mathf.Max(rawValue, 1.01f);
                }
                
                // Add to the list
                rawValues.Add(rawValue);
            }
            
            return rawValues;
        }
    }
}
