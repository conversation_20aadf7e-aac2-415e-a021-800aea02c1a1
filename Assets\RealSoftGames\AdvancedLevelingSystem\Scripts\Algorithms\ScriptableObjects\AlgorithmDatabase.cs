using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Database of leveling algorithms
    /// </summary>
    [CreateAssetMenu(fileName = "AlgorithmDatabase", menuName = "RealSoftGames/Advanced Leveling System/Algorithm Database", order = 1)]
    public class AlgorithmDatabase : ScriptableObject
    {
        /// <summary>
        /// List of all algorithms in the database
        /// </summary>
        public List<LevelingAlgorithmBase> algorithms = new List<LevelingAlgorithmBase>();

        /// <summary>
        /// Gets an algorithm by ID
        /// </summary>
        public LevelingAlgorithmBase GetAlgorithmByID(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            foreach (var algorithm in algorithms)
            {
                if (algorithm != null && algorithm.uniqueID == id)
                    return algorithm;
            }

            return null;
        }

        /// <summary>
        /// Gets an algorithm by name
        /// </summary>
        public LevelingAlgorithmBase GetAlgorithmByName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;

            foreach (var algorithm in algorithms)
            {
                if (algorithm != null && algorithm.algorithmName == name)
                    return algorithm;
            }

            return null;
        }

        /// <summary>
        /// Adds an algorithm to the database
        /// </summary>
        public void AddAlgorithm(LevelingAlgorithmBase algorithm)
        {
            if (algorithm == null)
                return;

            // Check if the algorithm is already in the database
            if (algorithms.Contains(algorithm))
                return;

            algorithms.Add(algorithm);

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }

        /// <summary>
        /// Removes an algorithm from the database
        /// </summary>
        public void RemoveAlgorithm(LevelingAlgorithmBase algorithm)
        {
            if (algorithm == null)
                return;

            algorithms.Remove(algorithm);

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }

        /// <summary>
        /// Clears all algorithms from the database
        /// </summary>
        public void ClearAlgorithms()
        {
            algorithms.Clear();

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }
    }
}
