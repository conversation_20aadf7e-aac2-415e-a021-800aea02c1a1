using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Utility class for comparing leveling algorithms
    /// </summary>
    public static class AlgorithmComparison
    {
        /// <summary>
        /// Compares two algorithms and returns the difference in experience requirements at each level
        /// </summary>
        /// <param name="algorithm1">The first algorithm</param>
        /// <param name="algorithm2">The second algorithm</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>A list of differences in experience requirements for each level</returns>
        public static List<int> CompareExperienceRequirements(
            LevelingAlgorithmBase algorithm1,
            LevelingAlgorithmBase algorithm2,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm1 == null || algorithm2 == null)
            {
                Debug.LogError("AlgorithmComparison: One or both algorithms are null");
                return new List<int>();
            }

            // Calculate the experience requirements for both algorithms
            List<int> requirements1 = AlgorithmUtilities.CalculateExperienceRequirements(
                algorithm1, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            List<int> requirements2 = AlgorithmUtilities.CalculateExperienceRequirements(
                algorithm2, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            // Calculate the differences
            List<int> differences = new List<int>();
            int minCount = Mathf.Min(requirements1.Count, requirements2.Count);

            for (int i = 0; i < minCount; i++)
            {
                differences.Add(requirements2[i] - requirements1[i]);
            }

            return differences;
        }

        /// <summary>
        /// Compares two algorithms and returns the percentage difference in experience requirements at each level
        /// </summary>
        /// <param name="algorithm1">The first algorithm</param>
        /// <param name="algorithm2">The second algorithm</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>A list of percentage differences in experience requirements for each level</returns>
        public static List<float> CompareExperienceRequirementsPercentage(
            LevelingAlgorithmBase algorithm1,
            LevelingAlgorithmBase algorithm2,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm1 == null || algorithm2 == null)
            {
                Debug.LogError("AlgorithmComparison: One or both algorithms are null");
                return new List<float>();
            }

            // Calculate the experience requirements for both algorithms
            List<int> requirements1 = AlgorithmUtilities.CalculateExperienceRequirements(
                algorithm1, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            List<int> requirements2 = AlgorithmUtilities.CalculateExperienceRequirements(
                algorithm2, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            // Calculate the percentage differences
            List<float> percentageDifferences = new List<float>();
            int minCount = Mathf.Min(requirements1.Count, requirements2.Count);

            for (int i = 0; i < minCount; i++)
            {
                if (requirements1[i] == 0)
                {
                    percentageDifferences.Add(0);
                }
                else
                {
                    float percentageDifference = ((float)requirements2[i] - requirements1[i]) / requirements1[i] * 100f;
                    percentageDifferences.Add(percentageDifference);
                }
            }

            return percentageDifferences;
        }

        /// <summary>
        /// Compares the total experience required to reach a specific level for two algorithms
        /// </summary>
        /// <param name="algorithm1">The first algorithm</param>
        /// <param name="algorithm2">The second algorithm</param>
        /// <param name="targetLevel">The target level</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>The difference in total experience required to reach the target level</returns>
        public static int CompareTotalExperienceToLevel(
            LevelingAlgorithmBase algorithm1,
            LevelingAlgorithmBase algorithm2,
            int targetLevel,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm1 == null || algorithm2 == null)
            {
                Debug.LogError("AlgorithmComparison: One or both algorithms are null");
                return 0;
            }

            // Calculate the total experience required for both algorithms
            int totalExperience1 = AlgorithmUtilities.CalculateTotalExperienceToLevel(
                algorithm1, targetLevel, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            int totalExperience2 = AlgorithmUtilities.CalculateTotalExperienceToLevel(
                algorithm2, targetLevel, startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            // Return the difference
            return totalExperience2 - totalExperience1;
        }
    }
}
