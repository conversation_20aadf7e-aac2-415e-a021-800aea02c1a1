using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Utility class for converting between different algorithm types
    /// </summary>
    public static class AlgorithmConverter
    {
        /// <summary>
        /// Converts a coded formula algorithm to a drawn pattern algorithm
        /// </summary>
        /// <param name="codedAlgorithm">The coded formula algorithm to convert</param>
        /// <param name="numPoints">The number of points to sample (default: 10)</param>
        /// <param name="startingLevel">The starting level for sampling (default: 1)</param>
        /// <param name="maxLevel">The maximum level for sampling (default: 50)</param>
        /// <param name="levelUpMultiplier">The level up multiplier to use (default: 1.1)</param>
        /// <returns>A new drawn pattern algorithm based on the coded formula</returns>
        public static LevelingAlgorithmBase ConvertCodedToDrawn(
            LevelingAlgorithmBase codedAlgorithm,
            int numPoints = 10,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (codedAlgorithm == null || codedAlgorithm.algorithmType != AlgorithmEnums.AlgorithmType.CodedFormula)
                return null;

            // Create a new drawn pattern algorithm
            LevelingAlgorithmBase drawnAlgorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

            // Set algorithm type to drawn pattern
            drawnAlgorithm.algorithmType = AlgorithmEnums.AlgorithmType.DrawnPattern;

            // Copy basic properties
            drawnAlgorithm.algorithmName = codedAlgorithm.algorithmName + " (Drawn)";
            drawnAlgorithm.description = codedAlgorithm.description;
            drawnAlgorithm.formulaExplanation = codedAlgorithm.formulaExplanation + "\n\nConverted to drawn pattern.";
            drawnAlgorithm.difficultyRating = codedAlgorithm.difficultyRating;

            // Calculate raw formula values
            List<float> rawValues = codedAlgorithm.CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);

            // Sample points from the raw values
            drawnAlgorithm.points.Clear();

            if (numPoints <= 1)
                numPoints = 2; // Ensure at least 2 points

            for (int i = 0; i < numPoints; i++)
            {
                // Calculate the normalized position (0-1)
                float normalizedPos = i / (float)(numPoints - 1);

                // Calculate the level at this position
                int level = Mathf.RoundToInt(startingLevel + normalizedPos * (maxLevel - startingLevel));

                // Ensure level is within bounds
                level = Mathf.Clamp(level, startingLevel, maxLevel);

                // Calculate the index in the raw values list
                int index = level - startingLevel;

                // Ensure index is within bounds
                index = Mathf.Clamp(index, 0, rawValues.Count - 1);

                // Add the point
                drawnAlgorithm.points.Add(new Vector2(normalizedPos, rawValues[index]));
            }

            // Set interpolation method
            drawnAlgorithm.interpolationMethod = AlgorithmEnums.InterpolationMethod.Cubic;

            return drawnAlgorithm;
        }

        /// <summary>
        /// Converts a drawn pattern algorithm to a coded formula algorithm
        /// This is a best-effort conversion and may not perfectly match the original
        /// </summary>
        /// <param name="drawnAlgorithm">The drawn pattern algorithm to convert</param>
        /// <returns>A new coded formula algorithm that approximates the drawn pattern</returns>
        public static LevelingAlgorithmBase ConvertDrawnToCoded(LevelingAlgorithmBase drawnAlgorithm)
        {
            if (drawnAlgorithm == null || drawnAlgorithm.algorithmType != AlgorithmEnums.AlgorithmType.DrawnPattern || drawnAlgorithm.points.Count < 2)
                return null;

            // Create a new coded formula algorithm
            LevelingAlgorithmBase codedAlgorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

            // Set algorithm type to coded formula
            codedAlgorithm.algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;

            // Copy basic properties
            codedAlgorithm.algorithmName = drawnAlgorithm.algorithmName + " (Coded)";
            codedAlgorithm.description = drawnAlgorithm.description;
            codedAlgorithm.formulaExplanation = drawnAlgorithm.formulaExplanation + "\n\nConverted to coded formula.";
            codedAlgorithm.difficultyRating = drawnAlgorithm.difficultyRating;

            // Sort points by X value
            List<Vector2> sortedPoints = new List<Vector2>(drawnAlgorithm.points);
            sortedPoints.Sort((a, b) => a.x.CompareTo(b.x));

            // Calculate base multiplier (average Y value)
            float avgY = 0;
            foreach (var point in sortedPoints)
            {
                avgY += point.y;
            }
            avgY /= sortedPoints.Count;
            codedAlgorithm.baseMultiplier = avgY;

            // Calculate level multiplier factor (slope of best-fit line)
            if (sortedPoints.Count >= 2)
            {
                Vector2 first = sortedPoints[0];
                Vector2 last = sortedPoints[sortedPoints.Count - 1];
                float slope = (last.y - first.y) / (last.x - first.x);
                codedAlgorithm.levelMultiplierFactor = slope * 0.05f; // Scale down for better results
            }

            // Analyze for patterns
            AnalyzeForSineWave(sortedPoints, codedAlgorithm);
            AnalyzeForHeartbeat(sortedPoints, codedAlgorithm);
            AnalyzeForZigzag(sortedPoints, codedAlgorithm);

            return codedAlgorithm;
        }

        /// <summary>
        /// Analyzes points for sine wave patterns
        /// </summary>
        private static void AnalyzeForSineWave(List<Vector2> points, LevelingAlgorithmBase algorithm)
        {
            if (points.Count < 4)
                return;

            // Calculate moving averages to detect oscillation
            List<float> movingAvgs = new List<float>();
            for (int i = 1; i < points.Count - 1; i++)
            {
                movingAvgs.Add((points[i-1].y + points[i].y + points[i+1].y) / 3f);
            }

            // Calculate differences from moving average
            List<float> diffs = new List<float>();
            for (int i = 0; i < movingAvgs.Count; i++)
            {
                diffs.Add(points[i+1].y - movingAvgs[i]);
            }

            // Count sign changes to detect oscillation
            int signChanges = 0;
            for (int i = 1; i < diffs.Count; i++)
            {
                if (Mathf.Sign(diffs[i]) != Mathf.Sign(diffs[i-1]))
                {
                    signChanges++;
                }
            }

            // If we have enough sign changes, it might be a sine wave
            if (signChanges >= 2)
            {
                algorithm.useSineWave = true;

                // Estimate amplitude (half the average peak-to-peak)
                float maxDiff = 0;
                float minDiff = 0;

                if (diffs.Count > 0)
                {
                    maxDiff = diffs[0];
                    minDiff = diffs[0];

                    foreach (var diff in diffs)
                    {
                        if (diff > maxDiff) maxDiff = diff;
                        if (diff < minDiff) minDiff = diff;
                    }
                }

                algorithm.sineAmplitude = (maxDiff - minDiff) / 2f;

                // Estimate period (points.Count / signChanges * 2)
                algorithm.sinePeriod = points.Count / (float)signChanges * 2f;
            }
        }

        /// <summary>
        /// Analyzes points for heartbeat patterns
        /// </summary>
        private static void AnalyzeForHeartbeat(List<Vector2> points, LevelingAlgorithmBase algorithm)
        {
            if (points.Count < 6)
                return;

            // Look for sharp peaks
            List<int> peakIndices = new List<int>();
            for (int i = 1; i < points.Count - 1; i++)
            {
                if (points[i].y > points[i-1].y && points[i].y > points[i+1].y)
                {
                    peakIndices.Add(i);
                }
            }

            // If we have at least 2 peaks, check if they're evenly spaced
            if (peakIndices.Count >= 2)
            {
                // Calculate average distance between peaks
                float avgDistance = 0;
                for (int i = 1; i < peakIndices.Count; i++)
                {
                    avgDistance += peakIndices[i] - peakIndices[i-1];
                }
                avgDistance /= peakIndices.Count - 1;

                // If peaks are relatively evenly spaced, it might be a heartbeat
                bool evenlySpaced = true;
                for (int i = 1; i < peakIndices.Count; i++)
                {
                    float distance = peakIndices[i] - peakIndices[i-1];
                    if (Mathf.Abs(distance - avgDistance) > avgDistance * 0.3f)
                    {
                        evenlySpaced = false;
                        break;
                    }
                }

                if (evenlySpaced)
                {
                    algorithm.useHeartbeat = true;

                    // Estimate amplitude (average peak height above baseline)
                    float baseline = 0;
                    foreach (var point in points)
                    {
                        baseline += point.y;
                    }
                    baseline /= points.Count;

                    float avgPeakHeight = 0;
                    foreach (int index in peakIndices)
                    {
                        avgPeakHeight += points[index].y - baseline;
                    }
                    avgPeakHeight /= peakIndices.Count;
                    algorithm.heartbeatAmplitude = avgPeakHeight;

                    // Estimate period
                    algorithm.heartbeatPeriod = points.Count / (float)peakIndices.Count;
                }
            }
        }

        /// <summary>
        /// Analyzes points for zigzag patterns
        /// </summary>
        private static void AnalyzeForZigzag(List<Vector2> points, LevelingAlgorithmBase algorithm)
        {
            if (points.Count < 4)
                return;

            // Count direction changes
            int directionChanges = 0;
            for (int i = 2; i < points.Count; i++)
            {
                float slope1 = points[i-1].y - points[i-2].y;
                float slope2 = points[i].y - points[i-1].y;

                if (Mathf.Sign(slope1) != Mathf.Sign(slope2))
                {
                    directionChanges++;
                }
            }

            // If we have enough direction changes, it might be a zigzag
            if (directionChanges >= 3 && directionChanges > points.Count / 4)
            {
                algorithm.useZigzag = true;

                // Estimate amplitude (average peak-to-peak / 2)
                float maxY = float.MinValue;
                float minY = float.MaxValue;

                foreach (var point in points)
                {
                    if (point.y > maxY) maxY = point.y;
                    if (point.y < minY) minY = point.y;
                }

                algorithm.zigzagAmplitude = (maxY - minY) / 2f;

                // Estimate period
                algorithm.zigzagPeriod = points.Count / (float)directionChanges * 2f;
            }
        }
    }
}
