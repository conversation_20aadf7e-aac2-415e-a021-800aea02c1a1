using UnityEngine;
using System.Collections.Generic;
using System;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Factory for creating algorithm instances
    /// </summary>
    public static class AlgorithmFactory
    {
        /// <summary>
        /// Creates a new drawn pattern algorithm
        /// </summary>
        /// <param name="name">Name of the algorithm</param>
        /// <param name="description">Description of the algorithm</param>
        /// <param name="formulaExplanation">Explanation of the formula</param>
        /// <param name="difficultyRating">Difficulty rating</param>
        /// <returns>A new drawn pattern algorithm</returns>
        public static LevelingAlgorithmBase CreateDrawnPatternAlgorithm(
            string name = null,
            string description = null,
            string formulaExplanation = null,
            DifficultyRating difficultyRating = null)
        {
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

            // Set algorithm type
            algorithm.algorithmType = AlgorithmEnums.AlgorithmType.DrawnPattern;

            // Set basic properties
            algorithm.algorithmName = name ?? "Custom Pattern " + DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            algorithm.description = description ?? "Custom drawn pattern algorithm.";
            algorithm.formulaExplanation = formulaExplanation ?? "Custom drawn pattern with interpolation between points.";
            algorithm.difficultyRating = difficultyRating;

            // Add default points
            algorithm.points.Clear();
            algorithm.points.Add(new Vector2(0.0f, 1.1f));
            algorithm.points.Add(new Vector2(1.0f, 1.3f));

            // Set default interpolation method
            algorithm.interpolationMethod = AlgorithmEnums.InterpolationMethod.Linear;

            return algorithm;
        }

        /// <summary>
        /// Creates a new coded formula algorithm
        /// </summary>
        /// <param name="name">Name of the algorithm</param>
        /// <param name="description">Description of the algorithm</param>
        /// <param name="formulaExplanation">Explanation of the formula</param>
        /// <param name="difficultyRating">Difficulty rating</param>
        /// <returns>A new coded formula algorithm</returns>
        public static LevelingAlgorithmBase CreateCodedFormulaAlgorithm(
            string name = null,
            string description = null,
            string formulaExplanation = null,
            DifficultyRating difficultyRating = null)
        {
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

            // Set algorithm type
            algorithm.algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;

            // Set basic properties
            algorithm.algorithmName = name ?? "Custom Formula " + DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            algorithm.description = description ?? "Custom coded formula algorithm.";
            algorithm.formulaExplanation = formulaExplanation ?? "Custom formula with configurable parameters.";
            algorithm.difficultyRating = difficultyRating;

            return algorithm;
        }

        /// <summary>
        /// Creates a linear algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateLinearAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Linear",
                "A simple linear progression with consistent level-up requirements.",
                "Experience = Previous * 1.1",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an exponential algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateExponentialAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an ExponentialAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an ExponentialAlgorithm instance if the class exists
                var exponentialAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.ExponentialAlgorithm") as LevelingAlgorithmBase;

                if (exponentialAlgorithm != null)
                {
                    // Set basic properties
                    exponentialAlgorithm.algorithmName = "Exponential";
                    exponentialAlgorithm.description = "Experience requirements grow exponentially, making higher levels significantly more challenging.";
                    exponentialAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.01 * level))\n\nExperience requirements grow exponentially, making higher levels significantly more challenging.";
                    exponentialAlgorithm.difficultyRating = difficultyRating;

                    return exponentialAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create ExponentialAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Exponential",
                "Experience requirements grow exponentially, making higher levels significantly more challenging.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.01 * level))\n\nExperience requirements grow exponentially, making higher levels significantly more challenging.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a sinusoidal algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateSinusoidalAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Sinusoidal",
                "A wave-like progression with alternating easier and harder levels.",
                "Experience = Previous * (1.1 + 0.1 * sin(Level * π/5))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.1f;
            algorithm.sinePeriod = 5f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a heartbeat algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateHeartbeatAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Heartbeat",
                "A heartbeat-like progression with double peaks.",
                "Experience = Previous * (1.1 + 0.15 * heartbeat(Level))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.15f;
            algorithm.heartbeatPeriod = 10f;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a zigzag algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateZigzagAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Zigzag",
                "A zigzag progression with sharp ups and downs.",
                "Experience = Previous * (1.1 + 0.1 * zigzag(Level))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = true;
            algorithm.zigzagAmplitude = 0.1f;
            algorithm.zigzagPeriod = 3f;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a random algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateRandomAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Random",
                "A random progression with unpredictable jumps.",
                "Experience = Previous * (1.1 + 0.05 * random(-1, 1))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = true;
            algorithm.randomAmplitude = 0.05f;
            algorithm.randomSeed = UnityEngine.Random.Range(1, 10000);

            return algorithm;
        }

        /// <summary>
        /// Creates a combined algorithm
        /// </summary>
        public static LevelingAlgorithmBase CreateCombinedAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Combined",
                "A combined progression with multiple patterns.",
                "Experience = Previous * (1.1 + 0.01 * Level + 0.1 * sin(Level * π/5) + 0.05 * heartbeat(Level))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.1f;
            algorithm.sinePeriod = 5f;
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.05f;
            algorithm.heartbeatPeriod = 10f;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a complex wave algorithm with the settings from the user's image
        /// </summary>
        public static LevelingAlgorithmBase CreateComplexWaveAlgorithm(DifficultyRating difficultyRating = null)
        {
            var algorithm = CreateCodedFormulaAlgorithm(
                "Complex Wave",
                "A complex wave pattern combining sine wave, heartbeat, and random fluctuations.",
                "Experience = Previous * (1.1 + 0.01 * Level + 0.16 * sin(Level * π/15.3) + 0.69 * heartbeat(Level, 8) + 0.89 * random(seed: 12289))",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;

            // Sine wave settings
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.16f;
            algorithm.sinePeriod = 15.3f;

            // Heartbeat settings
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.69f;
            algorithm.heartbeatPeriod = 8f;

            // Zigzag settings
            algorithm.useZigzag = false;

            // Random fluctuations settings
            algorithm.useRandomFluctuations = true;
            algorithm.randomAmplitude = 0.89f;
            algorithm.randomSeed = 12289;

            return algorithm;
        }

        /// <summary>
        /// Creates a stepped algorithm with plateaus and sudden jumps
        /// </summary>
        public static LevelingAlgorithmBase CreateSteppedAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a SteppedAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a SteppedAlgorithm instance if the class exists
                var steppedAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.SteppedAlgorithm") as LevelingAlgorithmBase;

                if (steppedAlgorithm != null)
                {
                    // Set basic properties
                    steppedAlgorithm.algorithmName = "Stepped";
                    steppedAlgorithm.description = "Creates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.";
                    steppedAlgorithm.formulaExplanation = "Formula: Uses higher multiplier at specific level thresholds\n\nCreates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.";
                    steppedAlgorithm.difficultyRating = difficultyRating;

                    return steppedAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create SteppedAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Stepped",
                "Creates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.",
                "Formula: Uses higher multiplier at specific level thresholds\n\nCreates plateaus of difficulty with sudden jumps at specific level thresholds, making progression feel like distinct tiers or ranks.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a sawtooth algorithm with cyclical linear increases and drops
        /// </summary>
        public static LevelingAlgorithmBase CreateSawtoothAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a SawtoothAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a SawtoothAlgorithm instance if the class exists
                var sawtoothAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm") as LevelingAlgorithmBase;

                if (sawtoothAlgorithm != null)
                {
                    // Set basic properties
                    sawtoothAlgorithm.algorithmName = "Sawtooth";
                    sawtoothAlgorithm.description = "Creates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.";
                    sawtoothAlgorithm.formulaExplanation = "Formula: Uses cyclical pattern with reset\n\nCreates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.";
                    sawtoothAlgorithm.difficultyRating = difficultyRating;

                    return sawtoothAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create SawtoothAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Sawtooth",
                "Creates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.",
                "Formula: Uses cyclical pattern with reset\n\nCreates a sawtooth pattern where difficulty increases linearly then drops, repeating in cycles.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.0f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = true;
            algorithm.zigzagAmplitude = 0.1f;
            algorithm.zigzagPeriod = 5f;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a tiered algorithm with distinct progression tiers
        /// </summary>
        public static LevelingAlgorithmBase CreateTieredAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a TieredAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a TieredAlgorithm instance if the class exists
                var tieredAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.TieredAlgorithm") as LevelingAlgorithmBase;

                if (tieredAlgorithm != null)
                {
                    // Set basic properties
                    tieredAlgorithm.algorithmName = "Tiered";
                    tieredAlgorithm.description = "Progression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.";
                    tieredAlgorithm.formulaExplanation = "Formula: Varies based on level tier\n\nProgression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.";
                    tieredAlgorithm.difficultyRating = difficultyRating;

                    return tieredAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create TieredAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Tiered",
                "Progression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.",
                "Formula: Varies based on level tier\n\nProgression with distinct tiers, where each tier has its own fixed multiplier. Creates a feeling of advancement through ranks.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.15f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a staircase algorithm with plateaus and significant jumps
        /// </summary>
        public static LevelingAlgorithmBase CreateStaircaseAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a StaircaseAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a StaircaseAlgorithm instance if the class exists
                var staircaseAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.StaircaseAlgorithm") as LevelingAlgorithmBase;

                if (staircaseAlgorithm != null)
                {
                    // Set basic properties
                    staircaseAlgorithm.algorithmName = "Staircase";
                    staircaseAlgorithm.description = "Creates a staircase pattern with plateaus of consistent difficulty followed by significant jumps, giving players time to adjust to each new difficulty tier.";
                    staircaseAlgorithm.formulaExplanation = "Formula: Uses step functions with increasing step heights\n\nCreates a progression with flat plateaus followed by significant jumps, like climbing a staircase with increasingly taller steps.";
                    staircaseAlgorithm.difficultyRating = difficultyRating;

                    return staircaseAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create StaircaseAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Staircase",
                "Creates a staircase pattern with plateaus of consistent difficulty followed by significant jumps, giving players time to adjust to each new difficulty tier.",
                "Formula: Uses step functions with increasing step heights\n\nCreates a progression with flat plateaus followed by significant jumps, like climbing a staircase with increasingly taller steps.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a spiral algorithm with gradually increasing oscillations
        /// </summary>
        public static LevelingAlgorithmBase CreateSpiralAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a SpiralAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a SpiralAlgorithm instance if the class exists
                var spiralAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.SpiralAlgorithm") as LevelingAlgorithmBase;

                if (spiralAlgorithm != null)
                {
                    // Set basic properties
                    spiralAlgorithm.algorithmName = "Spiral";
                    spiralAlgorithm.description = "Creates a spiral pattern with gradually increasing oscillations, like a spiral staircase that gets steeper as you climb.";
                    spiralAlgorithm.formulaExplanation = "Formula: Combines exponential growth with increasing oscillations\n\nCreates a pattern that spirals upward with increasing intensity, combining both overall growth and oscillating difficulty to create a dynamic progression.";
                    spiralAlgorithm.difficultyRating = difficultyRating;

                    return spiralAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create SpiralAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Spiral",
                "Creates a spiral pattern with gradually increasing oscillations, like a spiral staircase that gets steeper as you climb.",
                "Formula: Combines exponential growth with increasing oscillations\n\nCreates a pattern that spirals upward with increasing intensity, combining both overall growth and oscillating difficulty to create a dynamic progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 5f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a seasonal algorithm with alternating easy and hard periods
        /// </summary>
        public static LevelingAlgorithmBase CreateSeasonalAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a SeasonalAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a SeasonalAlgorithm instance if the class exists
                var seasonalAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.SeasonalAlgorithm") as LevelingAlgorithmBase;

                if (seasonalAlgorithm != null)
                {
                    // Set basic properties
                    seasonalAlgorithm.algorithmName = "Seasonal";
                    seasonalAlgorithm.description = "Cycles between easy and hard periods, creating a rhythm to progression with periods of relief.";
                    seasonalAlgorithm.formulaExplanation = "Formula: Alternates between easy and hard multipliers based on level\n\nCycles between easy and hard periods, creating a rhythm to progression with periods of relief.";
                    seasonalAlgorithm.difficultyRating = difficultyRating;

                    return seasonalAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create SeasonalAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Seasonal",
                "Cycles between easy and hard periods, creating a rhythm to progression with periods of relief.",
                "Formula: Alternates between easy and hard multipliers based on level\n\nCycles between easy and hard periods, creating a rhythm to progression with periods of relief.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 4f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a square root algorithm with moderate progression curve
        /// </summary>
        public static LevelingAlgorithmBase CreateRootAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a RootAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a RootAlgorithm instance if the class exists
                var rootAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.RootAlgorithm") as LevelingAlgorithmBase;

                if (rootAlgorithm != null)
                {
                    // Set basic properties
                    rootAlgorithm.algorithmName = "Square Root";
                    rootAlgorithm.description = "Uses square root scaling to create a moderate progression curve that slows down at higher levels.";
                    rootAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * Mathf.Sqrt(level * levelUpMultiplier)\n\nUses square root scaling to create a moderate progression curve that slows down at higher levels.";
                    rootAlgorithm.difficultyRating = difficultyRating;

                    return rootAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create RootAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Square Root",
                "Uses square root scaling to create a moderate progression curve that slows down at higher levels.",
                "Formula: requiredExp = requiredExp * Mathf.Sqrt(level * levelUpMultiplier)\n\nUses square root scaling to create a moderate progression curve that slows down at higher levels.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.0f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a ripple algorithm with multiple overlapping waves
        /// </summary>
        public static LevelingAlgorithmBase CreateRippleAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a RippleAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a RippleAlgorithm instance if the class exists
                var rippleAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.RippleAlgorithm") as LevelingAlgorithmBase;

                if (rippleAlgorithm != null)
                {
                    // Set basic properties
                    rippleAlgorithm.algorithmName = "Ripple";
                    rippleAlgorithm.description = "Creates a ripple effect with multiple overlapping waves of different frequencies, like dropping multiple stones in a pond.";
                    rippleAlgorithm.formulaExplanation = "Formula: Combines multiple sine waves with different frequencies\n\nCreates a complex pattern by overlaying multiple wave patterns with different frequencies and amplitudes, creating a ripple-like effect that varies throughout the progression.";
                    rippleAlgorithm.difficultyRating = difficultyRating;

                    return rippleAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create RippleAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Ripple",
                "Creates a ripple effect with multiple overlapping waves of different frequencies, like dropping multiple stones in a pond.",
                "Formula: Combines multiple sine waves with different frequencies\n\nCreates a complex pattern by overlaying multiple wave patterns with different frequencies and amplitudes, creating a ripple-like effect that varies throughout the progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 3f;
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.03f;
            algorithm.heartbeatPeriod = 7f;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a quantum algorithm with pseudo-random fluctuations
        /// </summary>
        public static LevelingAlgorithmBase CreateQuantumAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a QuantumAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a QuantumAlgorithm instance if the class exists
                var quantumAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.QuantumAlgorithm") as LevelingAlgorithmBase;

                if (quantumAlgorithm != null)
                {
                    // Set basic properties
                    quantumAlgorithm.algorithmName = "Quantum";
                    quantumAlgorithm.description = "Creates a quantum uncertainty pattern with random fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.";
                    quantumAlgorithm.formulaExplanation = "Formula: Combines deterministic growth with pseudo-random quantum fluctuations\n\nCreates a quantum uncertainty pattern with random-appearing fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.";
                    quantumAlgorithm.difficultyRating = difficultyRating;

                    return quantumAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create QuantumAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Quantum",
                "Creates a quantum uncertainty pattern with random fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.",
                "Formula: Combines deterministic growth with pseudo-random quantum fluctuations\n\nCreates a quantum uncertainty pattern with random-appearing fluctuations around a central trend, like particles in quantum mechanics that behave unpredictably yet follow probability laws.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 7f;
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.03f;
            algorithm.heartbeatPeriod = 11f;
            algorithm.useRandomFluctuations = true;
            algorithm.randomAmplitude = 0.02f;
            algorithm.useZigzag = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a quadratic algorithm with steep growth curve
        /// </summary>
        public static LevelingAlgorithmBase CreateQuadraticAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a QuadraticAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a QuadraticAlgorithm instance if the class exists
                var quadraticAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.QuadraticAlgorithm") as LevelingAlgorithmBase;

                if (quadraticAlgorithm != null)
                {
                    // Set basic properties
                    quadraticAlgorithm.algorithmName = "Quadratic";
                    quadraticAlgorithm.description = "Experience requirements grow with the square of the level, creating a steep curve.";
                    quadraticAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.005 * level^2))\n\nExperience requirements grow with the square of the level, creating a steep curve.";
                    quadraticAlgorithm.difficultyRating = difficultyRating;

                    return quadraticAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create QuadraticAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Quadratic",
                "Experience requirements grow with the square of the level, creating a steep curve.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.005 * level^2))\n\nExperience requirements grow with the square of the level, creating a steep curve.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a pulsating algorithm with gradually increasing intensity
        /// </summary>
        public static LevelingAlgorithmBase CreatePulsatingAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a PulsatingAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a PulsatingAlgorithm instance if the class exists
                var pulsatingAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.PulsatingAlgorithm") as LevelingAlgorithmBase;

                if (pulsatingAlgorithm != null)
                {
                    // Set basic properties
                    pulsatingAlgorithm.algorithmName = "Pulsating";
                    pulsatingAlgorithm.description = "Creates a pulsating pattern with gradually increasing intensity, like a drumbeat that gets stronger over time.";
                    pulsatingAlgorithm.formulaExplanation = "Formula: Combines sine waves with increasing amplitude\n\nCreates a pulsating pattern where the intensity of pulses increases as levels progress, creating a rhythm that becomes more pronounced over time.";
                    pulsatingAlgorithm.difficultyRating = difficultyRating;

                    return pulsatingAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create PulsatingAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Pulsating",
                "Creates a pulsating pattern with gradually increasing intensity, like a drumbeat that gets stronger over time.",
                "Formula: Combines sine waves with increasing amplitude\n\nCreates a pulsating pattern where the intensity of pulses increases as levels progress, creating a rhythm that becomes more pronounced over time.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 8f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a polynomial algorithm with growth between quadratic and cubic
        /// </summary>
        public static LevelingAlgorithmBase CreatePolynomialAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a PolynomialAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a PolynomialAlgorithm instance if the class exists
                var polynomialAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.PolynomialAlgorithm") as LevelingAlgorithmBase;

                if (polynomialAlgorithm != null)
                {
                    // Set basic properties
                    polynomialAlgorithm.algorithmName = "Polynomial";
                    polynomialAlgorithm.description = "Uses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.";
                    polynomialAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.002 * level^2.5))\n\nUses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.";
                    polynomialAlgorithm.difficultyRating = difficultyRating;

                    return polynomialAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create PolynomialAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Polynomial",
                "Uses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.002 * level^2.5))\n\nUses a polynomial with degree 2.5, creating a curve between quadratic and cubic growth.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a plateau algorithm with steep climbs and extended flat periods
        /// </summary>
        public static LevelingAlgorithmBase CreatePlateauAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a PlateauAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a PlateauAlgorithm instance if the class exists
                var plateauAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.PlateauAlgorithm") as LevelingAlgorithmBase;

                if (plateauAlgorithm != null)
                {
                    // Set basic properties
                    plateauAlgorithm.algorithmName = "Plateau";
                    plateauAlgorithm.description = "Creates a pattern with steep climbs followed by extended plateaus, giving players time to enjoy their achievements before facing the next challenge.";
                    plateauAlgorithm.formulaExplanation = "Formula: Combines steep growth with extended flat periods\n\nCreates a progression with short periods of rapid growth followed by longer plateaus of consistent difficulty, like climbing a mountain with flat resting areas.";
                    plateauAlgorithm.difficultyRating = difficultyRating;

                    return plateauAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create PlateauAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Plateau",
                "Creates a pattern with steep climbs followed by extended plateaus, giving players time to enjoy their achievements before facing the next challenge.",
                "Formula: Combines steep growth with extended flat periods\n\nCreates a progression with short periods of rapid growth followed by longer plateaus of consistent difficulty, like climbing a mountain with flat resting areas.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = true;
            algorithm.zigzagAmplitude = 0.1f;
            algorithm.zigzagPeriod = 10f;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a milestone algorithm with big jumps at milestone levels
        /// </summary>
        public static LevelingAlgorithmBase CreateMilestoneAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a MilestoneAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a MilestoneAlgorithm instance if the class exists
                var milestoneAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.MilestoneAlgorithm") as LevelingAlgorithmBase;

                if (milestoneAlgorithm != null)
                {
                    // Set basic properties
                    milestoneAlgorithm.algorithmName = "Milestone";
                    milestoneAlgorithm.description = "Big jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.";
                    milestoneAlgorithm.formulaExplanation = "Formula: Uses higher multiplier at milestone levels\n\nBig jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.";
                    milestoneAlgorithm.difficultyRating = difficultyRating;

                    return milestoneAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create MilestoneAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Milestone",
                "Big jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.",
                "Formula: Uses higher multiplier at milestone levels\n\nBig jumps at milestone levels (e.g., every 5 levels) with smaller increases between milestones.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = true;
            algorithm.zigzagAmplitude = 0.15f;
            algorithm.zigzagPeriod = 5f;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a micro progression algorithm with tiny increments in difficulty
        /// </summary>
        public static LevelingAlgorithmBase CreateMicroProgressionAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a MicroProgressionAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a MicroProgressionAlgorithm instance if the class exists
                var microProgressionAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.MicroProgressionAlgorithm") as LevelingAlgorithmBase;

                if (microProgressionAlgorithm != null)
                {
                    // Set basic properties
                    microProgressionAlgorithm.algorithmName = "Micro Progression";
                    microProgressionAlgorithm.description = "Tiny increments in difficulty using logarithmic scaling to keep increases very small.";
                    microProgressionAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * log10(level+10)/log10(level+9) + min(1, requiredExp*0.005)\n\nTiny increments in difficulty using logarithmic scaling to keep increases very small.";
                    microProgressionAlgorithm.difficultyRating = difficultyRating;

                    return microProgressionAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create MicroProgressionAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Micro Progression",
                "Tiny increments in difficulty using logarithmic scaling to keep increases very small.",
                "Formula: requiredExp = requiredExp * log10(level+10)/log10(level+9) + min(1, requiredExp*0.005)\n\nTiny increments in difficulty using logarithmic scaling to keep increases very small.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.01f;
            algorithm.levelMultiplierFactor = 0.001f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a logistic algorithm with S-curve growth pattern
        /// </summary>
        public static LevelingAlgorithmBase CreateLogisticAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a LogisticAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a LogisticAlgorithm instance if the class exists
                var logisticAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.LogisticAlgorithm") as LevelingAlgorithmBase;

                if (logisticAlgorithm != null)
                {
                    // Set basic properties
                    logisticAlgorithm.algorithmName = "Logistic";
                    logisticAlgorithm.description = "Creates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.";
                    logisticAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(1+e^(-(level-maxLevel/2)/(maxLevel/10)))))\n\nCreates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.";
                    logisticAlgorithm.difficultyRating = difficultyRating;

                    return logisticAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create LogisticAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Logistic",
                "Creates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(1+e^(-(level-maxLevel/2)/(maxLevel/10)))))\n\nCreates an S-curve where growth is slow at first, then accelerates in the middle, then slows down again near max level.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a logarithmic algorithm with decreasing growth rate
        /// </summary>
        public static LevelingAlgorithmBase CreateLogarithmicAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a LogarithmicAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a LogarithmicAlgorithm instance if the class exists
                var logarithmicAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.LogarithmicAlgorithm") as LevelingAlgorithmBase;

                if (logarithmicAlgorithm != null)
                {
                    // Set basic properties
                    logarithmicAlgorithm.algorithmName = "Logarithmic";
                    logarithmicAlgorithm.description = "Experience requirements grow more slowly at higher levels, making progression easier over time.";
                    logarithmicAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - log10(level)/log10(maxLevel) * 0.2))\n\nExperience requirements grow more slowly at higher levels, making progression easier over time.";
                    logarithmicAlgorithm.difficultyRating = difficultyRating;

                    return logarithmicAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create LogarithmicAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Logarithmic",
                "Experience requirements grow more slowly at higher levels, making progression easier over time.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - log10(level)/log10(maxLevel) * 0.2))\n\nExperience requirements grow more slowly at higher levels, making progression easier over time.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = -0.01f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an inverse logarithmic algorithm with increasing growth rate
        /// </summary>
        public static LevelingAlgorithmBase CreateInverseLogarithmicAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an InverseLogarithmicAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an InverseLogarithmicAlgorithm instance if the class exists
                var inverseLogarithmicAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.InverseLogarithmicAlgorithm") as LevelingAlgorithmBase;

                if (inverseLogarithmicAlgorithm != null)
                {
                    // Set basic properties
                    inverseLogarithmicAlgorithm.algorithmName = "Inverse Logarithmic";
                    inverseLogarithmicAlgorithm.description = "Inverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.";
                    inverseLogarithmicAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + log10(level+1)/log10(maxLevel+1) * 0.5))\n\nInverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.";
                    inverseLogarithmicAlgorithm.difficultyRating = difficultyRating;

                    return inverseLogarithmicAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create InverseLogarithmicAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Inverse Logarithmic",
                "Inverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + log10(level+1)/log10(maxLevel+1) * 0.5))\n\nInverse of logarithmic growth - starts slow and accelerates over time, creating a challenging late-game progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.02f; // Positive to indicate increasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a hyperbolic algorithm with gradually decreasing growth rate
        /// </summary>
        public static LevelingAlgorithmBase CreateHyperbolicAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a HyperbolicAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a HyperbolicAlgorithm instance if the class exists
                var hyperbolicAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.HyperbolicAlgorithm") as LevelingAlgorithmBase;

                if (hyperbolicAlgorithm != null)
                {
                    // Set basic properties
                    hyperbolicAlgorithm.algorithmName = "Hyperbolic";
                    hyperbolicAlgorithm.description = "Uses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.";
                    hyperbolicAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - 1/(level+1)))\n\nUses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.";
                    hyperbolicAlgorithm.difficultyRating = difficultyRating;

                    return hyperbolicAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create HyperbolicAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Hyperbolic",
                "Uses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - 1/(level+1)))\n\nUses a hyperbolic function that grows more slowly at higher levels, creating a more manageable progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = -0.005f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a harmonic algorithm with gradually decreasing growth rate
        /// </summary>
        public static LevelingAlgorithmBase CreateHarmonicAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a HarmonicAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a HarmonicAlgorithm instance if the class exists
                var harmonicAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.HarmonicAlgorithm") as LevelingAlgorithmBase;

                if (harmonicAlgorithm != null)
                {
                    // Set basic properties
                    harmonicAlgorithm.algorithmName = "Harmonic";
                    harmonicAlgorithm.description = "Uses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.";
                    harmonicAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(level+1)))\n\nUses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.";
                    harmonicAlgorithm.difficultyRating = difficultyRating;

                    return harmonicAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create HarmonicAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Harmonic",
                "Uses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 1/(level+1)))\n\nUses the harmonic series for a gradually decreasing growth rate, creating a balanced progression curve.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = -0.01f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a geometric algorithm with exponential-like growth
        /// </summary>
        public static LevelingAlgorithmBase CreateGeometricAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a GeometricAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a GeometricAlgorithm instance if the class exists
                var geometricAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.GeometricAlgorithm") as LevelingAlgorithmBase;

                if (geometricAlgorithm != null)
                {
                    // Set basic properties
                    geometricAlgorithm.algorithmName = "Geometric";
                    geometricAlgorithm.description = "Similar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.";
                    geometricAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier^(level/10))\n\nSimilar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.";
                    geometricAlgorithm.difficultyRating = difficultyRating;

                    return geometricAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create GeometricAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Geometric",
                "Similar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier^(level/10))\n\nSimilar to exponential, creates a geometric progression where each level is a fixed multiple of the previous.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.02f; // Positive to indicate increasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a fractional algorithm with unique curve between linear and exponential growth
        /// </summary>
        public static LevelingAlgorithmBase CreateFractionalAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a FractionalAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a FractionalAlgorithm instance if the class exists
                var fractionalAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.FractionalAlgorithm") as LevelingAlgorithmBase;

                if (fractionalAlgorithm != null)
                {
                    // Set basic properties
                    fractionalAlgorithm.algorithmName = "Fractional";
                    fractionalAlgorithm.description = "Uses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.";
                    fractionalAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (1.5^(1.3/level))\n\nUses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.";
                    fractionalAlgorithm.difficultyRating = difficultyRating;

                    return fractionalAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create FractionalAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Fractional",
                "Uses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.",
                "Formula: requiredExp = requiredExp * (1.5^(1.3/level))\n\nUses fractional powers for a unique curve between linear and exponential growth. Creates a mathematically interesting progression pattern that starts steeper and gradually flattens.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Fibonacci algorithm with golden ratio progression
        /// </summary>
        public static LevelingAlgorithmBase CreateFibonacciAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a FibonacciAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a FibonacciAlgorithm instance if the class exists
                var fibonacciAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.FibonacciAlgorithm") as LevelingAlgorithmBase;

                if (fibonacciAlgorithm != null)
                {
                    // Set basic properties
                    fibonacciAlgorithm.algorithmName = "Fibonacci";
                    fibonacciAlgorithm.description = "Uses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.";
                    fibonacciAlgorithm.formulaExplanation = "Formula: Uses Fibonacci sequence for multiplier\n\nUses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.";
                    fibonacciAlgorithm.difficultyRating = difficultyRating;

                    return fibonacciAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create FibonacciAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Fibonacci",
                "Uses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.",
                "Formula: Uses Fibonacci sequence for multiplier\n\nUses the Fibonacci sequence to determine experience requirements, creating an interesting progression pattern.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Wave algorithm with increasing amplitude and frequency
        /// </summary>
        public static LevelingAlgorithmBase CreateWaveAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a WaveAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a WaveAlgorithm instance if the class exists
                var waveAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.WaveAlgorithm") as LevelingAlgorithmBase;

                if (waveAlgorithm != null)
                {
                    // Set basic properties
                    waveAlgorithm.algorithmName = "Wave";
                    waveAlgorithm.description = "Creates a smooth wave pattern with gradually increasing amplitude, like ocean waves that grow larger over time.";
                    waveAlgorithm.formulaExplanation = "Formula: Uses sine waves with increasing amplitude and frequency\n\nCreates a wave pattern where both the height and frequency of waves increase as levels progress, creating a more dynamic and challenging progression.";
                    waveAlgorithm.difficultyRating = difficultyRating;

                    return waveAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create WaveAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Wave",
                "Creates a smooth wave pattern with gradually increasing amplitude, like ocean waves that grow larger over time.",
                "Formula: Uses sine waves with increasing amplitude and frequency\n\nCreates a wave pattern where both the height and frequency of waves increase as levels progress, creating a more dynamic and challenging progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.05f;
            algorithm.sinePeriod = 5f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an Adaptive algorithm that adjusts based on progression
        /// </summary>
        public static LevelingAlgorithmBase CreateAdaptiveAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an AdaptiveAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an AdaptiveAlgorithm instance if the class exists
                var adaptiveAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm") as LevelingAlgorithmBase;

                if (adaptiveAlgorithm != null)
                {
                    // Set basic properties
                    adaptiveAlgorithm.algorithmName = "Adaptive";
                    adaptiveAlgorithm.description = "Adjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.";
                    adaptiveAlgorithm.formulaExplanation = "Formula: Varies based on progress through total levels\n\nAdjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.";
                    adaptiveAlgorithm.difficultyRating = difficultyRating;

                    return adaptiveAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create AdaptiveAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Adaptive",
                "Adjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.",
                "Formula: Varies based on progress through total levels\n\nAdjusts difficulty based on current level vs max level, creating a custom curve for each max level setting.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Diminishing Returns algorithm that makes high levels more achievable
        /// </summary>
        public static LevelingAlgorithmBase CreateDiminishingReturnsAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a DiminishingReturnsAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a DiminishingReturnsAlgorithm instance if the class exists
                var diminishingReturnsAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.DiminishingReturnsAlgorithm") as LevelingAlgorithmBase;

                if (diminishingReturnsAlgorithm != null)
                {
                    // Set basic properties
                    diminishingReturnsAlgorithm.algorithmName = "Diminishing Returns";
                    diminishingReturnsAlgorithm.description = "As levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.";
                    diminishingReturnsAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - level/(level+10)))\n\nAs levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.";
                    diminishingReturnsAlgorithm.difficultyRating = difficultyRating;

                    return diminishingReturnsAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create DiminishingReturnsAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Diminishing Returns",
                "As levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.",
                "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 - level/(level+10)))\n\nAs levels increase, the percentage increase in required experience gradually diminishes, making high levels more achievable.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = -0.01f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an Early Boost algorithm that makes early levels easier
        /// </summary>
        public static LevelingAlgorithmBase CreateEarlyBoostAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an EarlyBoostAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an EarlyBoostAlgorithm instance if the class exists
                var earlyBoostAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.EarlyBoostAlgorithm") as LevelingAlgorithmBase;

                if (earlyBoostAlgorithm != null)
                {
                    // Set basic properties
                    earlyBoostAlgorithm.algorithmName = "Early Boost";
                    earlyBoostAlgorithm.description = "Makes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.";
                    earlyBoostAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (1 + 0.05 * (1 - (level/maxLevel)))\n\nMakes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.";
                    earlyBoostAlgorithm.difficultyRating = difficultyRating;

                    return earlyBoostAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create EarlyBoostAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Early Boost",
                "Makes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.",
                "Formula: requiredExp = requiredExp * (1 + 0.05 * (1 - (level/maxLevel)))\n\nMakes early levels very easy to achieve with minimal increases, then gradually transitions to normal progression.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an Elastic algorithm with damped oscillation
        /// </summary>
        public static LevelingAlgorithmBase CreateElasticAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an ElasticAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an ElasticAlgorithm instance if the class exists
                var elasticAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.ElasticAlgorithm") as LevelingAlgorithmBase;

                if (elasticAlgorithm != null)
                {
                    // Set basic properties
                    elasticAlgorithm.algorithmName = "Elastic";
                    elasticAlgorithm.description = "Creates an elastic bouncing effect where progression speeds up then slows down like a rubber band stretching and contracting.";
                    elasticAlgorithm.formulaExplanation = "Formula: Uses damped oscillation with exponential decay\n\nCreates an elastic bouncing effect where progression speeds up then slows down, with the oscillations gradually diminishing over time, like a rubber band that stretches and contracts with decreasing intensity.";
                    elasticAlgorithm.difficultyRating = difficultyRating;

                    return elasticAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create ElasticAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Elastic",
                "Creates an elastic bouncing effect where progression speeds up then slows down like a rubber band stretching and contracting.",
                "Formula: Uses damped oscillation with exponential decay\n\nCreates an elastic bouncing effect where progression speeds up then slows down, with the oscillations gradually diminishing over time, like a rubber band that stretches and contracts with decreasing intensity.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.15f;
            algorithm.sinePeriod = 8f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Decremental algorithm that gets easier as you level up
        /// </summary>
        public static LevelingAlgorithmBase CreateDecrementalAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a DecrementalAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a DecrementalAlgorithm instance if the class exists
                var decrementalAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.DecrementalAlgorithm") as LevelingAlgorithmBase;

                if (decrementalAlgorithm != null)
                {
                    // Set basic properties
                    decrementalAlgorithm.algorithmName = "Decremental";
                    decrementalAlgorithm.description = "Gets easier as you level up, with the multiplier decreasing as you progress through the game.";
                    decrementalAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (1 + (levelUpMultiplier-1) * (1-(level/maxLevel)*0.2))\n\nGets easier as you level up, with the multiplier decreasing as you progress through the game.";
                    decrementalAlgorithm.difficultyRating = difficultyRating;

                    return decrementalAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create DecrementalAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Decremental",
                "Gets easier as you level up, with the multiplier decreasing as you progress through the game.",
                "Formula: requiredExp = requiredExp * (1 + (levelUpMultiplier-1) * (1-(level/maxLevel)*0.2))\n\nGets easier as you level up, with the multiplier decreasing as you progress through the game.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = -0.01f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Decelerating algorithm that starts fast and slows down
        /// </summary>
        public static LevelingAlgorithmBase CreateDeceleratingAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a DeceleratingAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a DeceleratingAlgorithm instance if the class exists
                var deceleratingAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.DeceleratingAlgorithm") as LevelingAlgorithmBase;

                if (deceleratingAlgorithm != null)
                {
                    // Set basic properties
                    deceleratingAlgorithm.algorithmName = "Decelerating";
                    deceleratingAlgorithm.description = "Starts fast, then slows down. Creates a steep early curve that gradually flattens out.";
                    deceleratingAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * max(minMultiplier, initialMultiplier - decelerationFactor*level)\n\nStarts fast, then slows down. Creates a steep early curve that gradually flattens out.";
                    deceleratingAlgorithm.difficultyRating = difficultyRating;

                    return deceleratingAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create DeceleratingAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Decelerating",
                "Starts fast, then slows down. Creates a steep early curve that gradually flattens out.",
                "Formula: requiredExp = requiredExp * max(minMultiplier, initialMultiplier - decelerationFactor*level)\n\nStarts fast, then slows down. Creates a steep early curve that gradually flattens out.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.2f;
            algorithm.levelMultiplierFactor = -0.005f; // Negative to indicate decreasing multiplier
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Cyclical algorithm with repeating patterns of difficulty
        /// </summary>
        public static LevelingAlgorithmBase CreateCyclicalAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a CyclicalAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a CyclicalAlgorithm instance if the class exists
                var cyclicalAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.CyclicalAlgorithm") as LevelingAlgorithmBase;

                if (cyclicalAlgorithm != null)
                {
                    // Set basic properties
                    cyclicalAlgorithm.algorithmName = "Cyclical";
                    cyclicalAlgorithm.description = "Creates a repeating pattern of difficulty that rises and falls in cycles.";
                    cyclicalAlgorithm.formulaExplanation = "Formula: Uses multiple sine waves to create a complex cyclical pattern\n\nCreates a repeating pattern where difficulty increases and decreases in cycles, providing periods of challenge followed by relief.";
                    cyclicalAlgorithm.difficultyRating = difficultyRating;

                    return cyclicalAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create CyclicalAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Cyclical",
                "Creates a repeating pattern of difficulty that rises and falls in cycles.",
                "Formula: Uses multiple sine waves to create a complex cyclical pattern\n\nCreates a repeating pattern where difficulty increases and decreases in cycles, providing periods of challenge followed by relief.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.3f;
            algorithm.sinePeriod = 6f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Crescendo algorithm that builds dramatically toward the end
        /// </summary>
        public static LevelingAlgorithmBase CreateCrescendoAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a CrescendoAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a CrescendoAlgorithm instance if the class exists
                var crescendoAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.CrescendoAlgorithm") as LevelingAlgorithmBase;

                if (crescendoAlgorithm != null)
                {
                    // Set basic properties
                    crescendoAlgorithm.algorithmName = "Crescendo";
                    crescendoAlgorithm.description = "Creates a musical crescendo effect where difficulty builds gradually then dramatically increases toward the end, like the climax of a symphony.";
                    crescendoAlgorithm.formulaExplanation = "Formula: Uses exponential growth with accelerating rate\n\nCreates a musical crescendo effect where difficulty builds gradually at first, then accelerates more dramatically toward the end, like the climax of a symphony building to its finale.";
                    crescendoAlgorithm.difficultyRating = difficultyRating;

                    return crescendoAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create CrescendoAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Crescendo",
                "Creates a musical crescendo effect where difficulty builds gradually then dramatically increases toward the end, like the climax of a symphony.",
                "Formula: Uses exponential growth with accelerating rate\n\nCreates a musical crescendo effect where difficulty builds gradually at first, then accelerates more dramatically toward the end, like the climax of a symphony building to its finale.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0.02f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.02f;
            algorithm.sinePeriod = 20f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Cosine algorithm with a smooth wave pattern
        /// </summary>
        public static LevelingAlgorithmBase CreateCosineAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a CosineAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a CosineAlgorithm instance if the class exists
                var cosineAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.CosineAlgorithm") as LevelingAlgorithmBase;

                if (cosineAlgorithm != null)
                {
                    // Set basic properties
                    cosineAlgorithm.algorithmName = "Cosine";
                    cosineAlgorithm.description = "Creates a smooth wave pattern that starts high, dips in the middle, and rises again, creating a balanced progression with alternating easy and challenging phases.";
                    cosineAlgorithm.formulaExplanation = "Formula: Uses cosine function to create a smooth wave pattern\n\nCreates a progression that starts with higher multipliers, decreases in the middle, and increases again toward the end, following a cosine curve.";
                    cosineAlgorithm.difficultyRating = difficultyRating;

                    return cosineAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create CosineAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Cosine",
                "Creates a smooth wave pattern that starts high, dips in the middle, and rises again, creating a balanced progression with alternating easy and challenging phases.",
                "Formula: Uses cosine function to create a smooth wave pattern\n\nCreates a progression that starts with higher multipliers, decreases in the middle, and increases again toward the end, following a cosine curve.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.05f;
            algorithm.levelMultiplierFactor = 0f;
            algorithm.useSineWave = true;
            algorithm.sineAmplitude = 0.04f;
            algorithm.sinePeriod = 1f;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates an Asymptotic algorithm that approaches a limit
        /// </summary>
        public static LevelingAlgorithmBase CreateAsymptoticAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create an AsymptoticAlgorithm ScriptableObject if the class exists
            try
            {
                // Create an AsymptoticAlgorithm instance if the class exists
                var asymptoticAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.AsymptoticAlgorithm") as LevelingAlgorithmBase;

                if (asymptoticAlgorithm != null)
                {
                    // Set basic properties
                    asymptoticAlgorithm.algorithmName = "Asymptotic";
                    asymptoticAlgorithm.description = "Approaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.";
                    asymptoticAlgorithm.formulaExplanation = "Formula: requiredExp = requiredExp * (maxMultiplier * (1 - e^(-rate*level)))\n\nApproaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.";
                    asymptoticAlgorithm.difficultyRating = difficultyRating;

                    return asymptoticAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create AsymptoticAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Asymptotic",
                "Approaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.",
                "Formula: requiredExp = requiredExp * (maxMultiplier * (1 - e^(-rate*level)))\n\nApproaches a limit as levels increase, creating a ceiling on difficulty that prevents excessive grinding.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = false;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

        /// <summary>
        /// Creates a Burst algorithm with sudden spikes of difficulty
        /// </summary>
        public static LevelingAlgorithmBase CreateBurstAlgorithm(DifficultyRating difficultyRating = null)
        {
            // Try to create a BurstAlgorithm ScriptableObject if the class exists
            try
            {
                // Create a BurstAlgorithm instance if the class exists
                var burstAlgorithm = ScriptableObject.CreateInstance("RealSoftGames.AdvancedLevelingSystem.BurstAlgorithm") as LevelingAlgorithmBase;

                if (burstAlgorithm != null)
                {
                    // Set basic properties
                    burstAlgorithm.algorithmName = "Burst";
                    burstAlgorithm.description = "Creates sudden bursts of high difficulty followed by periods of easier progression, like a series of explosions with calm periods in between.";
                    burstAlgorithm.formulaExplanation = "Formula: Uses exponential bursts at regular intervals\n\nCreates a pattern with sudden spikes of high difficulty followed by periods of more gradual progression, with bursts becoming more intense as levels increase.";
                    burstAlgorithm.difficultyRating = difficultyRating;

                    return burstAlgorithm;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"Could not create BurstAlgorithm instance: {ex.Message}. Falling back to generic implementation.");
            }

            // Fallback to generic implementation
            var algorithm = CreateCodedFormulaAlgorithm(
                "Burst",
                "Creates sudden bursts of high difficulty followed by periods of easier progression, like a series of explosions with calm periods in between.",
                "Formula: Uses exponential bursts at regular intervals\n\nCreates a pattern with sudden spikes of high difficulty followed by periods of more gradual progression, with bursts becoming more intense as levels increase.",
                difficultyRating);

            // Set formula properties
            algorithm.baseMultiplier = 1.1f;
            algorithm.levelMultiplierFactor = 0.01f;
            algorithm.useSineWave = false;
            algorithm.useHeartbeat = true;
            algorithm.heartbeatAmplitude = 0.5f;
            algorithm.heartbeatPeriod = 7f;
            algorithm.useZigzag = false;
            algorithm.useRandomFluctuations = false;

            return algorithm;
        }

#if UNITY_EDITOR
        /// <summary>
        /// Saves an algorithm to disk and adds it to the database
        /// </summary>
        public static void SaveAlgorithm(LevelingAlgorithmBase algorithm, string path = null)
        {
            if (algorithm == null)
                return;

            // Generate a path if none provided
            if (string.IsNullOrEmpty(path))
            {
                string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
                string fileName = algorithm.algorithmName.Replace(" ", "") + ".asset";
                path = System.IO.Path.Combine(directory, fileName);
            }

            // Create the directory if it doesn't exist
            string directoryPath = System.IO.Path.GetDirectoryName(path);
            if (!System.IO.Directory.Exists(directoryPath))
            {
                System.IO.Directory.CreateDirectory(directoryPath);
            }

            // Save the asset
            AssetDatabase.CreateAsset(algorithm, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Add to database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database != null)
            {
                database.AddAlgorithm(algorithm);
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();
            }

            Debug.Log($"Saved algorithm '{algorithm.algorithmName}' to {path}");
        }
#endif
    }
}
