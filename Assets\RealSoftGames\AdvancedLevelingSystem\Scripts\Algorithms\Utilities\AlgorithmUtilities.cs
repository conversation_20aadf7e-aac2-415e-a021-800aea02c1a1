using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Utility methods for working with leveling algorithms
    /// </summary>
    public static class AlgorithmUtilities
    {
        /// <summary>
        /// Calculates the experience requirements for a range of levels using the specified algorithm
        /// </summary>
        /// <param name="algorithm">The algorithm to use</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>A list of experience requirements for each level</returns>
        public static List<int> CalculateExperienceRequirements(
            LevelingAlgorithmBase algorithm,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm == null)
            {
                Debug.LogError("AlgorithmUtilities: Algorithm is null");
                return new List<int>();
            }

            List<int> requirements = new List<int>();
            int currentExperience = startingExperience;
            int currentLevel = startingLevel;

            // Calculate the experience requirements for each level
            while (currentLevel < maxLevel)
            {
                // Calculate the next requirement
                int nextRequirement = algorithm.CalculateNextRequirement(
                    currentExperience,
                    currentLevel,
                    levelUpMultiplier,
                    startingLevel,
                    maxLevel);

                // Add to the list
                requirements.Add(nextRequirement);

                // Update for the next level
                currentExperience = nextRequirement;
                currentLevel++;
            }

            return requirements;
        }

        /// <summary>
        /// Calculates the total experience required to reach a specific level
        /// </summary>
        /// <param name="algorithm">The algorithm to use</param>
        /// <param name="targetLevel">The target level</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>The total experience required to reach the target level</returns>
        public static int CalculateTotalExperienceToLevel(
            LevelingAlgorithmBase algorithm,
            int targetLevel,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm == null)
            {
                Debug.LogError("AlgorithmUtilities: Algorithm is null");
                return 0;
            }

            if (targetLevel <= startingLevel)
            {
                return 0;
            }

            if (targetLevel > maxLevel)
            {
                targetLevel = maxLevel;
            }

            int totalExperience = 0;
            int currentExperience = startingExperience;
            int currentLevel = startingLevel;

            // Calculate the experience requirements for each level up to the target level
            while (currentLevel < targetLevel)
            {
                // Calculate the next requirement
                int nextRequirement = algorithm.CalculateNextRequirement(
                    currentExperience,
                    currentLevel,
                    levelUpMultiplier,
                    startingLevel,
                    maxLevel);

                // Add to the total
                totalExperience += nextRequirement;

                // Update for the next level
                currentExperience = nextRequirement;
                currentLevel++;
            }

            return totalExperience;
        }

        /// <summary>
        /// Estimates the level a player would reach with a given amount of experience
        /// </summary>
        /// <param name="algorithm">The algorithm to use</param>
        /// <param name="totalExperience">The total experience available</param>
        /// <param name="startingExperience">The starting experience requirement</param>
        /// <param name="startingLevel">The starting level</param>
        /// <param name="maxLevel">The maximum level</param>
        /// <param name="levelUpMultiplier">The level up multiplier</param>
        /// <returns>The estimated level the player would reach</returns>
        public static int EstimateLevelFromExperience(
            LevelingAlgorithmBase algorithm,
            int totalExperience,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 50,
            float levelUpMultiplier = 1.1f)
        {
            if (algorithm == null)
            {
                Debug.LogError("AlgorithmUtilities: Algorithm is null");
                return startingLevel;
            }

            if (totalExperience <= 0)
            {
                return startingLevel;
            }

            int remainingExperience = totalExperience;
            int currentExperience = startingExperience;
            int currentLevel = startingLevel;

            // Calculate the level the player would reach
            while (currentLevel < maxLevel && remainingExperience > 0)
            {
                // Calculate the next requirement
                int nextRequirement = algorithm.CalculateNextRequirement(
                    currentExperience,
                    currentLevel,
                    levelUpMultiplier,
                    startingLevel,
                    maxLevel);

                // Check if we have enough experience to level up
                if (remainingExperience >= nextRequirement)
                {
                    // Level up
                    remainingExperience -= nextRequirement;
                    currentLevel++;
                    currentExperience = nextRequirement;
                }
                else
                {
                    // Not enough experience to level up
                    break;
                }
            }

            return currentLevel;
        }
    }
}
