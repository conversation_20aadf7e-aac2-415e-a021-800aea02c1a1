using UnityEngine;
using System;
using System.Collections.Generic;


namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Registry for ScriptableObject-based leveling algorithms
    /// </summary>
    public static class ScriptableAlgorithmRegistry
    {
        private static bool isInitialized = false;

        /// <summary>
        /// Gets whether the registry has been initialized
        /// </summary>
        public static bool IsInitialized => isInitialized;

        // Using lists instead of dictionaries for better serialization support
        private static readonly List<LevelingAlgorithmBase> allAlgorithms = new();
        private static readonly List<string> algorithmNames = new();
        private static readonly List<string> difficultyNames = new();

        // Lists of algorithms by category
        private static readonly List<LevelingAlgorithmBase> veryEasyAlgorithms = new();
        private static readonly List<LevelingAlgorithmBase> easyAlgorithms = new();
        private static readonly List<LevelingAlgorithmBase> mediumAlgorithms = new();
        private static readonly List<LevelingAlgorithmBase> hardAlgorithms = new();
        private static readonly List<LevelingAlgorithmBase> veryHardAlgorithms = new();
        private static readonly List<LevelingAlgorithmBase> customAlgorithms = new();

        // Flag to prevent recursive initialization
        private static bool isInitializing = false;

        /// <summary>
        /// Initializes the registry by loading all algorithms from the AlgorithmDatabase
        /// </summary>
        public static void Initialize()
        {
            Initialize(false);
        }

        /// <summary>
        /// Initializes the registry by loading all algorithms from the AlgorithmDatabase
        /// </summary>
        /// <param name="force">If true, forces reinitialization even if already initialized</param>
        public static void Initialize(bool force)
        {
            // Prevent recursive initialization
            if (isInitializing)
            {
                Debug.LogWarning("ScriptableAlgorithmRegistry: Already initializing, skipping recursive call");
                return;
            }

            // Skip initialization if already initialized and not forced
            if (isInitialized && !force)
            {
                // Debug.Log("ScriptableAlgorithmRegistry: Already initialized, skipping (use force=true to reinitialize)");
                return;
            }

            try
            {
                isInitializing = true;
                // Debug.Log("ScriptableAlgorithmRegistry: Beginning initialization");

                // Reset initialization state
                isInitialized = false;

                // Clear existing data
                allAlgorithms.Clear();
                algorithmNames.Clear();
                difficultyNames.Clear();
                veryEasyAlgorithms.Clear();
                easyAlgorithms.Clear();
                mediumAlgorithms.Clear();
                hardAlgorithms.Clear();
                veryHardAlgorithms.Clear();
                customAlgorithms.Clear();

                // Load algorithms from the AlgorithmDatabase
                AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");

                if (database == null)
                {
                    Debug.LogError("ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.");
                    return;
                }

                if (database.algorithms == null)
                {
                    Debug.LogError("ScriptableAlgorithmRegistry: AlgorithmDatabase.algorithms is null");
                    return;
                }

                if (database.algorithms.Count == 0)
                {
                    Debug.LogWarning("ScriptableAlgorithmRegistry: AlgorithmDatabase is empty. Please add algorithms to it.");
                    return;
                }

                // Debug.Log($"ScriptableAlgorithmRegistry: Found {database.algorithms.Count} algorithms in database");

                // Register each algorithm
                int validCount = 0;
                int nullCount = 0;
                foreach (var algorithm in database.algorithms)
                {
                    if (algorithm == null)
                    {
                        Debug.LogWarning("ScriptableAlgorithmRegistry: Null algorithm found in database");
                        nullCount++;
                        continue;
                    }

                    try
                    {
                        // Debug.Log($"ScriptableAlgorithmRegistry: Registering algorithm: {algorithm.name} ({algorithm.GetType().Name})");

                        // Register the algorithm
                        RegisterAlgorithm(algorithm);
                        validCount++;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"ScriptableAlgorithmRegistry: Error registering algorithm {algorithm.name}: {ex.Message}");
                    }
                }

                // Debug.Log($"ScriptableAlgorithmRegistry: Successfully registered {validCount} out of {database.algorithms.Count} algorithms. Null algorithms: {nullCount}");
                // Debug.Log($"ScriptableAlgorithmRegistry: Registered algorithm names: {string.Join(", ", algorithmNames)}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error initializing ScriptableAlgorithmRegistry: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                // Always mark as initialized to prevent further initialization attempts
                isInitialized = true;
                isInitializing = false;
                // Debug.Log("ScriptableAlgorithmRegistry: Initialization complete");
            }
        }

        /// <summary>
        /// Gets an algorithm by name
        /// </summary>
        public static LevelingAlgorithmBase GetAlgorithm(string name)
        {
            if (!isInitialized)
                Initialize();

            // Find the algorithm by name
            int index = algorithmNames.IndexOf(name);
            if (index >= 0 && index < allAlgorithms.Count)
            {
                return allAlgorithms[index];
            }

            // Fallback to first available algorithm if the requested algorithm isn't found
            Debug.LogWarning($"Algorithm '{name}' not found. Using first available algorithm as fallback.");

            // Check if we have any algorithms at all
            if (allAlgorithms.Count > 0)
            {
                return allAlgorithms[0];
            }

            // If no algorithms are available, return null
            Debug.LogError("No algorithms available in the registry. Please create some algorithms first.");
            return null;
        }

        /// <summary>
        /// Gets an algorithm by difficulty rating name
        /// </summary>
        public static LevelingAlgorithmBase GetAlgorithmByDifficultyName(string difficultyName)
        {
            if (!isInitialized)
                Initialize();

            // Find the first algorithm with the matching difficulty rating
            foreach (var algorithm in allAlgorithms)
            {
                if (algorithm.DifficultyRating != null &&
                    algorithm.DifficultyRating.ratingName == difficultyName)
                {
                    return algorithm;
                }
            }

            // Fallback to first available algorithm if the requested algorithm isn't found
            Debug.LogWarning($"Algorithm for difficulty '{difficultyName}' not found. Using first available algorithm as fallback.");

            // Check if we have any algorithms at all
            if (allAlgorithms.Count > 0)
            {
                return allAlgorithms[0];
            }

            // If no algorithms are available, return null
            Debug.LogError("No algorithms available in the registry. Please create some algorithms first.");
            return null;
        }

        /// <summary>
        /// Gets all registered algorithms as LevelingAlgorithmBase
        /// </summary>
        public static IEnumerable<LevelingAlgorithmBase> GetAllAlgorithms()
        {
            if (!isInitialized)
                Initialize();

            return allAlgorithms;
        }

        /// <summary>
        /// Gets all registered algorithms as ILevelingAlgorithm
        /// </summary>
        public static IEnumerable<ILevelingAlgorithm> GetAllAlgorithmsAsInterface()
        {
            try
            {
                if (!isInitialized)
                    Initialize();

                // If we still don't have any algorithms, try to force load them from the database
                if (allAlgorithms.Count == 0)
                {
                    Debug.LogWarning("ScriptableAlgorithmRegistry: No algorithms found in registry, attempting to force load from database");
                    ForceLoadFromDatabase();
                }

                // Return a new list to avoid modification issues
                return new List<ILevelingAlgorithm>(allAlgorithms);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error in GetAllAlgorithmsAsInterface: {ex.Message}");
                // Return an empty list instead of null to prevent NullReferenceException
                return new List<ILevelingAlgorithm>();
            }
        }

        /// <summary>
        /// Forces loading algorithms directly from the database, bypassing normal initialization
        /// </summary>
        public static void ForceLoadFromDatabase()
        {
            try
            {
                // Debug.Log("ScriptableAlgorithmRegistry: Force loading algorithms from database");

                // Load algorithms from the AlgorithmDatabase
                AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");

                if (database == null)
                {
                    Debug.LogError("ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder during force load.");
                    return;
                }

                if (database.algorithms == null)
                {
                    Debug.LogError("ScriptableAlgorithmRegistry: AlgorithmDatabase.algorithms is null during force load");
                    return;
                }

                if (database.algorithms.Count == 0)
                {
                    Debug.LogWarning("ScriptableAlgorithmRegistry: AlgorithmDatabase is empty during force load.");
                    return;
                }

                // Debug.Log($"ScriptableAlgorithmRegistry: Found {database.algorithms.Count} algorithms in database during force load");

                // Clear existing data to prevent duplicates
                allAlgorithms.Clear();
                algorithmNames.Clear();
                difficultyNames.Clear();
                veryEasyAlgorithms.Clear();
                easyAlgorithms.Clear();
                mediumAlgorithms.Clear();
                hardAlgorithms.Clear();
                veryHardAlgorithms.Clear();
                customAlgorithms.Clear();

                // Register each algorithm directly
                int validCount = 0;
                int nullCount = 0;
                foreach (var algorithm in database.algorithms)
                {
                    if (algorithm == null)
                    {
                        Debug.LogWarning("ScriptableAlgorithmRegistry: Null algorithm found in database during force load");
                        nullCount++;
                        continue;
                    }

                    try
                    {
                        // Add directly to the registry lists
                        string name = algorithm.Name;
                        if (string.IsNullOrEmpty(name))
                        {
                            name = "Unnamed Algorithm";
                        }

                        // Ensure unique names
                        if (algorithmNames.Contains(name))
                        {
                            int counter = 1;
                            string baseName = name;
                            while (algorithmNames.Contains(name))
                            {
                                name = $"{baseName} ({counter})";
                                counter++;
                            }
                        }

                        // Add to main lists
                        allAlgorithms.Add(algorithm);
                        algorithmNames.Add(name);

                        // Register by difficulty rating name
                        if (algorithm.DifficultyRating != null)
                        {
                            string difficultyName = algorithm.DifficultyRating.ratingName;
                            if (!difficultyNames.Contains(difficultyName))
                            {
                                difficultyNames.Add(difficultyName);
                            }
                        }

                        // Categorize based on name keywords
                        string nameLower = algorithm.Name.ToLower();
                        if (nameLower.Contains("easy") || nameLower.Contains("flat") || nameLower.Contains("simple"))
                        {
                            veryEasyAlgorithms.Add(algorithm);
                        }
                        else if (nameLower.Contains("linear") || nameLower.Contains("boost") || nameLower.Contains("decremental") ||
                                 nameLower.Contains("micro") || nameLower.Contains("elastic"))
                        {
                            easyAlgorithms.Add(algorithm);
                        }
                        else if (nameLower.Contains("moderate") || nameLower.Contains("balanced") || nameLower.Contains("heartbeat") ||
                                 nameLower.Contains("sine") || nameLower.Contains("sinusoidal") || nameLower.Contains("wave"))
                        {
                            mediumAlgorithms.Add(algorithm);
                        }
                        else if (nameLower.Contains("challenging") || nameLower.Contains("exponential") || nameLower.Contains("logarithmic") ||
                                 nameLower.Contains("sawtooth"))
                        {
                            hardAlgorithms.Add(algorithm);
                        }
                        else if (nameLower.Contains("hardcore") || nameLower.Contains("brutal") || nameLower.Contains("impossible") ||
                                 nameLower.Contains("extreme"))
                        {
                            veryHardAlgorithms.Add(algorithm);
                        }
                        else
                        {
                            customAlgorithms.Add(algorithm);
                        }

                        validCount++;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"ScriptableAlgorithmRegistry: Error registering algorithm {algorithm.name} during force load: {ex.Message}");
                    }
                }

                // Debug.Log($"ScriptableAlgorithmRegistry: Successfully force loaded {validCount} out of {database.algorithms.Count} algorithms. Null algorithms: {nullCount}");

                // Mark as initialized
                isInitialized = true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error in ForceLoadFromDatabase: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Gets all algorithm names for dropdown selection
        /// </summary>
        public static string[] GetAllAlgorithmNames()
        {
            if (!isInitialized)
                Initialize();

            return algorithmNames.ToArray();
        }

        /// <summary>
        /// Gets the number of algorithms in the registry
        /// </summary>
        public static int GetAlgorithmCount()
        {
            if (!isInitialized)
                Initialize();

            return allAlgorithms.Count;
        }

        /// <summary>
        /// Gets all algorithms grouped by difficulty category
        /// </summary>
        public static Dictionary<string, List<LevelingAlgorithmBase>> GetAlgorithmsByCategory()
        {
            if (!isInitialized)
                Initialize();

            // Create a dictionary to return
            var result = new Dictionary<string, List<LevelingAlgorithmBase>>
            {
                ["Very Easy"] = veryEasyAlgorithms,
                ["Easy"] = easyAlgorithms,
                ["Medium"] = mediumAlgorithms,
                ["Hard"] = hardAlgorithms,
                ["Very Hard"] = veryHardAlgorithms,
                ["Custom"] = customAlgorithms
            };

            return result;
        }

        // Flag to prevent recursive registration
        private static bool isRegistering = false;

        /// <summary>
        /// Registers a new algorithm
        /// </summary>
        public static void RegisterAlgorithm(LevelingAlgorithmBase algorithm)
        {
            // Prevent recursive registration
            if (isRegistering)
            {
                Debug.LogWarning("ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call");
                return;
            }

            try
            {
                isRegistering = true;

                if (algorithm == null)
                {
                    Debug.LogWarning("ScriptableAlgorithmRegistry: Attempted to register a null algorithm");
                    return;
                }

                if (!isInitialized && !isInitializing)
                {
                    // Debug.Log("ScriptableAlgorithmRegistry: Registry not initialized, initializing now");
                    Initialize();
                }

                // Skip if we're still initializing to prevent recursive calls
                if (isInitializing)
                {
                    Debug.LogWarning("ScriptableAlgorithmRegistry: Registry is currently initializing, skipping registration");
                    return;
                }

                // Check if algorithm is already registered
                if (allAlgorithms.Contains(algorithm))
                {
                    // Debug.Log($"ScriptableAlgorithmRegistry: Algorithm {algorithm.name} is already registered, skipping");
                    return;
                }

                // Debug.Log($"ScriptableAlgorithmRegistry: Registering algorithm: {algorithm.name} ({algorithm.GetType().Name})");

                try
                {
                    // Validate the algorithm
                    algorithm.Validate();

                    // Pre-calculate experience requirements and raw formula curves
                    // This ensures the algorithms have their points calculated before being displayed
                    try
                    {
                        // Use default values for pre-calculation
                        int startingExperience = 250;
                        int startingLevel = 1;
                        int maxLevel = 30;
                        float levelUpMultiplier = 1.1f;

                        // Pre-calculate points for the algorithm
                        algorithm.PreCalculatePoints(startingExperience, startingLevel, maxLevel, levelUpMultiplier);
                    }
                    catch (Exception ex)
                    {
                        // Continue with registration despite calculation failure
                        Debug.LogWarning($"ScriptableAlgorithmRegistry: Error pre-calculating curves for {algorithm.name}: {ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    // Continue with registration despite validation failure
                    Debug.LogWarning($"ScriptableAlgorithmRegistry: Error validating algorithm {algorithm.name}: {ex.Message}");
                }

                // Register by name
                string name = algorithm.Name;
                if (string.IsNullOrEmpty(name))
                {
                    name = "Unnamed Algorithm";
                    Debug.LogWarning($"ScriptableAlgorithmRegistry: Algorithm has no name, using '{name}' instead");
                }

                // Ensure unique names
                if (algorithmNames.Contains(name))
                {
                    int counter = 1;
                    string baseName = name;
                    while (algorithmNames.Contains(name))
                    {
                        name = $"{baseName} ({counter})";
                        counter++;
                    }
                    // Debug.Log($"ScriptableAlgorithmRegistry: Renamed duplicate algorithm to '{name}'");
                }

                // Add to main lists
                allAlgorithms.Add(algorithm);
                algorithmNames.Add(name);
                // Debug.Log($"ScriptableAlgorithmRegistry: Added algorithm '{name}' to registry");

                // Register by difficulty rating name
                if (algorithm.DifficultyRating != null)
                {
                    string difficultyName = algorithm.DifficultyRating.ratingName;
                    if (!difficultyNames.Contains(difficultyName))
                    {
                        difficultyNames.Add(difficultyName);
                        // Debug.Log($"ScriptableAlgorithmRegistry: Added difficulty rating '{difficultyName}' to registry");
                    }
                }
                else
                {
                    Debug.LogWarning($"ScriptableAlgorithmRegistry: Algorithm '{name}' has no difficulty rating");
                }

                // Use algorithm name to determine category
                string nameLower = algorithm.Name.ToLower();

                // Categorize based on name keywords
                if (nameLower.Contains("easy") || nameLower.Contains("flat") || nameLower.Contains("simple"))
                {
                    veryEasyAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Very Easy");
                }
                else if (nameLower.Contains("linear") || nameLower.Contains("boost") || nameLower.Contains("decremental") ||
                         nameLower.Contains("micro") || nameLower.Contains("elastic"))
                {
                    easyAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Easy");
                }
                else if (nameLower.Contains("moderate") || nameLower.Contains("balanced") || nameLower.Contains("heartbeat") ||
                         nameLower.Contains("sine") || nameLower.Contains("sinusoidal") || nameLower.Contains("wave"))
                {
                    mediumAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Medium");
                }
                else if (nameLower.Contains("challenging") || nameLower.Contains("exponential") || nameLower.Contains("logarithmic") ||
                         nameLower.Contains("sawtooth"))
                {
                    hardAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Hard");
                }
                else if (nameLower.Contains("hardcore") || nameLower.Contains("brutal") || nameLower.Contains("impossible") ||
                         nameLower.Contains("extreme"))
                {
                    veryHardAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Very Hard");
                }
                else
                {
                    customAlgorithms.Add(algorithm);
                    // Debug.Log($"ScriptableAlgorithmRegistry: Categorized '{name}' as Custom");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"ScriptableAlgorithmRegistry: Error registering algorithm: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                isRegistering = false;
            }
        }
    }
}
