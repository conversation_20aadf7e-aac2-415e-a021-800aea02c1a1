using RealSoftGames.AdvancedLevelingSystem;
using UnityEngine;
using UnityEngine.UI;
namespace RealSoftGames.AdvancedLevelingSystem
{
    public class SaverLoadBtnAssign : MonoBehaviour
    {
        [SerializeField] private Button saveBtn, loadBtn;

        private void Awake()
        {
            var levelingSystem = GetComponent<AdvancedLevelingSystem>();
            saveBtn.onClick.AddListener(() => levelingSystem.Save("LevelingData"));
            loadBtn.onClick.AddListener(() => levelingSystem.Load("LevelingData"));
        }
    }
}