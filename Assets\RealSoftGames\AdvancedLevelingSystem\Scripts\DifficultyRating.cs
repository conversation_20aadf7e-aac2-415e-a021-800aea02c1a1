using UnityEngine;
using RealSoftGames;

namespace RealSoftGames.AdvancedLevelingSystem
{
    [CreateAssetMenu(fileName = "DifficultyRating", menuName = "RealSoftGames/Leveling System/Difficulty Rating")]
    public class DifficultyRating : ScriptableObject
    {
        [Tooltip("Unique identifier for the difficulty rating")]
        public string uniqueID = System.Guid.NewGuid().ToString();      

        [Tooltip("The name of the difficulty rating (e.g., Very Easy, Easy, Medium, Hard, Very Hard)")]
        public string ratingName;

        [Tooltip("The number of stars to display (1-5)")]
        [Range(1, 5)]
        public int stars = 1;

        [Tooltip("The color associated with this difficulty rating")]
        public Color ratingColor = Color.white;

        [Tooltip("Description of this difficulty rating")]
        [TextArea(3, 6)]
        public string description;
    }
}
