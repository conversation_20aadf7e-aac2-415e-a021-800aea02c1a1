using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    [CreateAssetMenu(fileName = "DifficultyRatingDatabase", menuName = "RealSoftGames/Leveling System/Difficulty Rating Database")]
    public class DifficultyRatingDatabase : ScriptableObject
    {
        [Tooltip("List of all difficulty ratings in order from easiest to hardest")]
        public List<DifficultyRating> difficultyRatings = new List<DifficultyRating>();

        private void OnValidate()
        {
            // Ensure we have at least the basic difficulty ratings
            if (difficultyRatings.Count == 0)
            {
                InitializeDefaultRatings();
            }
        }

        private void InitializeDefaultRatings()
        {
            // Create default difficulty ratings if none exist
            CreateRating("Very Easy", new Color(0.0f, 0.8f, 0.0f), 1, "Extremely gentle progression suitable for casual games.");
            CreateRating("Easy", new Color(0.5f, 0.8f, 0.0f), 2, "Smooth progression with moderate challenges.");
            CreateRating("Medium", new Color(0.8f, 0.8f, 0.0f), 3, "Balanced progression with regular challenges.");
            CreateRating("Hard", new Color(0.8f, 0.4f, 0.0f), 4, "Steep progression requiring dedication and skill.");
            CreateRating("Very Hard", new Color(0.8f, 0.0f, 0.0f), 5, "Extremely challenging progression for dedicated players.");
            CreateRating("Extreme", new Color(0.5f, 0.0f, 0.5f), 5, "Brutal progression designed for the most hardcore players.");
        }

        private void CreateRating(string name, Color color, int stars, string desc)
        {
            DifficultyRating rating = CreateInstance<DifficultyRating>();
            rating.ratingName = name;
            rating.ratingColor = color;
            rating.stars = stars;
            rating.description = desc;
            difficultyRatings.Add(rating);
        }

        // Get a difficulty rating by name
        public DifficultyRating GetRatingByName(string name)
        {
            return difficultyRatings.Find(r => r.ratingName == name);
        }

        // Get a difficulty rating by index
        public DifficultyRating GetRatingByIndex(int index)
        {
            if (index >= 0 && index < difficultyRatings.Count)
            {
                return difficultyRatings[index];
            }
            return null;
        }

        // Get a difficulty rating by star count
        public DifficultyRating GetRatingByStars(int stars)
        {
            return difficultyRatings.Find(r => r.stars == stars);
        }

        // Add a difficulty rating to the database
        public void AddDifficultyRating(DifficultyRating rating)
        {
            if (rating == null)
                return;

            // Check if the rating already exists
            if (!difficultyRatings.Contains(rating))
            {
                difficultyRatings.Add(rating);
            }
        }
    }
}
