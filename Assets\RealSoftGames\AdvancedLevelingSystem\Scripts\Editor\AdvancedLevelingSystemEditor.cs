using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    [CustomEditor(typeof(AdvancedLevelingSystem))]
    public class AdvancedLevelingSystemEditor : UnityEditor.Editor
    {
        // Method to find or create an AdvancedLevelingSystem component
        public static void FindOrCreateComponent()
        {
            // Find any AdvancedLevelingSystem component in the scene
            AdvancedLevelingSystem system = GameObject.FindObjectOfType<AdvancedLevelingSystem>();

            if (system != null)
            {
                // Select the GameObject with the AdvancedLevelingSystem component
                Selection.activeGameObject = system.gameObject;
                // Focus the Inspector window
                EditorApplication.ExecuteMenuItem("Window/General/Inspector");
            }
            else
            {
                // If no component exists, show a dialog and offer to create one
                if (EditorUtility.DisplayDialog("Advanced Leveling System",
                    "No AdvancedLevelingSystem component found in the scene. Would you like to create one?",
                    "Yes", "No"))
                {
                    // Create a new GameObject with the component
                    GameObject newObject = new GameObject("Advanced Leveling System");
                    newObject.AddComponent<AdvancedLevelingSystem>();
                    Selection.activeGameObject = newObject;
                    EditorApplication.ExecuteMenuItem("Window/General/Inspector");
                }
            }
        }
        private SerializedProperty levelingSystemDataProp;
        private SerializedProperty currentLevelProp;
        private SerializedProperty currentExperienceProp;
        private SerializedProperty requiredExperienceProp;
        private SerializedProperty audioSourceProp;
        private SerializedProperty levelUpClipProp;
        private SerializedProperty levelUpEffectProp;
        private SerializedProperty transitionDurationProp;


        private GUIStyle descriptionBoxStyle;
        private GUIStyle descriptionTextStyle;
        private GUIStyle difficultyRatingStyle;
        private bool showDescription = true;

        protected void OnEnable()
        {
            levelingSystemDataProp = serializedObject.FindProperty("levelingSystemData");
            currentLevelProp = serializedObject.FindProperty("currentLevel");
            currentExperienceProp = serializedObject.FindProperty("currentExperience");
            requiredExperienceProp = serializedObject.FindProperty("requiredExperience");
            audioSourceProp = serializedObject.FindProperty("audioSource");
            levelUpClipProp = serializedObject.FindProperty("levelUpClip");
            levelUpEffectProp = serializedObject.FindProperty("levelUpEffect");
            transitionDurationProp = serializedObject.FindProperty("transitionDuration");

            // No need to load difficulty ratings database anymore
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Initialize styles
            if (descriptionBoxStyle == null)
            {
                descriptionBoxStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    margin = new RectOffset(0, 0, 10, 10),
                    padding = new RectOffset(10, 10, 10, 10)
                };

                descriptionTextStyle = new GUIStyle(EditorStyles.label)
                {
                    wordWrap = true,
                    richText = true
                };

                difficultyRatingStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    alignment = TextAnchor.MiddleCenter
                };
            }

            // Draw the levelingSystemData field
            EditorGUILayout.PropertyField(levelingSystemDataProp, new GUIContent("Leveling System Data"));

            // Check if levelingSystemData is null and offer to create it
            if (levelingSystemDataProp.objectReferenceValue == null)
            {
                EditorGUILayout.HelpBox("LevelingSystemData is not assigned. This component requires a LevelingSystemData asset.", MessageType.Warning);

                if (GUILayout.Button("Create New LevelingSystemData Asset", GUILayout.Height(30)))
                {
                    // Create a new LevelingSystemData asset
                    LevelingSystemData newData = CreateLevelingSystemDataAsset();
                    if (newData != null)
                    {
                        levelingSystemDataProp.objectReferenceValue = newData;
                        serializedObject.ApplyModifiedProperties();
                    }
                }

                if (GUILayout.Button("Find Existing LevelingSystemData Asset", GUILayout.Height(30)))
                {
                    // Try to find an existing LevelingSystemData asset
                    LevelingSystemData existingData = Resources.Load<LevelingSystemData>("LevelingSystemData");
                    if (existingData != null)
                    {
                        levelingSystemDataProp.objectReferenceValue = existingData;
                        serializedObject.ApplyModifiedProperties();
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("LevelingSystemData Not Found",
                            "No LevelingSystemData asset found in the Resources folder. Please create a new one.", "OK");
                    }
                }
            }
            else
            {
                // Get the LevelingSystemData object
                LevelingSystemData data = (LevelingSystemData)levelingSystemDataProp.objectReferenceValue;

                // Show algorithm and difficulty rating if available
                if (data.levelingAlgorithm != null && data.levelingAlgorithm.DifficultyRating != null)
                {
                    // Get the algorithm and its difficulty rating
                    var algorithm = data.levelingAlgorithm;
                    var difficultyRating = algorithm.DifficultyRating;

                    // Get the difficulty info
                    string description = difficultyRating.description;
                    int rating = difficultyRating.stars;
                    Color difficultyColor = difficultyRating.ratingColor;

                    // Show/hide description foldout
                    showDescription = EditorGUILayout.Foldout(showDescription, "Difficulty Description", true);

                    if (showDescription)
                    {
                        // Use the difficulty color as background
                        GUI.backgroundColor = difficultyColor;
                        EditorGUILayout.BeginVertical(descriptionBoxStyle);
                        GUI.backgroundColor = Color.white;

                        // Display difficulty rating as stars
                        string ratingStars = "";
                        for (int i = 0; i < rating; i++)
                        {
                            ratingStars += "★";
                        }
                        for (int i = rating; i < 5; i++)
                        {
                            ratingStars += "☆";
                        }
                        EditorGUILayout.LabelField(ratingStars, difficultyRatingStyle);

                        // Display the description
                        EditorGUILayout.LabelField(description, descriptionTextStyle);

                        EditorGUILayout.EndVertical();
                    }

                    // Add button to open the difficulty editor
                    if (GUILayout.Button("Open Difficulty Editor"))
                    {
                        // Open the unified editor window with the Difficulties tab selected
                        var window = EditorWindow.GetWindow<AdvancedLevelingSystemEditorWindow>("Advanced Leveling System");

                        // Find the Difficulties tab manager and select it
                        if (window.GetType().GetField("tabManagers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(window) is List<ITabManager> tabManagers &&
                            window.GetType().GetField("currentTabIndex", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance) is System.Reflection.FieldInfo currentTabIndexField)
                        {
                            // Find the index of the Difficulties tab manager
                            for (int i = 0; i < tabManagers.Count; i++)
                            {
                                if (tabManagers[i].TabName == "Difficulties")
                                {
                                    currentTabIndexField.SetValue(window, i);
                                    break;
                                }
                            }
                        }
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("Difficulty descriptions asset not found. Create one using the Difficulty Editor.", MessageType.Info);
                    if (GUILayout.Button("Open Difficulty Editor"))
                    {
                        // Open the unified editor window with the Difficulties tab selected
                        var window = EditorWindow.GetWindow<AdvancedLevelingSystemEditorWindow>("Advanced Leveling System");

                        // Find the Difficulties tab manager and select it
                        if (window.GetType().GetField("tabManagers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(window) is List<ITabManager> tabManagers &&
                            window.GetType().GetField("currentTabIndex", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance) is System.Reflection.FieldInfo currentTabIndexField)
                        {
                            // Find the index of the Difficulties tab manager
                            for (int i = 0; i < tabManagers.Count; i++)
                            {
                                if (tabManagers[i].TabName == "Difficulties")
                                {
                                    currentTabIndexField.SetValue(window, i);
                                    break;
                                }
                            }
                        }
                    }
                }

                EditorGUILayout.Space(10);

                // Draw the rest of the properties
                EditorGUILayout.PropertyField(currentLevelProp);
                EditorGUILayout.PropertyField(currentExperienceProp);
                EditorGUILayout.PropertyField(requiredExperienceProp);
                EditorGUILayout.PropertyField(audioSourceProp);
                EditorGUILayout.PropertyField(levelUpClipProp);

                EditorGUILayout.PropertyField(levelUpEffectProp);
                EditorGUILayout.PropertyField(transitionDurationProp);
            }

            serializedObject.ApplyModifiedProperties();
        }

        private LevelingSystemData CreateLevelingSystemDataAsset()
        {
            // Create the Resources directory if it doesn't exist
            string resourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
            if (!System.IO.Directory.Exists(resourcesPath))
            {
                System.IO.Directory.CreateDirectory(resourcesPath);
                AssetDatabase.Refresh();
            }

            // Create the asset
            string assetPath = resourcesPath + "/LevelingSystemData.asset";

            // Check if the asset already exists
            if (System.IO.File.Exists(assetPath))
            {
                bool overwrite = EditorUtility.DisplayDialog("Asset Already Exists",
                    "A LevelingSystemData asset already exists. Do you want to overwrite it?",
                    "Yes, Overwrite", "No, Use Existing");

                if (!overwrite)
                {
                    return AssetDatabase.LoadAssetAtPath<LevelingSystemData>(assetPath);
                }
            }

            // Create a new instance
            var asset = ScriptableObject.CreateInstance<LevelingSystemData>();

            // Set default values
            asset.maxLevel = 30;
            asset.initialRequiredExperience = 250;
            asset.levelUpMultiplier = 1.1f;

            // Set default algorithm
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Try to find a linear algorithm or use the first one
                LevelingAlgorithmBase linearAlgorithm = algorithmDB.algorithms.Find(a => a.Name.Contains("Linear"));
                asset.levelingAlgorithm = linearAlgorithm != null ? linearAlgorithm : algorithmDB.algorithms[0];
            }

            // Create or overwrite the asset
            if (System.IO.File.Exists(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }

            AssetDatabase.CreateAsset(asset, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log("Created LevelingSystemData asset at: " + assetPath);

            return asset;
        }
    }
}
