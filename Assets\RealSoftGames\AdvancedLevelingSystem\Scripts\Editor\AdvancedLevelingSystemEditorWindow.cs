using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class AdvancedLevelingSystemEditorWindow : EditorWindowTemplate
    {
        #region Variables

        // Tab managers
        private AlgorithmsTabManager algorithmsTabManager;
        private ProgressionTabManager progressionTabManager; // Combined tab that replaces ExperienceRewards and Simulation
        private AlgorithmDesignerTabManager algorithmDesignerTabManager;
        private SettingsTabManager settingsTabManager;
        private AboutTabManager aboutTabManager;

        // We'll use the tab managers list from the base class

        // Shared data
        private DifficultyRatingDatabase difficultyRatingDatabase;
        private ExperienceRewardsData rewardsData;

        // Shared settings between tabs (minimum 1.0 to avoid issues with values below 1.0)
        public float SharedLevelUpMultiplier { get; private set; } = 1.1f;
        public int SharedStartingExperience { get; private set; } = 250;
        public int SharedStartingLevel { get; private set; } = 1;
        public int SharedMaxLevel { get; private set; } = 30;

        // Method to update shared settings
        public void UpdateSharedSettings(float? levelUpMultiplier = null, int? startingExperience = null, int? startingLevel = null, int? maxLevel = null)
        {
            bool changed = false;

            if (levelUpMultiplier.HasValue)
            {
                // Ensure the multiplier is at least 1.0 to avoid issues with values below 1.0
                float validMultiplier = Mathf.Max(1.0f, levelUpMultiplier.Value);
                if (SharedLevelUpMultiplier != validMultiplier)
                {
                    SharedLevelUpMultiplier = validMultiplier;
                    changed = true;
                }
            }

            if (startingExperience.HasValue && SharedStartingExperience != startingExperience.Value)
            {
                SharedStartingExperience = startingExperience.Value;
                changed = true;
            }

            if (startingLevel.HasValue && SharedStartingLevel != startingLevel.Value)
            {
                SharedStartingLevel = startingLevel.Value;
                changed = true;
            }

            if (maxLevel.HasValue && SharedMaxLevel != maxLevel.Value)
            {
                SharedMaxLevel = maxLevel.Value;
                changed = true;
            }

            if (changed)
            {
                // Save to the rewards data
                if (rewardsData != null)
                {
                    rewardsData.levelUpMultiplier = SharedLevelUpMultiplier;
                    rewardsData.startingExperience = SharedStartingExperience;
                    rewardsData.startingLevel = SharedStartingLevel;
                    rewardsData.maxLevel = SharedMaxLevel;
                    EditorUtility.SetDirty(rewardsData);
                }

                // Force repaint to update all tabs
                Repaint();
            }
        }

        #endregion Variables

        [MenuItem("Tools/RealSoftGames/Advanced Leveling System", false, 0)]
        public new static void ShowWindow()
        {
            var window = GetWindow<AdvancedLevelingSystemEditorWindow>("Advanced Leveling System");
            window.minSize = new Vector2(900, 600);
        }

        protected override void OnEnable()
        {
            NavigationPanelWidth = 180f;
            base.OnEnable();

            // Load shared data
            LoadSharedData();

            // Initialize tab managers

            try
            {
                // Initialize the ScriptableAlgorithmRegistry first to ensure algorithms are loaded
                // Only initialize if not already initialized
                if (!ScriptableAlgorithmRegistry.IsInitialized)
                {
                    Debug.Log("AdvancedLevelingSystemEditorWindow: Initializing ScriptableAlgorithmRegistry");
                    ScriptableAlgorithmRegistry.Initialize();
                }
                else
                {
                    Debug.Log("AdvancedLevelingSystemEditorWindow: ScriptableAlgorithmRegistry already initialized, skipping");
                }

                // Check if we have any algorithms loaded
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();
                if (algorithms == null || !algorithms.Any())
                {
                    Debug.LogWarning("No algorithms found in the registry. Opening the Settings tab first to allow creating algorithms.");
                    // Create the Settings tab first so the user can create algorithms
                    settingsTabManager = new SettingsTabManager(this);
                }

                algorithmsTabManager = new AlgorithmsTabManager(this, difficultyRatingDatabase);
                progressionTabManager = new ProgressionTabManager(this, rewardsData);
                algorithmDesignerTabManager = new AlgorithmDesignerTabManager(this);

                // Create the settings tab if not already created
                if (settingsTabManager == null)
                {
                    settingsTabManager = new SettingsTabManager(this);
                }

                // Use the AboutTabManager that fetches products from the API
                aboutTabManager = new AboutTabManager(this);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error initializing tab managers: {ex.Message}");

                // Ensure we at least have the settings tab available
                if (settingsTabManager == null)
                {
                    settingsTabManager = new SettingsTabManager(this);
                }
            }

            // Register tab managers with the base class in the desired order
            // The order of registration determines the order of tabs in the UI
            // Only register tabs that were successfully created
            if (algorithmsTabManager != null) RegisterTabManager(algorithmsTabManager);         // First tab
            if (progressionTabManager != null) RegisterTabManager(progressionTabManager);       // Second tab (combined)
            if (algorithmDesignerTabManager != null) RegisterTabManager(algorithmDesignerTabManager);     // Algorithm Designer tab
            if (settingsTabManager != null) RegisterTabManager(settingsTabManager);           // Settings tab
            if (aboutTabManager != null) RegisterTabManager(aboutTabManager);              // About tab

            // If we have no tabs registered, force register the settings tab
            if (tabManagers.Count == 0 && settingsTabManager != null)
            {
                RegisterTabManager(settingsTabManager);
            }
        }

        private void LoadSharedData()
        {
            // Ensure Resources directory exists
            string resourcesDirectory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
            if (!Directory.Exists(resourcesDirectory))
            {
                Directory.CreateDirectory(resourcesDirectory);
                AssetDatabase.Refresh();
            }

            // Load difficulty rating database
            difficultyRatingDatabase = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (difficultyRatingDatabase == null)
            {
                // Create the database if it doesn't exist
                DifficultyRatingDatabaseManager.CreateDatabase();
                difficultyRatingDatabase = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            }

            // Load and initialize the algorithm database
            InitializeAlgorithmDatabase();

            // Load experience rewards data
            rewardsData = Resources.Load<ExperienceRewardsData>("ExperienceRewardsData");
            if (rewardsData == null)
            {
                CreateDefaultExperienceRewardsData();
                rewardsData = Resources.Load<ExperienceRewardsData>("ExperienceRewardsData");
            }

            // Initialize shared settings from rewards data
            if (rewardsData != null)
            {
                SharedLevelUpMultiplier = rewardsData.levelUpMultiplier;
                SharedStartingExperience = rewardsData.startingExperience;
                SharedStartingLevel = rewardsData.startingLevel;
                SharedMaxLevel = rewardsData.maxLevel;
            }
        }

        private void InitializeAlgorithmDatabase()
        {
            // Load the algorithm database
            string databasePath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset";
            AlgorithmDatabase database = AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(databasePath);

            if (database == null)
            {
                // Create the database if it doesn't exist
                database = ScriptableObject.CreateInstance<AlgorithmDatabase>();
                AssetDatabase.CreateAsset(database, databasePath);
                Debug.Log("Created new AlgorithmDatabase");
            }

            // No need to refresh the database - we'll use it as is
            // This simplifies the system and makes it more reliable
        }

        // Flag to prevent recursive calls to RefreshAlgorithmDatabase
        private static bool isRefreshingDatabase = false;

        /// <summary>
        /// Refreshes the AlgorithmDatabase with all algorithms in the Resources/Algorithms folder
        /// </summary>
        /// <param name="database">The algorithm database to refresh</param>
        public static void RefreshAlgorithmDatabase(AlgorithmDatabase database)
        {
            // Prevent recursive calls
            if (isRefreshingDatabase)
            {
                Debug.LogWarning("Already refreshing database, skipping recursive call");
                return;
            }

            try
            {
                isRefreshingDatabase = true;

                if (database == null)
                {
                    Debug.LogError("Cannot refresh null AlgorithmDatabase");
                    return;
                }

                // Get all existing algorithms in the database
                HashSet<string> existingAlgorithmPaths = new HashSet<string>();
                List<LevelingAlgorithmBase> algorithmsToKeep = new List<LevelingAlgorithmBase>();

                // Track existing algorithms by their asset paths
                foreach (var algorithm in database.algorithms)
                {
                    if (algorithm != null)
                    {
                        string assetPath = AssetDatabase.GetAssetPath(algorithm);
                        if (!string.IsNullOrEmpty(assetPath))
                        {
                            existingAlgorithmPaths.Add(assetPath);
                            algorithmsToKeep.Add(algorithm);
                        }
                    }
                }

                // Find all algorithm assets in the Resources/Algorithms folder
                string[] guids = AssetDatabase.FindAssets("t:LevelingAlgorithmBase", new[] { "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms" });
                int newAlgorithmsCount = 0;

                // Add any new algorithms to the database
                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);

                    // Skip if this algorithm is already in the database
                    if (existingAlgorithmPaths.Contains(path))
                        continue;

                    LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                    if (algorithm != null)
                    {
                        algorithmsToKeep.Add(algorithm);
                        newAlgorithmsCount++;
                    }
                }

                // Update the database with the refreshed list
                database.algorithms.Clear();
                foreach (var algorithm in algorithmsToKeep)
                {
                    database.algorithms.Add(algorithm);
                }

                // Save changes
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();

                if (newAlgorithmsCount > 0)
                {
                    Debug.Log($"Added {newAlgorithmsCount} new algorithms to the AlgorithmDatabase. Total: {database.algorithms.Count}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error refreshing algorithm database: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                isRefreshingDatabase = false;
            }
        }

        // Method removed as part of refactoring - now using DifficultyRatingDatabaseManager.CreateDatabase() directly

        private void CreateDefaultExperienceRewardsData()
        {
            string dataPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/ExperienceRewardsData.asset";

            // Check if the data directory exists, if not create it
            string dataDirectory = Path.GetDirectoryName(dataPath);
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            // Create a new ScriptableObject
            ExperienceRewardsData rewardsData = CreateInstance<ExperienceRewardsData>();

            // Set default values
            // Find a suitable default algorithm (SineWave or similar)
            LevelingAlgorithmBase[] algorithms = Resources.LoadAll<LevelingAlgorithmBase>("Algorithms");
            if (algorithms != null && algorithms.Length > 0)
            {
                // Try to find SineWave or similar algorithm
                LevelingAlgorithmBase defaultAlgorithm = algorithms.FirstOrDefault(a =>
                    a.Name.Contains("Sine") || a.Name.Contains("Wave") || a.Name.Contains("Medium"));

                // If not found, use the first available algorithm
                if (defaultAlgorithm == null && algorithms.Length > 0)
                {
                    defaultAlgorithm = algorithms[0];
                }

                rewardsData.selectedAlgorithm = defaultAlgorithm;
            }

            rewardsData.levelUpMultiplier = 1.1f;
            rewardsData.startingExperience = 250;
            rewardsData.startingLevel = 1;
            rewardsData.maxLevel = 50;
            rewardsData.levelRangeSize = 5;
            rewardsData.showDetailedBreakdown = true;

            // Add default reward categories in the desired order
            // Quest categories
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Epic Quest", multiplier = 0.12f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Hard Quest", multiplier = 0.08f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Medium Quest", multiplier = 0.05f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Easy Quest", multiplier = 0.03f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Daily Activity", multiplier = 0.02f });

            // Boss and special enemies
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "World Boss", multiplier = 0.10f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Boss Enemy", multiplier = 0.05f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Elite Enemy", multiplier = 0.015f });

            // Regular enemies
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Hard Enemy", multiplier = 0.003f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Medium Enemy", multiplier = 0.002f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Easy Enemy", multiplier = 0.001f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Very Easy Enemy", multiplier = 0.0005f });

            // Crafting and gathering
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Crafting Item", multiplier = 0.0001f });
            rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Gathering Resource", multiplier = 0.0001f });

            // Create the asset
            AssetDatabase.CreateAsset(rewardsData, dataPath);
            AssetDatabase.SaveAssets();

            Debug.Log("Default Experience Rewards Data created at: " + dataPath);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
        }

        // Using the base implementation of EditorGUILayoutLeftPanel which now handles dynamic tabs

        protected override void EditorGUILayoutMainBody()
        {
            EditorGUI.BeginChangeCheck();

            // Call the base implementation to render the current tab
            // The base class now handles tab selection based on the currentTabIndex
            base.EditorGUILayoutMainBody();

            if (EditorGUI.EndChangeCheck())
            {
                // Save changes
                EditorUtility.SetDirty(difficultyRatingDatabase);
                EditorUtility.SetDirty(rewardsData);
                AssetDatabase.SaveAssets();
            }
        }

        protected override void Update()
        {
            base.Update();
        }
    }
}
