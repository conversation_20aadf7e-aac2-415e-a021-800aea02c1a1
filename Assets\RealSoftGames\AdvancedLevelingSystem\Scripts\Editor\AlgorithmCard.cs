using UnityEngine;
using UnityEditor;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// UI component for displaying algorithm cards in the editor
    /// </summary>
    public class AlgorithmCard
    {
        // Styles
        private GUIStyle cardStyle;
        private GUIStyle titleStyle;
        private GUIStyle descriptionStyle;
        private GUIStyle formulaStyle;
        private GUIStyle ratingStyle;
        private GUIStyle buttonStyle;

        // Card dimensions
        private const float CardWidth = 300f;
        private const float CardHeight = 400f;
        private const float CardPadding = 10f;
        private const float TitleHeight = 40f;
        private const float DescriptionHeight = 80f;
        private const float FormulaHeight = 60f;
        private const float GraphHeight = 150f;
        private const float ButtonHeight = 30f;

        // Graph preview
        private LevelingCurvePreview curvePreview;

        // Initialization flag
        private bool initialized = false;

        /// <summary>
        /// Creates a new instance of the AlgorithmCard class
        /// </summary>
        public AlgorithmCard()
        {
            curvePreview = new LevelingCurvePreview();
            curvePreview.GraphSize = new Vector2(1.0f, 0.8f);
            curvePreview.GraphPadding = 10;
        }

        /// <summary>
        /// Initializes the styles for the card
        /// </summary>
        private void InitializeStyles()
        {
            if (initialized)
                return;

            cardStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(10, 10, 10, 10),
                margin = new RectOffset(5, 5, 5, 5),
                alignment = TextAnchor.MiddleCenter,
                stretchWidth = false,
                stretchHeight = false,
                fixedWidth = CardWidth,
                fixedHeight = CardHeight
            };

            titleStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleCenter,
                wordWrap = true,
                stretchWidth = true
            };

            descriptionStyle = new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                alignment = TextAnchor.UpperLeft,
                stretchWidth = true
            };

            formulaStyle = new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                fontStyle = FontStyle.Italic,
                alignment = TextAnchor.UpperLeft,
                stretchWidth = true
            };

            ratingStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleCenter,
                stretchWidth = true
            };

            buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleCenter,
                stretchWidth = true
            };

            initialized = true;
        }

        /// <summary>
        /// Draws the algorithm card
        /// </summary>
        /// <param name="algorithm">The algorithm to display</param>
        /// <param name="levelUpMultiplier">The level up multiplier to use</param>
        /// <param name="startingExperience">Starting experience value</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="onSelect">Callback when the algorithm is selected</param>
        /// <param name="isSelected">Whether this algorithm is currently selected</param>
        public void Draw(ILevelingAlgorithm algorithm, float levelUpMultiplier,
            int startingExperience, int startingLevel, int maxLevel,
            Action<ILevelingAlgorithm> onSelect, bool isSelected = false)
        {
            if (algorithm == null)
                return;

            // Initialize styles if needed
            InitializeStyles();

            // Get the difficulty rating color if available
            Color algorithmColor = Color.white;
            if (algorithm is LevelingAlgorithmBase scriptableAlgorithm &&
                scriptableAlgorithm.difficultyRating != null)
            {
                algorithmColor = scriptableAlgorithm.difficultyRating.ratingColor;
            }

            // Use the algorithm color for the background of the card
            Color originalColor = GUI.backgroundColor;
            GUI.backgroundColor = isSelected ?
                Color.Lerp(algorithmColor, Color.cyan, 0.3f) :
                algorithmColor;

            // Begin the card
            EditorGUILayout.BeginVertical(cardStyle);

            // Title
            EditorGUILayout.LabelField(algorithm.Name, titleStyle, GUILayout.Height(TitleHeight));

            // Rating stars if available
            if (algorithm is LevelingAlgorithmBase scriptableAlg &&
                scriptableAlg.difficultyRating != null)
            {
                string ratingStars = "";
                for (int i = 0; i < scriptableAlg.difficultyRating.stars; i++)
                {
                    ratingStars += "★";
                }
                for (int i = scriptableAlg.difficultyRating.stars; i < 5; i++)
                {
                    ratingStars += "☆";
                }

                EditorGUILayout.LabelField(ratingStars, ratingStyle);
            }

            // Description
            EditorGUILayout.LabelField("Description:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField(algorithm.Description, descriptionStyle, GUILayout.Height(DescriptionHeight));

            // Formula
            EditorGUILayout.LabelField("Formula:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField(algorithm.FormulaExplanation, formulaStyle, GUILayout.Height(FormulaHeight));

            // Graph preview
            EditorGUILayout.LabelField("Experience Curve:", EditorStyles.boldLabel);

            try
            {
                // Draw the curve preview
                curvePreview.DrawCurvePreview(
                    algorithm,
                    levelUpMultiplier,
                    null,
                    startingExperience,
                    startingLevel,
                    maxLevel,
                    CardWidth - (2 * CardPadding)
                );
            }
            catch (Exception ex)
            {
                EditorGUILayout.HelpBox($"Error drawing curve: {ex.Message}", MessageType.Error);
            }

            // Select button
            if (GUILayout.Button(isSelected ? "Selected" : "Select", buttonStyle, GUILayout.Height(ButtonHeight)))
            {
                onSelect?.Invoke(algorithm);
            }

            // End the card
            EditorGUILayout.EndVertical();

            // Restore the original background color
            GUI.backgroundColor = originalColor;
        }
    }
}
