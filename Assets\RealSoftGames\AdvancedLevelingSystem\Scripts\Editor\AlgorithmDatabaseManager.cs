using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System;
using System.Reflection;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Manages the creation, initialization, and conversion of algorithm databases.
    /// This class automatically creates the database if it doesn't exist when Unity starts.
    /// </summary>
    [InitializeOnLoad]
    public class AlgorithmDatabaseManager
    {
        private const string ResourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
        private const string DatabasePath = ResourcesPath + "/AlgorithmDatabase.asset";
        private const string AlgorithmsPath = ResourcesPath + "/Algorithms";

        /// <summary>
        /// Static constructor that runs when Unity starts or when scripts are recompiled.
        /// Checks if the database exists and creates it if needed.
        /// </summary>
        static AlgorithmDatabaseManager()
        {
            // Check if the database exists
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                // Create the database
                CreateDatabase();
            }
        }

        /// <summary>
        /// Creates the AlgorithmDatabase.
        /// </summary>
        // No longer exposed as a menu item - accessed through the Settings tab
        public static void CreateDatabase()
        {
            // Create the Resources directory if it doesn't exist
            if (!Directory.Exists(ResourcesPath))
            {
                Directory.CreateDirectory(ResourcesPath);
            }

            // Check if the database already exists
            if (AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(DatabasePath) != null)
            {
                if (!EditorUtility.DisplayDialog("Overwrite Database?",
                    "An AlgorithmDatabase already exists. Do you want to overwrite it?",
                    "Yes", "No"))
                {
                    Debug.Log("Database creation cancelled by user.");
                    return;
                }
            }

            // Create the database
            AlgorithmDatabase database = ScriptableObject.CreateInstance<AlgorithmDatabase>();

            // Save the database
            AssetDatabase.CreateAsset(database, DatabasePath);

            // Save all the created assets
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log("Algorithm database created successfully!");

            // Select the database in the Project window
            Selection.activeObject = database;
        }

        /// <summary>
        /// Creates a default algorithm database with standard algorithms
        /// </summary>
        // No longer exposed as a menu item - accessed through the Settings tab
        public static void CreateDefaultAlgorithmDatabase()
        {
            // Create the Resources directory if it doesn't exist
            if (!Directory.Exists(ResourcesPath))
            {
                Directory.CreateDirectory(ResourcesPath);
            }

            // Check if the database already exists
            if (AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(DatabasePath) != null)
            {
                if (!EditorUtility.DisplayDialog("Overwrite Database?",
                    "An AlgorithmDatabase already exists. Do you want to overwrite it with default algorithms?",
                    "Yes", "No"))
                {
                    Debug.Log("Database creation cancelled by user.");
                    return;
                }
            }

            // Create the database
            AlgorithmDatabase database = ScriptableObject.CreateInstance<AlgorithmDatabase>();

            // Create default algorithms
            CreateDefaultAlgorithms(database);

            // Save the database
            AssetDatabase.CreateAsset(database, DatabasePath);

            // Save all the created assets
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log("Default algorithm database created successfully!");

            // Select the database in the Project window
            Selection.activeObject = database;
        }

        /// <summary>
        /// Creates default algorithms and adds them to the database
        /// </summary>
        private static void CreateDefaultAlgorithms(AlgorithmDatabase database)
        {

            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty ratings
            DifficultyRating veryEasy = ratingDB.GetRatingByName("Very Easy");
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");

            // Create algorithms using AlgorithmFactory
            var linear = AlgorithmFactory.CreateLinearAlgorithm(easy);
            var exponential = AlgorithmFactory.CreateExponentialAlgorithm(hard);
            var sinusoidal = AlgorithmFactory.CreateSinusoidalAlgorithm(medium);
            var heartbeat = AlgorithmFactory.CreateHeartbeatAlgorithm(medium);
            var zigzag = AlgorithmFactory.CreateZigzagAlgorithm(hard);
            var random = AlgorithmFactory.CreateRandomAlgorithm(veryHard);
            var combined = AlgorithmFactory.CreateCombinedAlgorithm(veryHard);

            // Create a drawn pattern algorithm
            var drawnPattern = AlgorithmFactory.CreateDrawnPatternAlgorithm(
                "Custom Pattern",
                "Custom pattern created in the Pattern Creator.",
                "Custom drawn pattern",
                medium);

            // Add more points to the drawn pattern
            drawnPattern.points.Clear();
            drawnPattern.points.Add(new Vector2(0.0f, 1.1f));
            drawnPattern.points.Add(new Vector2(0.25f, 1.15f));
            drawnPattern.points.Add(new Vector2(0.5f, 1.2f));
            drawnPattern.points.Add(new Vector2(0.75f, 1.25f));
            drawnPattern.points.Add(new Vector2(1.0f, 1.3f));

            // Add algorithms to the database and asset
            database.AddAlgorithm(linear);
            database.AddAlgorithm(exponential);
            database.AddAlgorithm(sinusoidal);
            database.AddAlgorithm(heartbeat);
            database.AddAlgorithm(zigzag);
            database.AddAlgorithm(random);
            database.AddAlgorithm(combined);
            database.AddAlgorithm(drawnPattern);

            AssetDatabase.AddObjectToAsset(linear, database);
            AssetDatabase.AddObjectToAsset(exponential, database);
            AssetDatabase.AddObjectToAsset(sinusoidal, database);
            AssetDatabase.AddObjectToAsset(heartbeat, database);
            AssetDatabase.AddObjectToAsset(zigzag, database);
            AssetDatabase.AddObjectToAsset(drawnPattern, database);
            AssetDatabase.AddObjectToAsset(random, database);
            AssetDatabase.AddObjectToAsset(combined, database);
        }

        /// <summary>
        /// Converts legacy algorithm classes to ScriptableObjects.
        /// </summary>
        // No longer exposed as a menu item - accessed through the Settings tab
        public static void ConvertAlgorithms()
        {
            // Create the Resources directory if it doesn't exist
            if (!Directory.Exists(ResourcesPath))
            {
                Directory.CreateDirectory(ResourcesPath);
            }

            // Create the Algorithms directory if it doesn't exist
            if (!Directory.Exists(AlgorithmsPath))
            {
                Directory.CreateDirectory(AlgorithmsPath);
            }

            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Create or load the algorithm database
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB == null)
            {
                algorithmDB = ScriptableObject.CreateInstance<AlgorithmDatabase>();
                AssetDatabase.CreateAsset(algorithmDB, DatabasePath);
            }

            // Find all algorithm classes that inherit from LevelingAlgorithmBase
            List<Type> algorithmTypes = FindAlgorithmTypes();

            // Create a mapping from old difficulty to new rating
            Dictionary<string, DifficultyRating> difficultyMap = CreateDifficultyMap(ratingDB);

            // Convert each algorithm to a ScriptableObject
            int convertedCount = 0;
            foreach (Type algorithmType in algorithmTypes)
            {
                try
                {
                    // Skip abstract classes
                    if (algorithmType.IsAbstract)
                        continue;

                    // Create an instance of the algorithm
                    ILevelingAlgorithm algorithm = (ILevelingAlgorithm)Activator.CreateInstance(algorithmType);

                    // Check if this algorithm already exists in the database
                    if (algorithmDB.GetAlgorithmByName(algorithm.Name) != null)
                    {
                        Debug.Log($"Algorithm '{algorithm.Name}' already exists in the database. Skipping.");
                        continue;
                    }

                    // Create a new LevelingAlgorithmBase for the algorithm
                    var algorithmSO = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

                    // Set the properties
                    algorithmSO.algorithmName = algorithm.Name;
                    algorithmSO.description = algorithm.Description;
                    algorithmSO.formulaExplanation = algorithm.FormulaExplanation;

                    // Get the difficulty name from the algorithm type
                    string difficultyName = GetDifficultyNameFromType(algorithmType);

                    // Set the difficulty rating
                    if (difficultyMap.TryGetValue(difficultyName, out DifficultyRating rating))
                    {
                        algorithmSO.difficultyRating = rating;
                    }
                    else
                    {
                        algorithmSO.difficultyRating = ratingDB.GetRatingByName("Medium");
                    }

                    // Set the algorithm type to CodedFormula by default
                    algorithmSO.algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;

                    // Save the ScriptableObject
                    string assetPath = $"{AlgorithmsPath}/{algorithm.Name}Algorithm.asset";
                    AssetDatabase.CreateAsset(algorithmSO, assetPath);

                    // Add the algorithm to the database
                    algorithmDB.AddAlgorithm(algorithmSO);

                    convertedCount++;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error converting algorithm {algorithmType.Name}: {ex.Message}");
                }
            }

            // Save the database
            EditorUtility.SetDirty(algorithmDB);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log($"Converted {convertedCount} algorithms to ScriptableObjects.");
        }

        /// <summary>
        /// Creates a mapping from old difficulty enum values to new DifficultyRating objects.
        /// </summary>
        private static Dictionary<string, DifficultyRating> CreateDifficultyMap(DifficultyRatingDatabase ratingDB)
        {
            Dictionary<string, DifficultyRating> difficultyMap = new Dictionary<string, DifficultyRating>();

            // Map the old difficulties to the new ratings
            difficultyMap["VeryEasy"] = ratingDB.GetRatingByName("Very Easy");
            difficultyMap["Flat"] = ratingDB.GetRatingByName("Very Easy");
            difficultyMap["Linear"] = ratingDB.GetRatingByName("Easy");
            difficultyMap["EarlyBoost"] = ratingDB.GetRatingByName("Easy");
            difficultyMap["Decremental"] = ratingDB.GetRatingByName("Easy");
            difficultyMap["MicroProgression"] = ratingDB.GetRatingByName("Easy");
            difficultyMap["Elastic"] = ratingDB.GetRatingByName("Easy");
            difficultyMap["SineWave"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Logarithmic"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Root"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["DiminishingReturns"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Asymptotic"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Heartbeat"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Pulsating"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Zigzag"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Crescendo"] = ratingDB.GetRatingByName("Medium");
            difficultyMap["Exponential"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Quadratic"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Polynomial"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Burst"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Ripple"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Quantum"] = ratingDB.GetRatingByName("Hard");
            difficultyMap["Cubic"] = ratingDB.GetRatingByName("Very Hard");
            difficultyMap["Cyclical"] = ratingDB.GetRatingByName("Very Hard");
            difficultyMap["Spiral"] = ratingDB.GetRatingByName("Very Hard");
            difficultyMap["Fractal"] = ratingDB.GetRatingByName("Very Hard");
            difficultyMap["Custom"] = ratingDB.GetRatingByName("Medium");

            return difficultyMap;
        }

        /// <summary>
        /// Finds all types that inherit from LevelingAlgorithmBase.
        /// </summary>
        private static List<Type> FindAlgorithmTypes()
        {
            List<Type> algorithmTypes = new List<Type>();

            // Get all assemblies
            Assembly[] assemblies = AppDomain.CurrentDomain.GetAssemblies();

            // Find all types that inherit from LevelingAlgorithmBase
            foreach (Assembly assembly in assemblies)
            {
                try
                {
                    Type[] types = assembly.GetTypes();
                    foreach (Type type in types)
                    {
                        if (type.IsClass && !type.IsAbstract && typeof(ILevelingAlgorithm).IsAssignableFrom(type))
                        {
                            algorithmTypes.Add(type);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error loading types from assembly {assembly.FullName}: {ex.Message}");
                }
            }

            return algorithmTypes;
        }

        /// <summary>
        /// Gets the difficulty name from an algorithm type.
        /// </summary>
        private static string GetDifficultyNameFromType(Type algorithmType)
        {
            // Try to get the difficulty from the algorithm type
            try
            {
                // Create an instance of the algorithm
                ILevelingAlgorithm algorithm = (ILevelingAlgorithm)Activator.CreateInstance(algorithmType);

                // Get the difficulty property using reflection
                PropertyInfo difficultyProperty = algorithmType.GetProperty("Difficulty");
                if (difficultyProperty != null)
                {
                    object difficultyValue = difficultyProperty.GetValue(algorithm);
                    if (difficultyValue != null)
                    {
                        // Get the enum value name
                        return difficultyValue.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error getting difficulty from algorithm {algorithmType.Name}: {ex.Message}");
            }

            // Default to Medium if we can't determine the difficulty
            return "Medium";
        }
    }
}
