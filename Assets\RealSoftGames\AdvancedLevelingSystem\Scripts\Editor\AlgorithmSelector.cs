using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Editor utility for selecting algorithms from both ScriptableObject and code-based implementations
    /// </summary>
    public class AlgorithmSelector
    {
        private static bool isInitialized = false;
        private static Dictionary<string, ILevelingAlgorithm> allAlgorithms = new Dictionary<string, ILevelingAlgorithm>();
        private static Dictionary<string, List<string>> algorithmCategories = new Dictionary<string, List<string>>();

        /// <summary>
        /// Initializes the algorithm selector by loading all available algorithms
        /// </summary>
        public static void Initialize()
        {
            if (isInitialized)
                return;

            // Clear existing data
            allAlgorithms.Clear();
            algorithmCategories.Clear();

            // Initialize category lists
            algorithmCategories["Very Easy"] = new List<string>();
            algorithmCategories["Easy"] = new List<string>();
            algorithmCategories["Medium"] = new List<string>();
            algorithmCategories["Hard"] = new List<string>();
            algorithmCategories["Very Hard"] = new List<string>();
            algorithmCategories["Custom"] = new List<string>();

            // Initialize the ScriptableObject registry
            ScriptableAlgorithmRegistry.Initialize();

            // Load ScriptableObject-based algorithms
            foreach (var algorithm in ScriptableAlgorithmRegistry.GetAllAlgorithms())
            {
                string name = algorithm.Name;

                // Ensure unique names
                if (allAlgorithms.ContainsKey(name))
                {
                    int counter = 1;
                    string baseName = name;
                    while (allAlgorithms.ContainsKey(name))
                    {
                        name = $"{baseName} ({counter})";
                        counter++;
                    }
                }

                allAlgorithms[name] = algorithm;

                // Add to appropriate category
                AddToCategory(algorithm);
            }

            isInitialized = true;
        }

        /// <summary>
        /// Adds an algorithm to the appropriate category
        /// </summary>
        private static void AddToCategory(ILevelingAlgorithm algorithm)
        {
            // Use algorithm name to determine category
            string name = algorithm.Name.ToLower();
            string category = "Custom";

            // Categorize based on name keywords
            if (name.Contains("easy") || name.Contains("flat") || name.Contains("simple"))
            {
                category = "Very Easy";
            }
            else if (name.Contains("linear") || name.Contains("boost") || name.Contains("decremental") ||
                     name.Contains("micro") || name.Contains("elastic"))
            {
                category = "Easy";
            }
            else if (name.Contains("moderate") || name.Contains("balanced") || name.Contains("heartbeat") ||
                     name.Contains("sine") || name.Contains("sinusoidal") || name.Contains("wave"))
            {
                category = "Medium";
            }
            else if (name.Contains("challenging") || name.Contains("exponential") || name.Contains("logarithmic") ||
                     name.Contains("sawtooth"))
            {
                category = "Hard";
            }
            else if (name.Contains("hardcore") || name.Contains("brutal") || name.Contains("impossible") ||
                     name.Contains("extreme"))
            {
                category = "Very Hard";
            }

            algorithmCategories[category].Add(algorithm.Name);
        }

        /// <summary>
        /// Gets an algorithm by name
        /// </summary>
        public static ILevelingAlgorithm GetAlgorithm(string name)
        {
            if (!isInitialized)
                Initialize();

            if (allAlgorithms.TryGetValue(name, out ILevelingAlgorithm algorithm))
            {
                return algorithm;
            }

            // Fallback to Linear if the requested algorithm isn't found
            Debug.LogWarning($"Algorithm '{name}' not found. Using Linear as fallback.");
            return allAlgorithms.ContainsKey("Linear") ? allAlgorithms["Linear"] : allAlgorithms.Values.FirstOrDefault();
        }

        /// <summary>
        /// Gets all algorithm names for dropdown selection
        /// </summary>
        public static string[] GetAllAlgorithmNames()
        {
            if (!isInitialized)
                Initialize();

            return allAlgorithms.Keys.ToArray();
        }

        /// <summary>
        /// Gets all algorithms grouped by category
        /// </summary>
        public static Dictionary<string, List<string>> GetAlgorithmsByCategory()
        {
            if (!isInitialized)
                Initialize();

            return algorithmCategories;
        }

        /// <summary>
        /// Draws a dropdown for selecting an algorithm
        /// </summary>
        public static string DrawAlgorithmSelector(string label, string currentAlgorithm, float labelWidth = 150f)
        {
            if (!isInitialized)
                Initialize();

            EditorGUILayout.BeginHorizontal();

            // Draw the label
            EditorGUILayout.LabelField(label, GUILayout.Width(labelWidth));

            // Create the dropdown button
            string displayName = string.IsNullOrEmpty(currentAlgorithm) ? "Select Algorithm" : currentAlgorithm;
            if (EditorGUILayout.DropdownButton(new GUIContent(displayName), FocusType.Keyboard))
            {
                // Create the dropdown menu
                GenericMenu menu = new GenericMenu();

                // Add algorithms by category
                foreach (var category in algorithmCategories)
                {
                    if (category.Value.Count > 0)
                    {
                        // Add a category header
                        menu.AddDisabledItem(new GUIContent(category.Key));

                        // Add algorithms in this category
                        foreach (var algorithmName in category.Value.OrderBy(n => n))
                        {
                            menu.AddItem(
                                new GUIContent($"  {algorithmName}"),
                                algorithmName == currentAlgorithm,
                                () => currentAlgorithm = algorithmName);
                        }

                        // Add a separator
                        menu.AddSeparator("");
                    }
                }

                // Show the menu
                menu.ShowAsContext();
            }

            EditorGUILayout.EndHorizontal();

            return currentAlgorithm;
        }

        /// <summary>
        /// Draws a dropdown for selecting an algorithm with a callback
        /// </summary>
        public static void DrawAlgorithmSelector(string label, string currentAlgorithm, System.Action<string> onAlgorithmSelected, float labelWidth = 150f)
        {
            string newAlgorithm = DrawAlgorithmSelector(label, currentAlgorithm, labelWidth);
            if (newAlgorithm != currentAlgorithm)
            {
                onAlgorithmSelected?.Invoke(newAlgorithm);
            }
        }
    }
}
