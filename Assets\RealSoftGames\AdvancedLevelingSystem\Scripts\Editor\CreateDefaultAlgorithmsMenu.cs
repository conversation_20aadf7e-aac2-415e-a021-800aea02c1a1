using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Menu items for creating default algorithms
    /// </summary>
    public static class CreateDefaultAlgorithmsMenu
    {
        [MenuItem("Tools/Advanced Leveling System/Create Default Algorithms")]
        public static void CreateDefaultAlgorithms()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty ratings
            DifficultyRating veryEasy = ratingDB.GetRatingByName("Very Easy");
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create algorithms
            var linear = AlgorithmFactory.CreateLinearAlgorithm(easy);
            var exponential = AlgorithmFactory.CreateExponentialAlgorithm(hard);
            var sinusoidal = AlgorithmFactory.CreateSinusoidalAlgorithm(medium);
            var heartbeat = AlgorithmFactory.CreateHeartbeatAlgorithm(medium);
            var zigzag = AlgorithmFactory.CreateZigzagAlgorithm(hard);
            var random = AlgorithmFactory.CreateRandomAlgorithm(veryHard);
            var combined = AlgorithmFactory.CreateCombinedAlgorithm(veryHard);
            var complexWave = AlgorithmFactory.CreateComplexWaveAlgorithm(veryHard);
            var stepped = AlgorithmFactory.CreateSteppedAlgorithm(hard);
            var sawtooth = AlgorithmFactory.CreateSawtoothAlgorithm(medium);
            var tiered = AlgorithmFactory.CreateTieredAlgorithm(hard);
            var staircase = AlgorithmFactory.CreateStaircaseAlgorithm(hard);
            var spiral = AlgorithmFactory.CreateSpiralAlgorithm(veryHard);
            var seasonal = AlgorithmFactory.CreateSeasonalAlgorithm(medium);
            var root = AlgorithmFactory.CreateRootAlgorithm(easy);
            var ripple = AlgorithmFactory.CreateRippleAlgorithm(hard);
            var quantum = AlgorithmFactory.CreateQuantumAlgorithm(veryHard);
            var quadratic = AlgorithmFactory.CreateQuadraticAlgorithm(veryHard);
            var pulsating = AlgorithmFactory.CreatePulsatingAlgorithm(hard);
            var polynomial = AlgorithmFactory.CreatePolynomialAlgorithm(veryHard);
            var plateau = AlgorithmFactory.CreatePlateauAlgorithm(hard);
            var milestone = AlgorithmFactory.CreateMilestoneAlgorithm(medium);
            var microProgression = AlgorithmFactory.CreateMicroProgressionAlgorithm(easy);
            var logistic = AlgorithmFactory.CreateLogisticAlgorithm(medium);
            var logarithmic = AlgorithmFactory.CreateLogarithmicAlgorithm(easy);
            var inverseLogarithmic = AlgorithmFactory.CreateInverseLogarithmicAlgorithm(hard);
            var hyperbolic = AlgorithmFactory.CreateHyperbolicAlgorithm(easy);
            var harmonic = AlgorithmFactory.CreateHarmonicAlgorithm(medium);
            var geometric = AlgorithmFactory.CreateGeometricAlgorithm(hard);
            var fractional = AlgorithmFactory.CreateFractionalAlgorithm(medium);
            var fibonacci = AlgorithmFactory.CreateFibonacciAlgorithm(medium);
            var wave = AlgorithmFactory.CreateWaveAlgorithm(medium);
            var adaptive = AlgorithmFactory.CreateAdaptiveAlgorithm(medium);
            var diminishingReturns = AlgorithmFactory.CreateDiminishingReturnsAlgorithm(easy);
            var earlyBoost = AlgorithmFactory.CreateEarlyBoostAlgorithm(easy);
            var elastic = AlgorithmFactory.CreateElasticAlgorithm(medium);
            var decremental = AlgorithmFactory.CreateDecrementalAlgorithm(easy);
            var decelerating = AlgorithmFactory.CreateDeceleratingAlgorithm(medium);
            var cyclical = AlgorithmFactory.CreateCyclicalAlgorithm(medium);
            var crescendo = AlgorithmFactory.CreateCrescendoAlgorithm(hard);
            var cosine = AlgorithmFactory.CreateCosineAlgorithm(medium);
            var asymptotic = AlgorithmFactory.CreateAsymptoticAlgorithm(medium);
            var burst = AlgorithmFactory.CreateBurstAlgorithm(hard);

            // Create a drawn pattern algorithm
            var drawnPattern = AlgorithmFactory.CreateDrawnPatternAlgorithm(
                "Custom Pattern",
                "Custom pattern created in the Pattern Creator.",
                "Custom drawn pattern",
                medium);

            // Add more points to the drawn pattern
            drawnPattern.points.Clear();
            drawnPattern.points.Add(new Vector2(0.0f, 1.1f));
            drawnPattern.points.Add(new Vector2(0.25f, 1.15f));
            drawnPattern.points.Add(new Vector2(0.5f, 1.2f));
            drawnPattern.points.Add(new Vector2(0.75f, 1.25f));
            drawnPattern.points.Add(new Vector2(1.0f, 1.3f));

            // Save algorithms
            AlgorithmFactory.SaveAlgorithm(linear, Path.Combine(directory, "Linear.asset"));
            AlgorithmFactory.SaveAlgorithm(exponential, Path.Combine(directory, "Exponential.asset"));
            AlgorithmFactory.SaveAlgorithm(sinusoidal, Path.Combine(directory, "Sinusoidal.asset"));
            AlgorithmFactory.SaveAlgorithm(heartbeat, Path.Combine(directory, "Heartbeat.asset"));
            AlgorithmFactory.SaveAlgorithm(zigzag, Path.Combine(directory, "Zigzag.asset"));
            AlgorithmFactory.SaveAlgorithm(random, Path.Combine(directory, "Random.asset"));
            AlgorithmFactory.SaveAlgorithm(combined, Path.Combine(directory, "Combined.asset"));
            AlgorithmFactory.SaveAlgorithm(complexWave, Path.Combine(directory, "ComplexWave.asset"));
            AlgorithmFactory.SaveAlgorithm(stepped, Path.Combine(directory, "Stepped.asset"));
            AlgorithmFactory.SaveAlgorithm(sawtooth, Path.Combine(directory, "Sawtooth.asset"));
            AlgorithmFactory.SaveAlgorithm(tiered, Path.Combine(directory, "Tiered.asset"));
            AlgorithmFactory.SaveAlgorithm(staircase, Path.Combine(directory, "Staircase.asset"));
            AlgorithmFactory.SaveAlgorithm(spiral, Path.Combine(directory, "Spiral.asset"));
            AlgorithmFactory.SaveAlgorithm(seasonal, Path.Combine(directory, "Seasonal.asset"));
            AlgorithmFactory.SaveAlgorithm(root, Path.Combine(directory, "SquareRoot.asset"));
            AlgorithmFactory.SaveAlgorithm(ripple, Path.Combine(directory, "Ripple.asset"));
            AlgorithmFactory.SaveAlgorithm(quantum, Path.Combine(directory, "Quantum.asset"));
            AlgorithmFactory.SaveAlgorithm(quadratic, Path.Combine(directory, "Quadratic.asset"));
            AlgorithmFactory.SaveAlgorithm(pulsating, Path.Combine(directory, "Pulsating.asset"));
            AlgorithmFactory.SaveAlgorithm(polynomial, Path.Combine(directory, "Polynomial.asset"));
            AlgorithmFactory.SaveAlgorithm(plateau, Path.Combine(directory, "Plateau.asset"));
            AlgorithmFactory.SaveAlgorithm(milestone, Path.Combine(directory, "Milestone.asset"));
            AlgorithmFactory.SaveAlgorithm(microProgression, Path.Combine(directory, "MicroProgression.asset"));
            AlgorithmFactory.SaveAlgorithm(logistic, Path.Combine(directory, "Logistic.asset"));
            AlgorithmFactory.SaveAlgorithm(logarithmic, Path.Combine(directory, "Logarithmic.asset"));
            AlgorithmFactory.SaveAlgorithm(inverseLogarithmic, Path.Combine(directory, "InverseLogarithmic.asset"));
            AlgorithmFactory.SaveAlgorithm(hyperbolic, Path.Combine(directory, "Hyperbolic.asset"));
            AlgorithmFactory.SaveAlgorithm(harmonic, Path.Combine(directory, "Harmonic.asset"));
            AlgorithmFactory.SaveAlgorithm(geometric, Path.Combine(directory, "Geometric.asset"));
            AlgorithmFactory.SaveAlgorithm(fractional, Path.Combine(directory, "Fractional.asset"));
            AlgorithmFactory.SaveAlgorithm(fibonacci, Path.Combine(directory, "Fibonacci.asset"));
            AlgorithmFactory.SaveAlgorithm(wave, Path.Combine(directory, "Wave.asset"));
            AlgorithmFactory.SaveAlgorithm(adaptive, Path.Combine(directory, "Adaptive.asset"));
            AlgorithmFactory.SaveAlgorithm(diminishingReturns, Path.Combine(directory, "DiminishingReturns.asset"));
            AlgorithmFactory.SaveAlgorithm(earlyBoost, Path.Combine(directory, "EarlyBoost.asset"));
            AlgorithmFactory.SaveAlgorithm(elastic, Path.Combine(directory, "Elastic.asset"));
            AlgorithmFactory.SaveAlgorithm(decremental, Path.Combine(directory, "Decremental.asset"));
            AlgorithmFactory.SaveAlgorithm(decelerating, Path.Combine(directory, "Decelerating.asset"));
            AlgorithmFactory.SaveAlgorithm(cyclical, Path.Combine(directory, "Cyclical.asset"));
            AlgorithmFactory.SaveAlgorithm(crescendo, Path.Combine(directory, "Crescendo.asset"));
            AlgorithmFactory.SaveAlgorithm(cosine, Path.Combine(directory, "Cosine.asset"));
            AlgorithmFactory.SaveAlgorithm(asymptotic, Path.Combine(directory, "Asymptotic.asset"));
            AlgorithmFactory.SaveAlgorithm(burst, Path.Combine(directory, "Burst.asset"));
            AlgorithmFactory.SaveAlgorithm(drawnPattern, Path.Combine(directory, "CustomPattern.asset"));

            // Show a success message
            EditorUtility.DisplayDialog("Default Algorithms Created",
                "Default algorithms have been created and added to the AlgorithmDatabase.\n\n" +
                "The following algorithms were created:\n" +
                "- Linear\n" +
                "- Exponential\n" +
                "- Sinusoidal\n" +
                "- Heartbeat\n" +
                "- Zigzag\n" +
                "- Random\n" +
                "- Combined\n" +
                "- Complex Wave\n" +
                "- Stepped\n" +
                "- Sawtooth\n" +
                "- Tiered\n" +
                "- Staircase\n" +
                "- Spiral\n" +
                "- Seasonal\n" +
                "- Square Root\n" +
                "- Ripple\n" +
                "- Quantum\n" +
                "- Quadratic\n" +
                "- Pulsating\n" +
                "- Polynomial\n" +
                "- Plateau\n" +
                "- Milestone\n" +
                "- Micro Progression\n" +
                "- Logistic\n" +
                "- Logarithmic\n" +
                "- Inverse Logarithmic\n" +
                "- Hyperbolic\n" +
                "- Harmonic\n" +
                "- Geometric\n" +
                "- Fractional\n" +
                "- Fibonacci\n" +
                "- Wave\n" +
                "- Adaptive\n" +
                "- Diminishing Returns\n" +
                "- Early Boost\n" +
                "- Elastic\n" +
                "- Decremental\n" +
                "- Decelerating\n" +
                "- Cyclical\n" +
                "- Crescendo\n" +
                "- Cosine\n" +
                "- Asymptotic\n" +
                "- Burst\n" +
                "- Custom Pattern\n\n" +
                "You can view and edit these algorithms in the Algorithms tab.",
                "OK");
        }

        [MenuItem("Tools/Advanced Leveling System/Create Algorithm Database")]
        public static void CreateAlgorithmDatabase()
        {
            string resourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
            string databasePath = resourcesPath + "/AlgorithmDatabase.asset";

            // Create the Resources directory if it doesn't exist
            if (!Directory.Exists(resourcesPath))
            {
                Directory.CreateDirectory(resourcesPath);
            }

            // Check if the database already exists
            if (AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(databasePath) != null)
            {
                if (!EditorUtility.DisplayDialog("Overwrite Database?",
                    "An AlgorithmDatabase already exists. Do you want to overwrite it?",
                    "Yes", "No"))
                {
                    Debug.Log("Database creation cancelled by user.");
                    return;
                }
            }

            // Create the database
            AlgorithmDatabase database = ScriptableObject.CreateInstance<AlgorithmDatabase>();

            // Save the database
            AssetDatabase.CreateAsset(database, databasePath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log("Algorithm database created successfully at: " + databasePath);

            // Select the database in the Project window
            Selection.activeObject = database;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Complex Wave Algorithm")]
        public static void CreateComplexWaveAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");
            if (veryHard == null)
            {
                veryHard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the complex wave algorithm
            var complexWave = AlgorithmFactory.CreateComplexWaveAlgorithm(veryHard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "ComplexWave.asset");
            AlgorithmFactory.SaveAlgorithm(complexWave, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(complexWave);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Complex Wave Algorithm Created",
                "The Complex Wave algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = complexWave;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Stepped Algorithm")]
        public static void CreateSteppedAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the stepped algorithm
            var stepped = AlgorithmFactory.CreateSteppedAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Stepped.asset");
            AlgorithmFactory.SaveAlgorithm(stepped, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(stepped);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Stepped Algorithm Created",
                "The Stepped algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = stepped;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Sawtooth Algorithm")]
        public static void CreateSawtoothAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the sawtooth algorithm
            var sawtooth = AlgorithmFactory.CreateSawtoothAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Sawtooth.asset");
            AlgorithmFactory.SaveAlgorithm(sawtooth, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(sawtooth);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Sawtooth Algorithm Created",
                "The Sawtooth algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = sawtooth;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Tiered Algorithm")]
        public static void CreateTieredAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the tiered algorithm
            var tiered = AlgorithmFactory.CreateTieredAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Tiered.asset");
            AlgorithmFactory.SaveAlgorithm(tiered, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(tiered);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Tiered Algorithm Created",
                "The Tiered algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = tiered;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Staircase Algorithm")]
        public static void CreateStaircaseAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the staircase algorithm
            var staircase = AlgorithmFactory.CreateStaircaseAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Staircase.asset");
            AlgorithmFactory.SaveAlgorithm(staircase, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(staircase);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Staircase Algorithm Created",
                "The Staircase algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = staircase;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Spiral Algorithm")]
        public static void CreateSpiralAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");
            if (veryHard == null)
            {
                veryHard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the spiral algorithm
            var spiral = AlgorithmFactory.CreateSpiralAlgorithm(veryHard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Spiral.asset");
            AlgorithmFactory.SaveAlgorithm(spiral, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(spiral);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Spiral Algorithm Created",
                "The Spiral algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = spiral;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Seasonal Algorithm")]
        public static void CreateSeasonalAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the seasonal algorithm
            var seasonal = AlgorithmFactory.CreateSeasonalAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Seasonal.asset");
            AlgorithmFactory.SaveAlgorithm(seasonal, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(seasonal);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Seasonal Algorithm Created",
                "The Seasonal algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = seasonal;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Square Root Algorithm")]
        public static void CreateRootAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the square root algorithm
            var root = AlgorithmFactory.CreateRootAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "SquareRoot.asset");
            AlgorithmFactory.SaveAlgorithm(root, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(root);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Square Root Algorithm Created",
                "The Square Root algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = root;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Ripple Algorithm")]
        public static void CreateRippleAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the ripple algorithm
            var ripple = AlgorithmFactory.CreateRippleAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Ripple.asset");
            AlgorithmFactory.SaveAlgorithm(ripple, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(ripple);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Ripple Algorithm Created",
                "The Ripple algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = ripple;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Quantum Algorithm")]
        public static void CreateQuantumAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");
            if (veryHard == null)
            {
                veryHard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the quantum algorithm
            var quantum = AlgorithmFactory.CreateQuantumAlgorithm(veryHard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Quantum.asset");
            AlgorithmFactory.SaveAlgorithm(quantum, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(quantum);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Quantum Algorithm Created",
                "The Quantum algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = quantum;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Quadratic Algorithm")]
        public static void CreateQuadraticAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");
            if (veryHard == null)
            {
                veryHard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the quadratic algorithm
            var quadratic = AlgorithmFactory.CreateQuadraticAlgorithm(veryHard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Quadratic.asset");
            AlgorithmFactory.SaveAlgorithm(quadratic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(quadratic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Quadratic Algorithm Created",
                "The Quadratic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = quadratic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Pulsating Algorithm")]
        public static void CreatePulsatingAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the pulsating algorithm
            var pulsating = AlgorithmFactory.CreatePulsatingAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Pulsating.asset");
            AlgorithmFactory.SaveAlgorithm(pulsating, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(pulsating);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Pulsating Algorithm Created",
                "The Pulsating algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = pulsating;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Polynomial Algorithm")]
        public static void CreatePolynomialAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating veryHard = ratingDB.GetRatingByName("Very Hard");
            if (veryHard == null)
            {
                veryHard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the polynomial algorithm
            var polynomial = AlgorithmFactory.CreatePolynomialAlgorithm(veryHard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Polynomial.asset");
            AlgorithmFactory.SaveAlgorithm(polynomial, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(polynomial);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Polynomial Algorithm Created",
                "The Polynomial algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = polynomial;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Plateau Algorithm")]
        public static void CreatePlateauAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the plateau algorithm
            var plateau = AlgorithmFactory.CreatePlateauAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Plateau.asset");
            AlgorithmFactory.SaveAlgorithm(plateau, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(plateau);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Plateau Algorithm Created",
                "The Plateau algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = plateau;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Milestone Algorithm")]
        public static void CreateMilestoneAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the milestone algorithm
            var milestone = AlgorithmFactory.CreateMilestoneAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Milestone.asset");
            AlgorithmFactory.SaveAlgorithm(milestone, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(milestone);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Milestone Algorithm Created",
                "The Milestone algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = milestone;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Micro Progression Algorithm")]
        public static void CreateMicroProgressionAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the micro progression algorithm
            var microProgression = AlgorithmFactory.CreateMicroProgressionAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "MicroProgression.asset");
            AlgorithmFactory.SaveAlgorithm(microProgression, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(microProgression);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Micro Progression Algorithm Created",
                "The Micro Progression algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = microProgression;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Logistic Algorithm")]
        public static void CreateLogisticAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the logistic algorithm
            var logistic = AlgorithmFactory.CreateLogisticAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Logistic.asset");
            AlgorithmFactory.SaveAlgorithm(logistic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(logistic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Logistic Algorithm Created",
                "The Logistic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = logistic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Logarithmic Algorithm")]
        public static void CreateLogarithmicAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the logarithmic algorithm
            var logarithmic = AlgorithmFactory.CreateLogarithmicAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Logarithmic.asset");
            AlgorithmFactory.SaveAlgorithm(logarithmic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(logarithmic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Logarithmic Algorithm Created",
                "The Logarithmic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = logarithmic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Inverse Logarithmic Algorithm")]
        public static void CreateInverseLogarithmicAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the inverse logarithmic algorithm
            var inverseLogarithmic = AlgorithmFactory.CreateInverseLogarithmicAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "InverseLogarithmic.asset");
            AlgorithmFactory.SaveAlgorithm(inverseLogarithmic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(inverseLogarithmic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Inverse Logarithmic Algorithm Created",
                "The Inverse Logarithmic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = inverseLogarithmic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Hyperbolic Algorithm")]
        public static void CreateHyperbolicAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the hyperbolic algorithm
            var hyperbolic = AlgorithmFactory.CreateHyperbolicAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Hyperbolic.asset");
            AlgorithmFactory.SaveAlgorithm(hyperbolic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(hyperbolic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Hyperbolic Algorithm Created",
                "The Hyperbolic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = hyperbolic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Harmonic Algorithm")]
        public static void CreateHarmonicAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the harmonic algorithm
            var harmonic = AlgorithmFactory.CreateHarmonicAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Harmonic.asset");
            AlgorithmFactory.SaveAlgorithm(harmonic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(harmonic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Harmonic Algorithm Created",
                "The Harmonic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = harmonic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Geometric Algorithm")]
        public static void CreateGeometricAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the geometric algorithm
            var geometric = AlgorithmFactory.CreateGeometricAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Geometric.asset");
            AlgorithmFactory.SaveAlgorithm(geometric, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(geometric);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Geometric Algorithm Created",
                "The Geometric algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = geometric;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Fractional Algorithm")]
        public static void CreateFractionalAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the fractional algorithm
            var fractional = AlgorithmFactory.CreateFractionalAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Fractional.asset");
            AlgorithmFactory.SaveAlgorithm(fractional, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(fractional);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Fractional Algorithm Created",
                "The Fractional algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = fractional;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Fibonacci Algorithm")]
        public static void CreateFibonacciAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the fibonacci algorithm
            var fibonacci = AlgorithmFactory.CreateFibonacciAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Fibonacci.asset");
            AlgorithmFactory.SaveAlgorithm(fibonacci, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(fibonacci);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Fibonacci Algorithm Created",
                "The Fibonacci algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = fibonacci;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Wave Algorithm")]
        public static void CreateWaveAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the wave algorithm
            var wave = AlgorithmFactory.CreateWaveAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Wave.asset");
            AlgorithmFactory.SaveAlgorithm(wave, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(wave);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Wave Algorithm Created",
                "The Wave algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = wave;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Adaptive Algorithm")]
        public static void CreateAdaptiveAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the adaptive algorithm
            var adaptive = AlgorithmFactory.CreateAdaptiveAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Adaptive.asset");
            AlgorithmFactory.SaveAlgorithm(adaptive, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(adaptive);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Adaptive Algorithm Created",
                "The Adaptive algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = adaptive;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Diminishing Returns Algorithm")]
        public static void CreateDiminishingReturnsAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the diminishing returns algorithm
            var diminishingReturns = AlgorithmFactory.CreateDiminishingReturnsAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "DiminishingReturns.asset");
            AlgorithmFactory.SaveAlgorithm(diminishingReturns, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(diminishingReturns);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Diminishing Returns Algorithm Created",
                "The Diminishing Returns algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = diminishingReturns;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Early Boost Algorithm")]
        public static void CreateEarlyBoostAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the early boost algorithm
            var earlyBoost = AlgorithmFactory.CreateEarlyBoostAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "EarlyBoost.asset");
            AlgorithmFactory.SaveAlgorithm(earlyBoost, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(earlyBoost);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Early Boost Algorithm Created",
                "The Early Boost algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = earlyBoost;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Elastic Algorithm")]
        public static void CreateElasticAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the elastic algorithm
            var elastic = AlgorithmFactory.CreateElasticAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Elastic.asset");
            AlgorithmFactory.SaveAlgorithm(elastic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(elastic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Elastic Algorithm Created",
                "The Elastic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = elastic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Decremental Algorithm")]
        public static void CreateDecrementalAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating easy = ratingDB.GetRatingByName("Easy");
            if (easy == null)
            {
                easy = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the decremental algorithm
            var decremental = AlgorithmFactory.CreateDecrementalAlgorithm(easy);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Decremental.asset");
            AlgorithmFactory.SaveAlgorithm(decremental, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(decremental);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Decremental Algorithm Created",
                "The Decremental algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = decremental;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Decelerating Algorithm")]
        public static void CreateDeceleratingAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the decelerating algorithm
            var decelerating = AlgorithmFactory.CreateDeceleratingAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Decelerating.asset");
            AlgorithmFactory.SaveAlgorithm(decelerating, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(decelerating);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Decelerating Algorithm Created",
                "The Decelerating algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = decelerating;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Cyclical Algorithm")]
        public static void CreateCyclicalAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the cyclical algorithm
            var cyclical = AlgorithmFactory.CreateCyclicalAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Cyclical.asset");
            AlgorithmFactory.SaveAlgorithm(cyclical, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(cyclical);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Cyclical Algorithm Created",
                "The Cyclical algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = cyclical;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Crescendo Algorithm")]
        public static void CreateCrescendoAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the crescendo algorithm
            var crescendo = AlgorithmFactory.CreateCrescendoAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Crescendo.asset");
            AlgorithmFactory.SaveAlgorithm(crescendo, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(crescendo);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Crescendo Algorithm Created",
                "The Crescendo algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = crescendo;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Cosine Algorithm")]
        public static void CreateCosineAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the cosine algorithm
            var cosine = AlgorithmFactory.CreateCosineAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Cosine.asset");
            AlgorithmFactory.SaveAlgorithm(cosine, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(cosine);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Cosine Algorithm Created",
                "The Cosine algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = cosine;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Asymptotic Algorithm")]
        public static void CreateAsymptoticAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating medium = ratingDB.GetRatingByName("Medium");
            if (medium == null)
            {
                medium = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the asymptotic algorithm
            var asymptotic = AlgorithmFactory.CreateAsymptoticAlgorithm(medium);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Asymptotic.asset");
            AlgorithmFactory.SaveAlgorithm(asymptotic, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(asymptotic);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Asymptotic Algorithm Created",
                "The Asymptotic algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = asymptotic;
        }

        [MenuItem("Tools/Advanced Leveling System/Create Burst Algorithm")]
        public static void CreateBurstAlgorithm()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Please create the default ratings first.");
                return;
            }

            // Get difficulty rating
            DifficultyRating hard = ratingDB.GetRatingByName("Hard");
            if (hard == null)
            {
                hard = ratingDB.difficultyRatings.Count > 0 ? ratingDB.difficultyRatings[0] : null;
            }

            // Create the directory if it doesn't exist
            string directory = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create the burst algorithm
            var burst = AlgorithmFactory.CreateBurstAlgorithm(hard);

            // Save the algorithm
            string assetPath = Path.Combine(directory, "Burst.asset");
            AlgorithmFactory.SaveAlgorithm(burst, assetPath);

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (database == null)
            {
                Debug.LogError("AlgorithmDatabase not found. Please create it first.");
                return;
            }

            // Add the algorithm to the database
            database.AddAlgorithm(burst);

            // Save the database
            EditorUtility.SetDirty(database);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Force refresh the algorithm registry
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            // Show a success message
            EditorUtility.DisplayDialog("Burst Algorithm Created",
                "The Burst algorithm has been created and added to the AlgorithmDatabase.\n\n" +
                "You can view and edit this algorithm in the Algorithms tab.",
                "OK");

            // Select the algorithm in the Project window
            Selection.activeObject = burst;
        }
    }
}
