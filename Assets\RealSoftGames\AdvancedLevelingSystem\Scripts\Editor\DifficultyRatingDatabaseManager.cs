using UnityEngine;
using UnityEditor;
using System;
using System.IO;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Manages the creation and initialization of the DifficultyRatingDatabase.
    /// This class automatically creates the database if it doesn't exist when Unity starts.
    /// </summary>
    [InitializeOnLoad]
    public class DifficultyRatingDatabaseManager
    {
        private const string ResourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
        private const string DatabasePath = ResourcesPath + "/DifficultyRatingDatabase.asset";

        /// <summary>
        /// Static constructor that runs when Unity starts or when scripts are recompiled.
        /// Checks if the database exists and creates it if needed.
        /// </summary>
        static DifficultyRatingDatabaseManager()
        {
            try
            {
                // Check if the database exists
                DifficultyRatingDatabase database = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
                if (database == null)
                {
                    // Create the database
                    CreateDatabase();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error initializing DifficultyRatingDatabaseManager: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Creates the DifficultyRatingDatabase with default ratings.
        /// </summary>
        // No longer exposed as a menu item - accessed through the Settings tab
        public static void CreateDatabase()
        {
            try
            {
                // Create the Resources directory if it doesn't exist
                if (!Directory.Exists(ResourcesPath))
                {
                    Directory.CreateDirectory(ResourcesPath);
                }

                // Check if the database already exists
                if (AssetDatabase.LoadAssetAtPath<DifficultyRatingDatabase>(DatabasePath) != null)
                {
                    if (!EditorUtility.DisplayDialog("Overwrite Database?",
                        "A DifficultyRatingDatabase already exists. Do you want to overwrite it?",
                        "Yes", "No"))
                    {
                        Debug.Log("Database creation cancelled by user.");
                        return;
                    }

                    // Delete the existing database
                    AssetDatabase.DeleteAsset(DatabasePath);
                    AssetDatabase.Refresh();
                }

                // Create the database
                DifficultyRatingDatabase database = ScriptableObject.CreateInstance<DifficultyRatingDatabase>();

                // First, save the database asset
                AssetDatabase.CreateAsset(database, DatabasePath);

                // Now create and add the ratings
                CreateRating(database, "Very Easy", new Color(0.0f, 0.8f, 0.0f), 1, "Extremely gentle progression suitable for casual games.");
                CreateRating(database, "Easy", new Color(0.5f, 0.8f, 0.0f), 2, "Smooth progression with moderate challenges.");
                CreateRating(database, "Medium", new Color(0.8f, 0.8f, 0.0f), 3, "Balanced progression with regular challenges.");
                CreateRating(database, "Hard", new Color(0.8f, 0.4f, 0.0f), 4, "Steep progression requiring dedication and skill.");
                CreateRating(database, "Very Hard", new Color(0.8f, 0.0f, 0.0f), 5, "Extremely challenging progression for dedicated players.");
                CreateRating(database, "Extreme", new Color(0.5f, 0.0f, 0.5f), 5, "Brutal progression designed for the most hardcore players.");

                // Save all the created assets
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Debug.Log("Difficulty rating database created successfully!");

                // Select the database in the Project window
                Selection.activeObject = database;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error creating difficulty rating database: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Creates a DifficultyRating and adds it to the database.
        /// </summary>
        /// <param name="database">The database to add the rating to</param>
        /// <param name="name">The name of the rating</param>
        /// <param name="color">The color associated with the rating</param>
        /// <param name="stars">The number of stars (1-5) for the rating</param>
        /// <param name="desc">The description of the rating</param>
        private static void CreateRating(DifficultyRatingDatabase database, string name, Color color, int stars, string desc)
        {
            try
            {
                // Create the rating
                DifficultyRating rating = ScriptableObject.CreateInstance<DifficultyRating>();
                rating.name = name; // Set the asset name

                // Use SerializedObject to set the properties
                SerializedObject serializedObject = new SerializedObject(rating);

                // Set the properties
                serializedObject.FindProperty("ratingName").stringValue = name;
                serializedObject.FindProperty("ratingColor").colorValue = color;
                serializedObject.FindProperty("stars").intValue = stars;
                serializedObject.FindProperty("description").stringValue = desc;

                // Apply the changes
                serializedObject.ApplyModifiedProperties();

                // Add it to the database
                database.difficultyRatings.Add(rating);

                // Save it as a sub-asset of the database
                AssetDatabase.AddObjectToAsset(rating, database);
                EditorUtility.SetDirty(rating);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error creating difficulty rating '{name}': {ex.Message}\n{ex.StackTrace}");
            }
        }
    }
}
