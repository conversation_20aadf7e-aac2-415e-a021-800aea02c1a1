using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Custom editor for the LevelingAlgorithm class
    /// </summary>
    [CustomEditor(typeof(LevelingAlgorithmBase))]
    public class LevelingAlgorithmEditor : UnityEditor.Editor
    {
        // References
        private LevelingAlgorithmBase algorithm;

        // UI state
        private bool showBasicProperties = true;
        private bool showAlgorithmSpecificProperties = true;
        private bool showPreview = true;

        // Preview settings
        private int startingExperience = 250;
        private int startingLevel = 1;
        private int maxLevel = 50;
        private float levelUpMultiplier = 1.1f;

        // Preview graph
        private LevelingCurvePreview curvePreview;

        private void OnEnable()
        {
            // Get references
            algorithm = (LevelingAlgorithmBase)target;

            // Create curve preview
            curvePreview = new LevelingCurvePreview();
        }

        public override void OnInspectorGUI()
        {
            // Update references in case they changed
            algorithm = (LevelingAlgorithmBase)target;

            // Begin change check
            EditorGUI.BeginChangeCheck();

            // Draw basic properties
            DrawBasicProperties();

            // Draw algorithm type selector
            DrawAlgorithmTypeSelector();

            // Draw algorithm-specific properties
            DrawAlgorithmSpecificProperties();

            // Draw preview
            DrawPreview();

            // Apply changes if modified
            if (EditorGUI.EndChangeCheck())
            {
                // Mark as dirty
                EditorUtility.SetDirty(algorithm);

                // Force recalculation
                algorithm.ClearCachedValues();
                algorithm.PreCalculatePoints(startingExperience, startingLevel, maxLevel, levelUpMultiplier);
            }
        }

        /// <summary>
        /// Draws the basic properties section
        /// </summary>
        private void DrawBasicProperties()
        {
            showBasicProperties = EditorGUILayout.Foldout(showBasicProperties, "Basic Properties", true);
            if (showBasicProperties)
            {
                EditorGUI.indentLevel++;

                // Algorithm name
                algorithm.algorithmName = EditorGUILayout.TextField("Name", algorithm.algorithmName);

                // Description
                EditorGUILayout.LabelField("Description");
                algorithm.description = EditorGUILayout.TextArea(algorithm.description, GUILayout.Height(60));

                // Formula explanation
                EditorGUILayout.LabelField("Formula Explanation");
                algorithm.formulaExplanation = EditorGUILayout.TextArea(algorithm.formulaExplanation, GUILayout.Height(60));

                // Difficulty rating
                algorithm.difficultyRating = (DifficultyRating)EditorGUILayout.ObjectField(
                    "Difficulty Rating",
                    algorithm.difficultyRating,
                    typeof(DifficultyRating),
                    false);

                EditorGUI.indentLevel--;
            }
        }

        /// <summary>
        /// Draws the algorithm type selector
        /// </summary>
        private void DrawAlgorithmTypeSelector()
        {
            EditorGUILayout.Space(10);
            EditorGUILayout.BeginHorizontal();

            EditorGUILayout.LabelField("Algorithm Type", GUILayout.Width(120));

            // Store the original algorithm type
            AlgorithmEnums.AlgorithmType originalType = algorithm.algorithmType;

            // Draw the algorithm type selector
            algorithm.algorithmType = (AlgorithmEnums.AlgorithmType)EditorGUILayout.EnumPopup(algorithm.algorithmType);

            // If the algorithm type changed, initialize default values
            if (originalType != algorithm.algorithmType)
            {
                if (algorithm.algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
                {
                    // Initialize drawn pattern properties
                    if (algorithm.points.Count < 2)
                    {
                        algorithm.points.Clear();
                        algorithm.points.Add(new Vector2(0.0f, 1.1f));
                        algorithm.points.Add(new Vector2(1.0f, 1.3f));
                    }
                }
                else
                {
                    // Initialize coded formula properties
                    algorithm.baseMultiplier = 1.1f;
                    algorithm.levelMultiplierFactor = 0.01f;
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        /// <summary>
        /// Draws the algorithm-specific properties section
        /// </summary>
        private void DrawAlgorithmSpecificProperties()
        {
            showAlgorithmSpecificProperties = EditorGUILayout.Foldout(showAlgorithmSpecificProperties, "Algorithm Properties", true);
            if (showAlgorithmSpecificProperties)
            {
                EditorGUI.indentLevel++;

                if (algorithm.algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
                {
                    DrawDrawnPatternProperties();
                }
                else
                {
                    DrawCodedFormulaProperties();
                }

                EditorGUI.indentLevel--;
            }
        }

        /// <summary>
        /// Draws properties specific to drawn pattern algorithms
        /// </summary>
        private void DrawDrawnPatternProperties()
        {
            // Interpolation method
            algorithm.interpolationMethod = (AlgorithmEnums.InterpolationMethod)EditorGUILayout.EnumPopup(
                "Interpolation Method",
                algorithm.interpolationMethod);

            // Points
            EditorGUILayout.LabelField("Points", EditorStyles.boldLabel);

            // Display points in a scrollable area
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            for (int i = 0; i < algorithm.points.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();

                // Point index
                EditorGUILayout.LabelField($"Point {i}:", GUILayout.Width(60));

                // X value (0-1)
                float x = EditorGUILayout.Slider(algorithm.points[i].x, 0f, 1f, GUILayout.Width(150));

                // Y value
                float y = EditorGUILayout.FloatField(algorithm.points[i].y, GUILayout.Width(60));

                // Update point
                algorithm.points[i] = new Vector2(x, y);

                // Remove button
                if (GUILayout.Button("X", GUILayout.Width(25)))
                {
                    algorithm.points.RemoveAt(i);
                    i--;
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add point button
            if (GUILayout.Button("Add Point"))
            {
                // Add a new point at the end
                float x = algorithm.points.Count > 0 ?
                    Mathf.Min(1f, algorithm.points[algorithm.points.Count - 1].x + 0.1f) :
                    0f;

                float y = 1.1f;
                algorithm.points.Add(new Vector2(x, y));
            }

            // Sort points button
            if (GUILayout.Button("Sort Points"))
            {
                algorithm.points.Sort((a, b) => a.x.CompareTo(b.x));
            }

            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draws properties specific to coded formula algorithms
        /// </summary>
        private void DrawCodedFormulaProperties()
        {
            // Base multiplier
            algorithm.baseMultiplier = EditorGUILayout.FloatField("Base Multiplier", algorithm.baseMultiplier);

            // Level multiplier factor
            algorithm.levelMultiplierFactor = EditorGUILayout.FloatField("Level Multiplier Factor", algorithm.levelMultiplierFactor);

            // Sine wave
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Sine Wave", EditorStyles.boldLabel);
            algorithm.useSineWave = EditorGUILayout.Toggle("Use Sine Wave", algorithm.useSineWave);
            if (algorithm.useSineWave)
            {
                EditorGUI.indentLevel++;
                algorithm.sineAmplitude = EditorGUILayout.FloatField("Amplitude", algorithm.sineAmplitude);
                algorithm.sinePeriod = EditorGUILayout.FloatField("Period (levels)", algorithm.sinePeriod);
                EditorGUI.indentLevel--;
            }

            // Heartbeat
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Heartbeat", EditorStyles.boldLabel);
            algorithm.useHeartbeat = EditorGUILayout.Toggle("Use Heartbeat", algorithm.useHeartbeat);
            if (algorithm.useHeartbeat)
            {
                EditorGUI.indentLevel++;
                algorithm.heartbeatAmplitude = EditorGUILayout.FloatField("Amplitude", algorithm.heartbeatAmplitude);
                algorithm.heartbeatPeriod = EditorGUILayout.FloatField("Period (levels)", algorithm.heartbeatPeriod);
                EditorGUI.indentLevel--;
            }

            // Zigzag
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Zigzag", EditorStyles.boldLabel);
            algorithm.useZigzag = EditorGUILayout.Toggle("Use Zigzag", algorithm.useZigzag);
            if (algorithm.useZigzag)
            {
                EditorGUI.indentLevel++;
                algorithm.zigzagAmplitude = EditorGUILayout.FloatField("Amplitude", algorithm.zigzagAmplitude);
                algorithm.zigzagPeriod = EditorGUILayout.FloatField("Period (levels)", algorithm.zigzagPeriod);
                EditorGUI.indentLevel--;
            }

            // Random fluctuations
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Random Fluctuations", EditorStyles.boldLabel);
            algorithm.useRandomFluctuations = EditorGUILayout.Toggle("Use Random Fluctuations", algorithm.useRandomFluctuations);
            if (algorithm.useRandomFluctuations)
            {
                EditorGUI.indentLevel++;
                algorithm.randomAmplitude = EditorGUILayout.FloatField("Amplitude", algorithm.randomAmplitude);
                algorithm.randomSeed = EditorGUILayout.IntField("Random Seed", algorithm.randomSeed);
                EditorGUI.indentLevel--;
            }
        }

        /// <summary>
        /// Draws the preview section
        /// </summary>
        private void DrawPreview()
        {
            showPreview = EditorGUILayout.Foldout(showPreview, "Preview", true);
            if (showPreview)
            {
                EditorGUI.indentLevel++;

                // Preview settings
                EditorGUILayout.LabelField("Preview Settings", EditorStyles.boldLabel);
                startingExperience = EditorGUILayout.IntField("Starting Experience", startingExperience);
                startingLevel = EditorGUILayout.IntField("Starting Level", startingLevel);
                maxLevel = EditorGUILayout.IntField("Max Level", maxLevel);
                levelUpMultiplier = EditorGUILayout.FloatField("Level Up Multiplier", levelUpMultiplier);

                // Draw the preview
                EditorGUILayout.Space();

                // Ensure curve preview is initialized
                if (curvePreview == null)
                {
                    curvePreview = new LevelingCurvePreview();
                }

                // Ensure algorithm is valid before drawing
                if (algorithm != null)
                {
                    // Validate algorithm data
                    algorithm.Validate();

                    try
                    {
                        curvePreview.DrawCurvePreview(algorithm, levelUpMultiplier, startingExperience, startingLevel, maxLevel);
                    }
                    catch (System.Exception e)
                    {
                        EditorGUILayout.HelpBox($"Error drawing preview: {e.Message}", MessageType.Error);
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("No algorithm selected", MessageType.Warning);
                }

                EditorGUI.indentLevel--;
            }
        }
    }
}
