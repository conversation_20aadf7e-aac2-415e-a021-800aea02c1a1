using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using RealSoftGames.AdvancedLevelingSystem;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Curve preview tool for visualizing leveling curves in the Unity Editor.
    /// This class uses the generic Graph class from RealSoftGames namespace.
    /// </summary>
    public class LevelingCurvePreview
    {
        private RealSoftGames.Graph graph;
        private int maxPreviewLevels = 30; // Default value, can be changed

        /// <summary>
        /// Gets or sets the graph size as a ratio of available space (width, height)
        /// </summary>
        public Vector2 GraphSize
        {
            get { return graph.GraphSize; }
            set
            {
                // If height is 0, we're trying to hide this graph
                if (value.y <= 0.001f)
                {
                    // Set a minimal height to avoid errors but make it effectively invisible
                    graph.GraphSize = new Vector2(value.x, 0.001f);
                    graph.SkipDrawing = true;
                }
                else
                {
                    graph.GraphSize = value;
                    graph.SkipDrawing = false;
                }
            }
        }

        /// <summary>
        /// Gets or sets the padding inside the graph in pixels
        /// </summary>
        public int GraphPadding
        {
            get { return graph.GraphPadding; }
            set { graph.GraphPadding = value; }
        }

        /// <summary>
        /// Gets or sets the space for bottom labels in pixels
        /// </summary>
        public int BottomLabelSpace
        {
            get { return graph.BottomLabelSpace; }
            set { graph.BottomLabelSpace = value; }
        }

        /// <summary>
        /// Gets or sets the space for left labels in pixels
        /// </summary>
        public int LeftLabelSpace
        {
            get { return graph.LeftLabelSpace; }
            set { graph.LeftLabelSpace = value; }
        }

        /// <summary>
        /// Gets or sets the horizontal margin on each side of the graph in pixels
        /// </summary>
        public int HorizontalMargin
        {
            get { return graph.HorizontalMargin; }
            set { graph.HorizontalMargin = value; }
        }

        /// <summary>
        /// Gets or sets the maximum number of levels to preview in the graph
        /// </summary>
        public int MaxPreviewLevels
        {
            get { return maxPreviewLevels; }
            set { maxPreviewLevels = Mathf.Max(5, value); } // Ensure at least 5 levels are shown
        }

        /// <summary>
        /// Gets whether graph zooming (logarithmic scaling) is enabled
        /// </summary>
        public bool EnableGraphZooming
        {
            get { return graph.EnableGraphZooming; }
            set
            {
                graph.EnableGraphZooming = value;
                // Also update the EditorPrefs value to ensure consistency
                IsGraphZoomingEnabled = value;
            }
        }

        /// <summary>
        /// Gets or sets whether to show raw curve values instead of actual values
        /// </summary>
        public bool ShowRawCurveValues
        {
            get { return graph.ShowRawCurveValues; }
            set
            {
                graph.ShowRawCurveValues = value;
                // Also update the EditorPrefs value to ensure consistency
                SetShowRawCurveValues(value);
            }
        }

        /// <summary>
        /// Gets whether to show raw curve values (based on levels) instead of experience values (static version)
        /// </summary>
        public static bool GetShowRawCurveValues()
        {
            return EditorPrefs.GetBool("RealSoftGames.AdvancedLevelingSystem.ShowRawCurveValues", false);
        }

        /// <summary>
        /// Sets whether to show raw curve values (based on levels) instead of experience values (static version)
        /// </summary>
        public static void SetShowRawCurveValues(bool value)
        {
            EditorPrefs.SetBool("RealSoftGames.AdvancedLevelingSystem.ShowRawCurveValues", value);
        }

        /// <summary>
        /// Gets whether graph zooming (logarithmic scaling) is enabled in the settings
        /// </summary>
        public static bool IsGraphZoomingEnabled
        {
            get { return EditorPrefs.GetBool("RealSoftGames.AdvancedLevelingSystem.EnableGraphZooming", true); }
            set { EditorPrefs.SetBool("RealSoftGames.AdvancedLevelingSystem.EnableGraphZooming", value); }
        }

        /// <summary>
        /// Creates a new instance of the LevelingCurvePreview class
        /// </summary>
        public LevelingCurvePreview()
        {
            graph = new RealSoftGames.Graph();
            graph.GraphSize = new Vector2(0.95f, 0.35f); // Reduced width to avoid horizontal scrollbar
            graph.CustomXAxisLabel = "Level";
            graph.CustomYAxisLabel = "Experience";
            graph.BottomLabelSpace = 75; // Ensure enough space for bottom labels
        }

        /// <summary>
        /// Draws a curve preview for the specified leveling algorithm scriptable object
        /// </summary>
        /// <param name="algorithm">The primary algorithm scriptable object to display</param>
        /// <param name="levelUpMultiplier">The level up multiplier to use</param>
        /// <param name="startingExperience">Starting experience value</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="availableWidth">Optional custom width for the graph</param>
        /// <param name="experienceTitle">Optional custom title for the experience requirements graph</param>
        /// <param name="multiplierTitle">Optional custom title for the level-up multiplier graph</param>
        public void DrawCurvePreview(LevelingAlgorithmBase algorithm, float levelUpMultiplier,
            int startingExperience = 250, int startingLevel = 1, int maxLevel = 30, float? availableWidth = null,
            string experienceTitle = null, string multiplierTitle = null)
        {
            // Log the algorithm we're drawing (commented out to prevent console spam)
            // Debug.Log($"LevelingCurvePreview: Drawing curve for scriptable algorithm: {algorithm.Name} with multiplier: {levelUpMultiplier}");

            // Update the maxPreviewLevels based on the provided maxLevel
            MaxPreviewLevels = maxLevel;

            // Update the graph's global settings
            graph.LevelUpMultiplier = levelUpMultiplier;
            graph.StartingExperience = startingExperience;
            graph.StartingLevel = startingLevel;
            graph.MaxLevel = maxLevel;
            graph.MaxPreviewPoints = maxLevel;
            graph.EnableGraphZooming = IsGraphZoomingEnabled;

            // Force algorithm to recalculate its values
            algorithm.ClearCachedValues();

            // Create a second graph instance for the raw formula curve
            RealSoftGames.Graph rawFormulaGraph = new RealSoftGames.Graph();
            rawFormulaGraph.GraphSize = new Vector2(0.95f, graph.GraphSize.y * 0.95f); // Reduced width to avoid horizontal scrollbar
            rawFormulaGraph.LevelUpMultiplier = levelUpMultiplier;
            rawFormulaGraph.StartingExperience = startingExperience;
            rawFormulaGraph.StartingLevel = startingLevel;
            rawFormulaGraph.MaxLevel = maxLevel;
            rawFormulaGraph.MaxPreviewPoints = maxLevel;
            rawFormulaGraph.EnableGraphZooming = IsGraphZoomingEnabled;
            rawFormulaGraph.ShowRawCurveValues = true; // Always show raw values for this graph
            rawFormulaGraph.BottomLabelSpace = 75; // Ensure enough space for bottom labels
            rawFormulaGraph.CustomXAxisLabel = "Level"; // Ensure X-axis label is set correctly

            // Add a section title for the experience requirements graph
            string defaultExperienceTitle = "Experience Requirements by Level";
            string displayExperienceTitle = experienceTitle ?? defaultExperienceTitle;
            //EditorGUILayout.LabelField(displayExperienceTitle, EditorStyles.boldLabel);
            EditorGUILayout.Space(15);

            // Set the title for the graph
            graph.Title = displayExperienceTitle;

            // Calculate experience requirements
            List<float> experienceValues = new List<float>();

            // Calculate the full experience requirements
            int currentRequirement = startingExperience;
            experienceValues.Add(currentRequirement);

            for (int level = startingLevel; level < maxLevel; level++)
            {
                int nextRequirement = algorithm.CalculateNextRequirement(
                    currentRequirement,
                    level,
                    levelUpMultiplier,
                    startingLevel,
                    maxLevel);

                // Ensure we have a valid value (greater than previous)
                if (nextRequirement <= currentRequirement)
                {
                    // If the formula produced an invalid value, use a fallback
                    nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                }

                experienceValues.Add(nextRequirement);
                currentRequirement = nextRequirement;
            }

            if (experienceValues.Count == 0)
                return;

            // Create X-axis labels (levels)
            List<string> xAxisLabels = new List<string>();
            for (int i = 0; i < experienceValues.Count; i++)
            {
                xAxisLabels.Add((startingLevel + i).ToString());
            }

            // Draw the experience requirements graph
            graph.DrawLineGraph(
                experienceValues,
                "", // Empty title since we're using CustomTitle
                "Level",
                "Experience",
                xAxisLabels,
                availableWidth,
                138f); // Increased height by another 20% from 115f

            // Add some space between the graphs
            EditorGUILayout.Space(15);

            // Add a section title for the raw formula curve
            string defaultMultiplierTitle = "Level-Up Multiplier by Level";
            string displayMultiplierTitle = multiplierTitle ?? defaultMultiplierTitle;
            //EditorGUILayout.LabelField(displayMultiplierTitle, EditorStyles.boldLabel);
            EditorGUILayout.Space(15);

            // Set the title for the raw formula graph
            rawFormulaGraph.Title = displayMultiplierTitle;

            // Calculate raw formula values
            List<float> rawFormulaValues = algorithm.CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);

            // Draw the raw formula curve
            rawFormulaGraph.DrawLineGraph(
                rawFormulaValues,
                "", // Empty title since we're using CustomTitle
                "Level",
                "Multiplier",
                xAxisLabels,
                availableWidth,
                96f); // Increased height by 20% from 80f
        }

        /// <summary>
        /// Draws a curve preview for the specified leveling algorithm
        /// </summary>
        /// <param name="algorithm">The primary algorithm to display</param>
        /// <param name="levelUpMultiplier">The level up multiplier to use</param>
        /// <param name="comparisonAlgorithm">Optional comparison algorithm</param>
        /// <param name="startingExperience">Starting experience value</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <param name="availableWidth">Optional custom width for the graph</param>
        /// <param name="experienceTitle">Optional custom title for the experience requirements graph</param>
        /// <param name="multiplierTitle">Optional custom title for the level-up multiplier graph</param>
        public void DrawCurvePreview(ILevelingAlgorithm algorithm, float levelUpMultiplier, ILevelingAlgorithm comparisonAlgorithm = null,
            int startingExperience = 250, int startingLevel = 1, int maxLevel = 30, float? availableWidth = null,
            string experienceTitle = null, string multiplierTitle = null)
        {
            // Update the maxPreviewLevels based on the provided maxLevel
            MaxPreviewLevels = maxLevel;

            // Update the graph's global settings
            graph.LevelUpMultiplier = levelUpMultiplier;
            graph.StartingExperience = startingExperience;
            graph.StartingLevel = startingLevel;
            graph.MaxLevel = maxLevel;
            graph.MaxPreviewPoints = maxLevel;
            graph.EnableGraphZooming = IsGraphZoomingEnabled;

            // Set the current algorithm on the graph for proper handling of SineWave
            graph.CurrentAlgorithm = algorithm;

            // Force algorithm to recalculate its values if it's a scriptable object
            if (algorithm is LevelingAlgorithmBase scriptableAlgorithm)
            {
                // Clear cached values to ensure fresh calculation
                scriptableAlgorithm.ClearCachedValues();

                // Force pre-calculation with the current parameters
                scriptableAlgorithm.PreCalculatePoints(startingExperience, startingLevel, maxLevel, levelUpMultiplier);
            }

            // Create a second graph instance for the raw formula curve
            RealSoftGames.Graph rawFormulaGraph = new RealSoftGames.Graph();
            rawFormulaGraph.GraphSize = new Vector2(0.95f, graph.GraphSize.y * 0.95f); // Reduced width to avoid horizontal scrollbar
            rawFormulaGraph.LevelUpMultiplier = levelUpMultiplier;
            rawFormulaGraph.StartingExperience = startingExperience;
            rawFormulaGraph.StartingLevel = startingLevel;
            rawFormulaGraph.MaxLevel = maxLevel;
            rawFormulaGraph.MaxPreviewPoints = maxLevel;
            rawFormulaGraph.EnableGraphZooming = IsGraphZoomingEnabled;
            rawFormulaGraph.ShowRawCurveValues = true; // Always show raw values for this graph
            rawFormulaGraph.CurrentAlgorithm = algorithm; // Set the current algorithm for the raw formula graph too
            rawFormulaGraph.BottomLabelSpace = 75; // Ensure enough space for bottom labels
            rawFormulaGraph.CustomXAxisLabel = "Level"; // Ensure X-axis label is set correctly

            // Add a section title for the experience requirements graph
            string defaultExperienceTitle = "Experience Requirements by Level";
            string displayExperienceTitle = experienceTitle ?? defaultExperienceTitle;
            EditorGUILayout.LabelField(displayExperienceTitle, EditorStyles.boldLabel);
            EditorGUILayout.Space(5);

            // Set the title for the graph
            graph.Title = displayExperienceTitle;

            // Always calculate fresh requirement curve points instead of using cached values
            // This ensures we get accurate, algorithm-specific values

            // Calculate the experience requirements directly
            List<int> requirementCurve = algorithm.CalculateRequirementCurve(startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            // Convert to Vector2 points for the graph
            List<Vector2> requirementCurvePoints = new List<Vector2>();
            for (int i = 0; i < requirementCurve.Count; i++)
            {
                int level = startingLevel + i;
                requirementCurvePoints.Add(new Vector2(level, requirementCurve[i]));
            }

            List<float> experienceValues = new List<float>();
            List<float> comparisonExperienceValues = null;

            if (requirementCurvePoints.Count == 0)
            {
                EditorGUILayout.HelpBox($"Failed to calculate requirement curve for {algorithm.Name}", MessageType.Error);
                return;
            }

            // Extract the y values from the Vector2 points
            foreach (var point in requirementCurvePoints)
            {
                experienceValues.Add(point.y);
            }

            // Get comparison requirements if provided
            if (comparisonAlgorithm != null)
            {
                // Calculate the comparison algorithm's experience requirements directly
                // This ensures we get fresh values that match the current parameters
                comparisonExperienceValues = new List<float>();
                int currentRequirement = startingExperience;

                // Add the starting experience as the first point
                comparisonExperienceValues.Add(currentRequirement);

                // Calculate each level's requirement
                for (int level = startingLevel; level < maxLevel; level++)
                {
                    int nextRequirement = comparisonAlgorithm.CalculateNextRequirement(
                        currentRequirement,
                        level,
                        levelUpMultiplier,
                        startingLevel,
                        maxLevel);

                    // Ensure we have a valid value (greater than previous)
                    if (nextRequirement <= currentRequirement)
                    {
                        // If the formula produced an invalid value, use a fallback
                        nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                    }

                    comparisonExperienceValues.Add(nextRequirement);
                    currentRequirement = nextRequirement;
                }

                // Debug log to verify we have comparison values (commented out to prevent console spam)
                // Debug.Log($"Comparison algorithm '{comparisonAlgorithm.Name}' has {comparisonExperienceValues.Count} experience values");
            }

            // Create X-axis labels (levels)
            List<string> xAxisLabels = new List<string>();
            for (int i = 0; i < experienceValues.Count; i++)
            {
                xAxisLabels.Add((startingLevel + i).ToString());
            }

            // Draw the experience requirements graph
            if (comparisonExperienceValues != null && comparisonAlgorithm != null)
            {
                graph.DrawComparisonLineGraph(
                    experienceValues,
                    comparisonExperienceValues,
                    "", // Empty title since we're using CustomTitle
                    "Level",
                    "Experience",
                    algorithm.Name,
                    comparisonAlgorithm.Name,
                    xAxisLabels,
                    availableWidth,
                    150f); // Increased height from 138f to 150f for better spacing
            }
            else
            {
                graph.DrawLineGraph(
                    experienceValues,
                    "", // Empty title since we're using CustomTitle
                    "Level",
                    "Experience",
                    xAxisLabels,
                    availableWidth,
                    150f); // Increased height from 138f to 150f for better spacing
            }

            // Add some space between the graphs
            EditorGUILayout.Space(15);

            // Add a section title for the raw formula curve
            string defaultMultiplierTitle = "Level-Up Multiplier by Level";
            string displayMultiplierTitle = multiplierTitle ?? defaultMultiplierTitle;
            EditorGUILayout.LabelField(displayMultiplierTitle, EditorStyles.boldLabel);
            EditorGUILayout.Space(5);

            // Set the title for the raw formula graph
            rawFormulaGraph.Title = displayMultiplierTitle;

            // Calculate raw formula values directly for consistency
            List<float> rawFormulaValues = algorithm.CalculateRawFormulaCurve(
                startingLevel,
                maxLevel,
                levelUpMultiplier);
            List<float> comparisonRawValues = null;

            if (comparisonAlgorithm != null)
            {
                // Get the raw formula values from the comparison algorithm
                comparisonRawValues = comparisonAlgorithm.CalculateRawFormulaCurve(
                    startingLevel,
                    maxLevel,
                    levelUpMultiplier);

                rawFormulaGraph.CurrentAlgorithm = comparisonAlgorithm; // Set the comparison algorithm for proper handling
            }

            // Draw the raw formula curve
            if (comparisonRawValues != null && comparisonAlgorithm != null)
            {
                rawFormulaGraph.DrawComparisonLineGraph(
                    rawFormulaValues,
                    comparisonRawValues,
                    "", // Empty title since we're using CustomTitle
                    "Level",
                    "Multiplier",
                    algorithm.Name,
                    comparisonAlgorithm.Name,
                    xAxisLabels,
                    availableWidth,
                    110f); // Increased height from 96f to 110f for better spacing
            }
            else
            {
                rawFormulaGraph.DrawLineGraph(
                    rawFormulaValues,
                    "", // Empty title since we're using CustomTitle
                    "Level",
                    "Multiplier",
                    xAxisLabels,
                    availableWidth,
                    110f); // Increased height from 96f to 110f for better spacing
            }
        }

        /// <summary>
        /// Draws a curve preview for the specified legacy difficulty enum
        /// </summary>
        /// <param name="difficulty">The legacy difficulty enum value</param>
        /// <param name="levelUpMultiplier">The level up multiplier to use</param>

    }
}