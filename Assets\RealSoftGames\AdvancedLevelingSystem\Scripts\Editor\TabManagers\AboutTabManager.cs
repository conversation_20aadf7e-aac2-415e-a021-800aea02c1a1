using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.IO;
using UnityEngine.Networking;
using System;
using System.Text.RegularExpressions;
using System.Text;
using System.Linq;
using RealSoftGames;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class AboutTabManager : ITabManager
    {
        private AdvancedLevelingSystemEditorWindow window;
        private GUIStyle headerStyle;
        private GUIStyle paragraphStyle;
        private GUIStyle linkStyle;
        private Vector2 scrollPosition;

        // Tab properties
        public string TabName => "About";

        // Version info
        private readonly string versionNumber = "2.0";
        private readonly string releaseDate = "22-04-2025";

        // Products info
        private List<ProductCache.ProductInfo> products = new List<ProductCache.ProductInfo>();
        private Texture2D defaultProductIcon;
        private bool isLoadingProducts = false;
        private string apiErrorMessage = "";
        private DateTime lastProductFetch = DateTime.MinValue;
        private const int PRODUCT_CACHE_MINUTES = 60;
        private const string API_BASE_URL = "https://realsoftgames.com/api"; // Replace localhost later with realsoftgames.com
        private const string API_TOKEN = "321f37d68f91dc9951c368f732566f452a19bd7d7657c857e7b02bdc8c1c0367";

        // API response classes
        [Serializable]
        private class ApiResponse<T>
        {
            public bool success;
            public T data;
            public string message;
        }

        [Serializable]
        private class ApiProductInfo
        {
            public string _id;
            public string title;
            public string description;
            public string externalLink; // Unity Asset Store URL
            public string liveDemoLink; // Demo URL
            public ApiProductMedia[] media;
            public string price;
            public bool isFeatured;
            public int order;
            public string status;
            public string createdAt;
            public string updatedAt;
        }

        [Serializable]
        private class ApiProductMedia
        {
            public string _id;
            public string type;
            public string url;
            public string alt;
        }



        public AboutTabManager(AdvancedLevelingSystemEditorWindow window)
        {
            this.window = window;

            // Create default product icon
            defaultProductIcon = new Texture2D(1, 1);
            defaultProductIcon.SetPixel(0, 0, new Color(0.3f, 0.3f, 0.3f, 1f));
            defaultProductIcon.Apply();

            // Initialize products list
            ClearExistingProducts();

            // Initialize product cache
            LoadCachedProducts();
        }

        public void OnEnable()
        {
            InitializeStyles();

            // Load cached products first
            LoadCachedProducts();

            // Check if we need to refresh products from API
            if (ShouldRefreshProducts())
            {
                FetchProductsFromApi();
            }
        }

        private void ClearExistingProducts()
        {
            // Clear existing products but don't clean up icons
            // This is used when we want to reset the product list without destroying textures
            products.Clear();
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
            // Save products to cache before destroying
            if (products.Count > 0)
            {
                SaveProductsToCache();
            }

            // Clean up textures
            if (defaultProductIcon != null)
            {
                UnityEngine.Object.DestroyImmediate(defaultProductIcon);
                defaultProductIcon = null;
            }

            // Clean up product icons
            ClearProductIcons();
        }

        public void Update()
        {
            // If we're loading products, we need to repaint the window to show progress
            if (isLoadingProducts)
            {
                window.Repaint();
            }
        }

        private bool ShouldRefreshProducts()
        {
            // Check if products list is empty or if cache has expired
            bool productsEmpty = products.Count == 0;
            bool cacheExpired = (DateTime.Now - lastProductFetch).TotalMinutes > PRODUCT_CACHE_MINUTES;

            // If we have a cache but no products loaded, don't refresh immediately
            if (productsEmpty && productCache != null &&
                productCache.cachedProducts != null &&
                productCache.cachedProducts.Count > 0)
            {
                // Only refresh if the cache is also expired
                bool cacheTooOld = (DateTime.Now - productCache.lastFetchTime).TotalMinutes > PRODUCT_CACHE_MINUTES;
                return cacheTooOld;
            }

            // Otherwise refresh if products are empty or cache expired
            return productsEmpty || cacheExpired;
        }

        // Reference to the ProductCache ScriptableObject
        private ProductCache productCache;
        private const string CACHE_PATH = "Assets/RealSoftGames/Resources/ProductCache.asset";

        private void LoadCachedProducts()
        {
            // Try to load the cache from the Resources folder
            productCache = AssetDatabase.LoadAssetAtPath<ProductCache>(CACHE_PATH);

            if (productCache == null)
            {
                // Create a new cache if it doesn't exist
                productCache = ScriptableObject.CreateInstance<ProductCache>();

                // Ensure the directory exists
                string directory = Path.GetDirectoryName(CACHE_PATH);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    AssetDatabase.Refresh(); // Refresh to make sure Unity sees the new directory
                }

                // Create the asset
                AssetDatabase.CreateAsset(productCache, CACHE_PATH);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh(); // Refresh to make sure Unity sees the new asset

                Debug.Log("ProductCache asset created at: " + CACHE_PATH);

                // Select the newly created asset to make it visible in the Project window
                Selection.activeObject = productCache;
                EditorGUIUtility.PingObject(productCache);
            }
            else if (productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
            {
                // Use cached products
                products.Clear();
                foreach (var cachedProduct in productCache.cachedProducts)
                {
                    products.Add(cachedProduct);

                    // Don't load icons immediately - they'll be loaded on demand when displayed
                    cachedProduct.Icon = null;
                    cachedProduct.IsLoadingIcon = false;
                }

                lastProductFetch = productCache.lastFetchTime;
            }
        }

        private void SaveProductsToCache()
        {
            if (productCache != null && products.Count > 0)
            {
                productCache.cachedProducts.Clear();
                productCache.cachedProducts.AddRange(products);
                productCache.lastFetchTime = DateTime.Now;

                EditorUtility.SetDirty(productCache);
                AssetDatabase.SaveAssets();
            }
        }

        private async void FetchProductsFromApi()
        {
            isLoadingProducts = true;
            apiErrorMessage = "";
            window.Repaint();

            // Always load cached products first
            LoadCachedProducts();

            // If we have cached products, use them immediately while we fetch new ones
            if (productCache != null && productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
            {
                products.Clear();
                products.AddRange(productCache.cachedProducts);
            }

            try
            {
                // Create HTTP client with authorization header
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {API_TOKEN}");
                    client.Timeout = TimeSpan.FromSeconds(10); // Set a reasonable timeout

                    // Make the API request
                    string url = $"{API_BASE_URL}/products";
                    HttpResponseMessage response = await client.GetAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResponse = await response.Content.ReadAsStringAsync();
                        //Debug.Log($"API Response: {jsonResponse}");

                        // Use the new API response handler for the updated format
                        HandleNewApiResponseFormat(jsonResponse);

                        // Save successful response to cache
                        SaveProductsToCache();
                    }
                    else
                    {
                        apiErrorMessage = $"API Error: {response.StatusCode} - {response.ReasonPhrase}";
                        Debug.LogError(apiErrorMessage);

                        // Always use cached data if available when there's an API error
                        if (productCache != null && productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
                        {
                            apiErrorMessage += "\nUsing cached product data.";
                            products.Clear(); // Clear any partial data
                            products.AddRange(productCache.cachedProducts);
                        }
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                // Network error - likely no internet connection
                apiErrorMessage = $"Network error: {ex.Message}";
                Debug.LogError(apiErrorMessage);

                // Always use cached data if available when there's a network error
                if (productCache != null && productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
                {
                    apiErrorMessage += "\nUsing cached product data.";
                    products.Clear(); // Clear any partial data
                    products.AddRange(productCache.cachedProducts);
                }
            }
            catch (TaskCanceledException ex)
            {
                // Timeout error
                apiErrorMessage = "Request timed out. Please check your internet connection.";
                Debug.LogError($"Timeout error: {ex.Message}");

                // Always use cached data if available when there's a timeout
                if (productCache != null && productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
                {
                    apiErrorMessage += "\nUsing cached product data.";
                    products.Clear(); // Clear any partial data
                    products.AddRange(productCache.cachedProducts);
                }
            }
            catch (Exception ex)
            {
                apiErrorMessage = $"Error fetching products: {ex.Message}";
                Debug.LogError(apiErrorMessage);

                // Always use cached data if available when there's any error
                if (productCache != null && productCache.cachedProducts != null && productCache.cachedProducts.Count > 0)
                {
                    apiErrorMessage += "\nUsing cached product data.";
                    products.Clear(); // Clear any partial data
                    products.AddRange(productCache.cachedProducts);
                }
            }
            finally
            {
                isLoadingProducts = false;
                lastProductFetch = DateTime.Now;
                window.Repaint();
            }
        }

        // Wrapper class for JSON array
        [Serializable]
        private class ApiProductInfoList
        {
            public List<ApiProductInfo> items;
        }

        // Rewritten method to handle the new API response format
        private void HandleNewApiResponseFormat(string jsonResponse)
        {
            try
            {
                // Clear existing products
                ClearProductIcons();
                products.Clear();

                // The expected format is a direct array of products
                if (jsonResponse.TrimStart().StartsWith("["))
                {
                    // Unity's JsonUtility doesn't directly support top-level arrays,
                    // so we need to wrap it in an object
                    string wrappedJson = $"{{\"items\":{jsonResponse}}}";
                    ApiProductInfoList productList = JsonUtility.FromJson<ApiProductInfoList>(wrappedJson);

                    if (productList != null && productList.items != null)
                    {
                        ProcessProductList(productList.items);
                        return;
                    }
                }

                // If we got here, parsing failed
                apiErrorMessage = "Failed to parse API response. Expected a JSON array.";
                Debug.LogError(apiErrorMessage);
                Debug.LogError($"JSON Response: {jsonResponse}");
            }
            catch (Exception ex)
            {
                apiErrorMessage = $"Error processing API response: {ex.Message}";
                Debug.LogError(apiErrorMessage);
                Debug.LogError($"JSON Response: {jsonResponse}");
            }
        }

        private void ProcessApiResponse(string jsonResponse)
        {
            try
            {
                // Clear existing products
                ClearProductIcons();
                products.Clear();

                // Try to determine the JSON format
                if (jsonResponse.TrimStart().StartsWith("{"))
                {
                    // Object format - try standard API response format
                    try
                    {
                        ApiResponse<List<ApiProductInfo>> apiResponse =
                            JsonUtility.FromJson<ApiResponse<List<ApiProductInfo>>>(jsonResponse);

                        if (apiResponse != null && apiResponse.success && apiResponse.data != null)
                        {
                            ProcessProductList(apiResponse.data);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to parse as ApiResponse<List>: {ex.Message}. Trying alternative formats...");
                    }

                    // Try parsing as a direct object with 'items' property
                    try
                    {
                        ApiProductInfoList productList = JsonUtility.FromJson<ApiProductInfoList>(jsonResponse);
                        if (productList != null && productList.items != null)
                        {
                            ProcessProductList(productList.items);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to parse as ApiProductInfoList: {ex.Message}. Trying alternative formats...");
                    }
                }
                else if (jsonResponse.TrimStart().StartsWith("["))
                {
                    // Array format - wrap in a temporary object for JsonUtility
                    try
                    {
                        string wrappedJson = $"{{\"items\":{jsonResponse}}}";
                        ApiProductInfoList productList = JsonUtility.FromJson<ApiProductInfoList>(wrappedJson);

                        if (productList != null && productList.items != null)
                        {
                            ProcessProductList(productList.items);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to parse as wrapped array: {ex.Message}");
                    }
                }

                // If we got here, all parsing attempts failed
                apiErrorMessage = "Failed to parse API response. Unsupported format.";
                Debug.LogError(apiErrorMessage);
                Debug.LogError($"JSON Response: {jsonResponse}");

                // Don't use fallback products for parsing errors
                // Just show the error message to the user
            }
            catch (Exception ex)
            {
                apiErrorMessage = $"Error processing API response: {ex.Message}";
                Debug.LogError(apiErrorMessage);

                // Don't use fallback products for API errors
                // Just show the error message to the user
            }
        }



        private void ProcessProductList(List<ApiProductInfo> apiProducts)
        {
            foreach (var apiProduct in apiProducts)
            {
                // Skip products that aren't published
                if (apiProduct.status != "Published")
                    continue;

                // Extract price as float (remove $ sign and convert)
                float price = 0f;
                if (!string.IsNullOrEmpty(apiProduct.price))
                {
                    string priceStr = apiProduct.price.Replace("$", "").Trim();
                    float.TryParse(priceStr, out price);
                }

                // Find the main image URL from media array
                string imageUrl = "";
                if (apiProduct.media != null && apiProduct.media.Length > 0)
                {
                    var mainImage = apiProduct.media.FirstOrDefault(m => m.type == "image");
                    if (mainImage != null)
                    {
                        imageUrl = mainImage.url;
                    }
                }

                ProductCache.ProductInfo product = new ProductCache.ProductInfo
                {
                    id = apiProduct._id,
                    Name = apiProduct.title,
                    Description = apiProduct.description,
                    Url = apiProduct.liveDemoLink ?? "", // Demo URL
                    AssetStoreUrl = apiProduct.externalLink ?? "", // Asset Store URL
                    IconUrl = imageUrl,
                    Price = price,
                    Category = "Asset", // Default category
                    IsFeatured = apiProduct.isFeatured,
                    Icon = defaultProductIcon,
                    LastUpdated = !string.IsNullOrEmpty(apiProduct.updatedAt) ?
                        DateTime.Parse(apiProduct.updatedAt) : DateTime.Now
                };

                products.Add(product);

                // Don't download icons immediately - they'll be loaded on demand when displayed
                // Just store the URL for later use
                product.Icon = null;
                product.IsLoadingIcon = false;
            }

            // Sort products by featured status, then by order, then by name
            products = products.OrderByDescending(p => p.IsFeatured)
                              .ThenBy(p => p.Name)
                              .ToList();
        }

        private void LoadProductIcon(ProductCache.ProductInfo product)
        {
            if (string.IsNullOrEmpty(product.IconUrl) || product.IsLoadingIcon)
                return;

            // Mark as loading to prevent multiple requests
            product.IsLoadingIcon = true;

            try
            {
                // Add a cache buster to prevent caching issues
                string url = product.IconUrl;
                if (!url.Contains("?"))
                    url += $"?t={DateTime.Now.Ticks}";
                else
                    url += $"&t={DateTime.Now.Ticks}";

                UnityWebRequest request = UnityWebRequestTexture.GetTexture(url);
                request.timeout = 10; // 10 second timeout
                UnityWebRequestAsyncOperation operation = request.SendWebRequest();

                operation.completed += (AsyncOperation op) => {
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        try
                        {
                            Texture2D texture = DownloadHandlerTexture.GetContent(request);

                            // Use a fixed size for all images without preserving aspect ratio
                            int targetSize = 128; // Larger size for better quality

                            try {
                                // Get original dimensions
                                int originalWidth = texture.width;
                                int originalHeight = texture.height;

                                // Use fixed dimensions for all images
                                int newWidth = targetSize;
                                int newHeight = targetSize;

                                // Create a properly sized texture with a more memory-efficient format
                                Texture2D resizedTexture = new Texture2D(newWidth, newHeight, TextureFormat.RGB24, false);

                                // Use a temporary render texture for resizing
                                RenderTexture rt = RenderTexture.GetTemporary(newWidth, newHeight, 0, RenderTextureFormat.ARGB32);
                                rt.filterMode = FilterMode.Bilinear;

                                // Blit the texture to the render texture
                                Graphics.Blit(texture, rt);
                                RenderTexture.active = rt;

                                // Read the pixels from the render texture
                                resizedTexture.ReadPixels(new Rect(0, 0, newWidth, newHeight), 0, 0);
                                resizedTexture.Apply();

                                // Clean up resources immediately
                                RenderTexture.active = null;
                                RenderTexture.ReleaseTemporary(rt);
                                UnityEngine.Object.DestroyImmediate(texture);

                                // Force garbage collection to free memory
                                System.GC.Collect();

                                product.Icon = resizedTexture;
                            }
                            catch (OutOfMemoryException)
                            {
                                // Handle out of memory specifically
                                Debug.LogWarning($"Out of memory when processing image for {product.Name}. Using default icon.");
                                product.Icon = defaultProductIcon;

                                // Clean up any resources that might be in use
                                if (texture != null) UnityEngine.Object.DestroyImmediate(texture);
                                RenderTexture.active = null;

                                // Force garbage collection
                                System.GC.Collect();
                            }

                            // Icon is already set in the try/catch block
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"Error processing image for {product.Name}: {ex.Message}");
                            product.Icon = defaultProductIcon;
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Failed to download icon for {product.Name}: {request.error}");
                        product.Icon = defaultProductIcon;
                    }

                    // Mark as no longer loading
                    product.IsLoadingIcon = false;

                    // Force repaint to show the loaded icon
                    window.Repaint();

                    request.Dispose();
                };
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error downloading product icon: {ex.Message}");
                product.Icon = defaultProductIcon;
                product.IsLoadingIcon = false;
            }
        }

        private void ClearProductIcons()
        {
            // Clean up existing product icons to prevent memory leaks
            foreach (var product in products)
            {
                if (product.Icon != null && product.Icon != defaultProductIcon)
                {
                    UnityEngine.Object.DestroyImmediate(product.Icon);
                    product.Icon = null;
                }

                // Reset loading state
                product.IsLoadingIcon = false;
            }
        }



        private void DisplayProductCard(ProductCache.ProductInfo product, float width)
        {
            #region Card Setup
            // Ensure width is reasonable for the card
            width = Mathf.Clamp(width, 300, 1200);

            // Set a fixed icon size to match the example image
            float iconSize = 130f;

            // Set a fixed card height to match the example image
            float cardHeight = 160f;

            // Calculate vertical padding for centering elements
            float verticalPadding = (cardHeight - iconSize) / 2;

            // Create a dark background for the entire card
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Height(cardHeight));

            // Use horizontal layout for the entire card with three main sections
            EditorGUILayout.BeginHorizontal();
            #endregion

            #region LEFT SECTION - Product icon with background
            EditorGUILayout.BeginVertical(GUILayout.Width(iconSize + 20));

            // Add top padding to center the icon
            GUILayout.Space(verticalPadding); // Top padding

            // Create a centered icon with proper padding
            EditorGUILayout.BeginHorizontal();
            GUILayout.Space(10); // Left padding

            // Icon container
            Rect iconRect = EditorGUILayout.GetControlRect(GUILayout.Width(iconSize), GUILayout.Height(iconSize));

            // Draw icon background
            EditorGUI.DrawRect(iconRect, new Color(0.15f, 0.15f, 0.15f));

            // Draw icon
            if (product.Icon != null)
            {
                GUI.DrawTexture(iconRect, product.Icon, ScaleMode.StretchToFill);
            }
            else if (product.IsLoadingIcon)
            {
                EditorGUI.LabelField(iconRect, "Loading...", new GUIStyle(EditorStyles.centeredGreyMiniLabel));
            }
            else
            {
                EditorGUI.LabelField(iconRect, "No Icon", new GUIStyle(EditorStyles.centeredGreyMiniLabel));

                // Start loading the icon if we have a URL
                if (!string.IsNullOrEmpty(product.IconUrl))
                {
                    LoadProductIcon(product);
                }
            }

            // Featured badge - positioned at the bottom of the icon
            if (product.IsFeatured)
            {
                float badgeHeight = Mathf.Max(16f, iconSize * 0.15f);
                Rect featuredRect = new Rect(iconRect.x, iconRect.y + iconRect.height - badgeHeight, iconRect.width, badgeHeight);
                EditorGUI.DrawRect(featuredRect, new Color(0.9f, 0.6f, 0.1f, 0.8f));

                GUIStyle featuredStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = Color.white },
                    alignment = TextAnchor.MiddleCenter,
                    fontStyle = FontStyle.Bold,
                    fontSize = Mathf.Max(10, Mathf.FloorToInt(badgeHeight * 0.6f))
                };

                EditorGUI.LabelField(featuredRect, "★ FEATURED", featuredStyle);
            }

            GUILayout.Space(10); // Right padding
            EditorGUILayout.EndHorizontal();

            // No need for additional bottom padding as we've already calculated the spacing

            EditorGUILayout.EndVertical();
            #endregion

            #region MIDDLE SECTION - Product details (name, category, description)
            EditorGUILayout.BeginVertical(GUILayout.ExpandWidth(true));

            // Product header with name and category
            int nameFontSize = Mathf.Max(12, Mathf.FloorToInt(width * 0.015f));

            GUIStyle nameStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = nameFontSize,
                wordWrap = true,
                richText = true, // Enable rich text to render HTML tags
                alignment = TextAnchor.MiddleLeft
            };
            EditorGUILayout.LabelField(ConvertHtmlToUnityRichText(product.Name), nameStyle, GUILayout.Height(nameFontSize + 4));

            // Show category if available
            if (!string.IsNullOrEmpty(product.Category))
            {
                GUIStyle categoryStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = new Color(0.5f, 0.5f, 0.5f) },
                    richText = true // Enable rich text to render HTML tags
                };
                EditorGUILayout.LabelField(ConvertHtmlToUnityRichText(product.Category), categoryStyle, GUILayout.Height(16));
            }

            EditorGUILayout.Space(4);

            // COMPLETELY REVAMPED PRODUCT DESCRIPTION DISPLAY
            // Convert HTML to Unity-compatible rich text
            string description = ConvertHtmlToUnityRichText(product.Description);

            // Calculate description height based on card height
            float descHeight = cardHeight - (nameFontSize + 30);

            // Create a description style that works well with scrolling
            int descFontSize = Mathf.Max(10, Mathf.FloorToInt(width * 0.01f));
            GUIStyle descStyle = new GUIStyle(EditorStyles.label) {
                wordWrap = true,
                richText = true,
                fontSize = descFontSize,
                normal = { textColor = new Color(0.7f, 0.7f, 0.7f) },
                margin = new RectOffset(0, 0, 0, 0),
                padding = new RectOffset(5, 5, 0, 0), // Only horizontal padding
                alignment = TextAnchor.UpperLeft // Align to top-left
            };

            // Initialize scroll position if needed
            if (!product.scrollPositions.ContainsKey("description"))
                product.scrollPositions["description"] = Vector2.zero;

            // Begin a scroll view with standard EditorGUILayout methods
            product.scrollPositions["description"] = EditorGUILayout.BeginScrollView(
                product.scrollPositions["description"],
                GUILayout.Height(descHeight));

            // Display the description text using a standard label
            // This is much simpler and more reliable than custom GUI.Label with Rect
            EditorGUILayout.LabelField(description, descStyle);

            // End the scroll view
            EditorGUILayout.EndScrollView();

            EditorGUILayout.EndVertical();
            #endregion

            #region RIGHT SECTION - Price and button
            EditorGUILayout.BeginVertical(GUILayout.Width(100)); // Wider to accommodate full-width button

            // Price with green color
            int priceFontSize = Mathf.Max(12, Mathf.FloorToInt(width * 0.015f));

            // Calculate spacing to position price and button at the bottom
            float priceHeight = priceFontSize + 4;
            float buttonHeight = 30f;
            float priceButtonSpacing = 5f; // Small space between price and button
            float totalContentHeight = priceHeight + priceButtonSpacing + buttonHeight;

            // Calculate space to push content to the bottom
            float topSpace = cardHeight - totalContentHeight - 10f; // 10f for bottom margin

            // Add flexible space to push price and button to the bottom
            GUILayout.Space(topSpace);
            GUIStyle priceStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = priceFontSize,
                normal = { textColor = new Color(0.2f, 0.6f, 0.2f) },
                alignment = TextAnchor.MiddleCenter
            };
            EditorGUILayout.LabelField($"${product.Price:F2}", priceStyle, GUILayout.Height(priceFontSize + 4));

            // Add small space between price label and button
            GUILayout.Space(priceButtonSpacing);

            // View button
            string urlToOpen = !string.IsNullOrEmpty(product.AssetStoreUrl) ? product.AssetStoreUrl : product.Url;
            int buttonFontSize = Mathf.Max(10, Mathf.FloorToInt(width * 0.01f));
            GUIStyle buttonStyle = CreateResponsiveButtonStyle(buttonFontSize);

            // Make button full width of the right section with the previously defined button height
            if (GUILayout.Button("View Asset", buttonStyle, GUILayout.ExpandWidth(true), GUILayout.Height(buttonHeight)))
            {
                Application.OpenURL(urlToOpen);
            }

            // No need for additional bottom padding as we've already calculated the spacing

            EditorGUILayout.EndVertical();
            #endregion

            #region Card Closing
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
            #endregion
        }

        // Helper method to convert HTML to Unity-compatible rich text (optimized for Quill editor)
        private string ConvertHtmlToUnityRichText(string html)
        {
            if (string.IsNullOrEmpty(html))
                return html;

            // COMPLETELY REVAMPED APPROACH FOR QUILL TEXT

            // Step 1: First, handle the literal "\\n\\n" sequences that appear in the text
            // This is the most direct approach to handle the specific issue in the screenshot
            string result = html;

            // Handle literal backslash+n sequences - this is the key fix for the issue
            // We need to be very aggressive with this replacement to catch all variations

            // First, handle the literal "\\n\\n" pattern (double newline)
            result = result.Replace("\\n\\n", "\n\n");

            // Then handle single "\\n" pattern
            result = result.Replace("\\n", "\n");

            // Also handle any remaining escaped newlines that might be in different formats
            result = result.Replace("\\r\\n", "\n");
            result = result.Replace("\\r", "\n");

            // Step 2: Handle HTML entities and tags
            result = result
                .Replace("&nbsp;", " ")
                .Replace("&amp;", "&")
                .Replace("&lt;", "<")
                .Replace("&gt;", ">")
                .Replace("&quot;", "\"")
                .Replace("&apos;", "'");

            // Step 3: Handle HTML formatting
            // Remove paragraph tags but preserve their line break meaning
            result = result.Replace("</p><p>", "\n\n"); // Double newline between paragraphs
            result = result.Replace("<p>", ""); // Remove opening p tags
            result = result.Replace("</p>", " "); // Single newline at end of paragraphs

            // Handle line breaks
            result = result.Replace("<br>", "\n");
            result = result.Replace("<br/>", "\n");
            result = result.Replace("<br />", "\n");

            // Handle headings (Quill uses standard heading tags)
            result = Regex.Replace(result, "<h1>(.*?)</h1>", "\n<b><size=18>$1</size></b>\n");
            result = Regex.Replace(result, "<h2>(.*?)</h2>", "\n<b><size=16>$1</size></b>\n");
            result = Regex.Replace(result, "<h3>(.*?)</h3>", "\n<b><size=14>$1</size></b>\n");
            result = Regex.Replace(result, "<h4>(.*?)</h4>", "\n<b><size=12>$1</size></b>\n");
            result = Regex.Replace(result, "<h5>(.*?)</h5>", "\n<b>$1</b>\n");
            result = Regex.Replace(result, "<h6>(.*?)</h6>", "\n<b>$1</b>\n");

            // Handle lists
            result = result.Replace("<ul>", "\n");
            result = result.Replace("</ul>", "\n");
            result = result.Replace("<ol>", "\n");
            result = result.Replace("</ol>", "\n");
            result = Regex.Replace(result, "<li>(.*?)</li>", "\n  • $1");

            // Handle text formatting
            result = Regex.Replace(result, "<strong>(.*?)</strong>", "<b>$1</b>");
            result = Regex.Replace(result, "<em>(.*?)</em>", "<i>$1</i>");
            result = Regex.Replace(result, "<u>(.*?)</u>", "<u>$1</u>");

            // Handle links
            result = Regex.Replace(result, "<a\\s+href=['\"]([^'\"]*)['\"]\\s*>(.*?)</a>", "<color=#0066cc>$2</color>");

            // Handle Quill's specific class-based styling
            result = Regex.Replace(result, "<p class=\"ql-align-center\">(.*?)</p>", "<align=center>$1</align>");
            result = Regex.Replace(result, "<p class=\"ql-align-right\">(.*?)</p>", "<align=right>$1</align>");

            // Step 4: Remove all other HTML tags
            result = Regex.Replace(result, "</?[^<>]*>", "");

            // Step 5: Fix specific patterns seen in the screenshot
            // These are targeted fixes for the specific issues shown

            // Fix spacing after "for Unity" pattern
            result = Regex.Replace(result, @"for Unity\n\n", "for Unity\n");

            // Fix spacing in "Advanced Loading Screen for Unity\n\n"
            result = Regex.Replace(result, @"Advanced Loading Screen for Unity\n\n", "Advanced Loading Screen for Unity\n");

            // Fix spacing after "mechanics." pattern
            result = Regex.Replace(result, @"mechanics\.\n\n", "mechanics.\n");

            // Step 6: Normalize newlines and whitespace

            // Convert all Windows newlines to Unix newlines
            result = result.Replace("\r\n", "\n");

            // Normalize whitespace (but preserve line breaks)
            result = Regex.Replace(result, "[ \\t]+", " ");

            // Normalize multiple consecutive newlines to at most two
            result = Regex.Replace(result, @"\n{3,}", "\n\n");

            // Fix spacing around bullet points
            result = Regex.Replace(result, @"\n\n\s*•", "\n\n•");
            result = Regex.Replace(result, @"\n\s*•", "\n•");

            // Fix spacing around section headers like "Features:"
            result = Regex.Replace(result, @"\n\n\s*Features:", "\n\nFeatures:");
            result = Regex.Replace(result, @"\nFeatures:\s*\n", "\nFeatures:\n");

            // Step 7: Final cleanup

            // Remove any excessive newlines at the beginning of the text
            result = Regex.Replace(result, "^\n+", "");

            // Ensure proper spacing between paragraphs without adding too much space
            result = Regex.Replace(result, "\n{3,}", "\n\n");

            // Make sure newlines are properly preserved in Unity's IMGUI
            result = result.Replace("\r\n", "\n");

            return result.Trim();
        }

        // Helper method to create a responsive button style that uses Unity's default styling
        private GUIStyle CreateResponsiveButtonStyle(int fontSize = 12)
        {
            GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
            // Keep Unity's default styling for normal/hover/active states
            // Just adjust the font size and make it bold
            buttonStyle.fontStyle = FontStyle.Bold;
            buttonStyle.fontSize = fontSize;
            return buttonStyle;
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel);
                headerStyle.fontSize = 16;
                headerStyle.alignment = TextAnchor.MiddleLeft;
                headerStyle.margin = new RectOffset(5, 5, 5, 5);
            }

            if (paragraphStyle == null)
            {
                paragraphStyle = new GUIStyle(EditorStyles.label);
                paragraphStyle.wordWrap = true;
                paragraphStyle.richText = true;
                paragraphStyle.fontSize = 12;
                paragraphStyle.margin = new RectOffset(5, 5, 5, 5);
                paragraphStyle.stretchHeight = true; // Allow the style to stretch for proper line breaks
                paragraphStyle.fixedHeight = 0; // Ensure the height can adjust to content
                paragraphStyle.padding = new RectOffset(0, 0, 4, 4); // Add padding for better line spacing
            }

            if (linkStyle == null)
            {
                linkStyle = new GUIStyle(EditorStyles.label);
                linkStyle.richText = true;
                linkStyle.fontSize = 12;
                linkStyle.normal.textColor = new Color(0.2f, 0.4f, 0.8f);
            }
        }

        public void OnGUI()
        {
            InitializeStyles();

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.LabelField("About Advanced Leveling System", headerStyle);
            EditorGUILayout.Space(10);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Version info
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField($"<b>Version:</b> {versionNumber}", paragraphStyle);
            EditorGUILayout.LabelField($"<b>Release Date:</b> {releaseDate}", paragraphStyle);
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Description
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("<b>Description</b>", paragraphStyle);
            EditorGUILayout.LabelField(
                "The Advanced Leveling System is a comprehensive solution for implementing flexible and customizable " +
                "leveling mechanics in your Unity games. It provides a wide range of leveling algorithms, from simple " +
                "linear progression to complex mathematical formulas, allowing you to fine-tune the difficulty curve " +
                "of your game.", paragraphStyle);
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField(
                "The system also includes an experience rewards calculator to help you balance your game's reward " +
                "structure based on the chosen leveling curve, ensuring a consistent and enjoyable player experience.", paragraphStyle);
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Features
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("<b>Key Features</b>", paragraphStyle);
            EditorGUILayout.LabelField("• 30+ leveling algorithms with customizable parameters", paragraphStyle);
            EditorGUILayout.LabelField("• Visual curve previews to compare different progression models", paragraphStyle);
            EditorGUILayout.LabelField("• Experience rewards calculator for balanced game design", paragraphStyle);
            EditorGUILayout.LabelField("• Detailed descriptions and formulas for each leveling method", paragraphStyle);
            EditorGUILayout.LabelField("• Easy integration with existing game systems", paragraphStyle);
            EditorGUILayout.LabelField("• Full source code access for customization", paragraphStyle);
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Documentation
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("<b>Documentation</b>", paragraphStyle);
            EditorGUILayout.LabelField(
                "For detailed documentation, tutorials, and examples, please visit our documentation website:", paragraphStyle);

            if (GUILayout.Button("Open Documentation", CreateResponsiveButtonStyle(), GUILayout.Height(30)))
            {
                Application.OpenURL("https://realsoftgames.com/docs/advanced-leveling-system");
            }

            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Video tutorials are also available on our YouTube channel:", paragraphStyle);

            if (GUILayout.Button("Watch Video Tutorials", CreateResponsiveButtonStyle(), GUILayout.Height(30)))
            {
                Application.OpenURL("https://www.youtube.com/@realsoftgames7174");
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Support
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("<b>Support</b>", paragraphStyle);
            EditorGUILayout.LabelField(
                "If you need help or have any questions, please contact us at:", paragraphStyle);

            if (GUILayout.Button("<EMAIL>", linkStyle))
            {
                Application.OpenURL("mailto:<EMAIL>?subject=Advanced%20Leveling%20System%20Support");
            }

            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Join our Discord community for support:", paragraphStyle);

            if (GUILayout.Button("Join Discord Community", CreateResponsiveButtonStyle(), GUILayout.Height(30)))
            {
                Application.OpenURL("https://discord.gg/AUrh5Xd");
            }

            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Visit our website for more assets and information:", paragraphStyle);

            if (GUILayout.Button("Visit RealSoft Games Website", CreateResponsiveButtonStyle(), GUILayout.Height(30)))
            {
                Application.OpenURL("https://realsoftgames.com");
            }

            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Check out our YouTube channel for video tutorials:", paragraphStyle);

            if (GUILayout.Button("YouTube Tutorials", CreateResponsiveButtonStyle(), GUILayout.Height(30)))
            {
                Application.OpenURL("https://www.youtube.com/@realsoftgames7174");
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // More Products - with stylish header
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Create a stylish header background
            Rect headerRect = EditorGUILayout.GetControlRect(GUILayout.Height(30));
            EditorGUI.DrawRect(headerRect, new Color(0.2f, 0.4f, 0.7f));

            // Add header text with white color
            GUIStyle headerTextStyle = new GUIStyle(headerStyle);
            headerTextStyle.normal.textColor = Color.white;
            headerTextStyle.alignment = TextAnchor.MiddleCenter;
            EditorGUI.LabelField(headerRect, "More Products from RealSoft Games", headerTextStyle);

            // Show loading indicator or error message if applicable
            if (isLoadingProducts)
            {
                EditorGUILayout.Space(10);
                EditorGUILayout.LabelField("Loading products...", paragraphStyle);
                EditorGUILayout.Space(10);
            }
            else if (!string.IsNullOrEmpty(apiErrorMessage))
            {
                EditorGUILayout.Space(10);
                EditorGUILayout.HelpBox(apiErrorMessage, MessageType.Warning);
                EditorGUILayout.Space(5);

                if (GUILayout.Button("Retry", CreateResponsiveButtonStyle(), GUILayout.Height(25)))
                {
                    FetchProductsFromApi();
                }
                EditorGUILayout.Space(10);
            }

            // Display products in a list
            if (!isLoadingProducts && string.IsNullOrEmpty(apiErrorMessage) && products.Count > 0)
            {
                EditorGUILayout.Space(10);

                // Get the available width, accounting for the left panel in the editor template
                float contentWidth = EditorWindowTemplate.MainBodyRect.width - 10; // Reduced margin

                // Calculate card width based on available space
                float cardWidth = contentWidth - 10f; // Minimal padding to prevent horizontal scrollbar

                // Display products in a single column
                for (int i = 0; i < products.Count; i++)
                {
                    // Add minimal horizontal padding
                    EditorGUILayout.BeginHorizontal();
                    GUILayout.Space(5);

                    // Display the product card
                    DisplayProductCard(products[i], cardWidth);

                    GUILayout.Space(5);
                    EditorGUILayout.EndHorizontal();

                    // Add space between cards
                    EditorGUILayout.Space(10);
                }

                // Stylish refresh button
                EditorGUILayout.Space(20);
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();

                // Create a responsive button that uses Unity's default styling
                GUIStyle refreshButtonStyle = CreateResponsiveButtonStyle(12);
                // Add extra padding for this specific button
                refreshButtonStyle.padding = new RectOffset(15, 15, 5, 5);

                // Calculate responsive button width based on content width
                float refreshButtonWidth = Mathf.Min(200f, contentWidth * 0.3f);

                if (GUILayout.Button("↻ Refresh Products", refreshButtonStyle, GUILayout.Width(refreshButtonWidth), GUILayout.Height(30)))
                {
                    FetchProductsFromApi();
                }

                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space(15);

                // End of refresh button section
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Credits
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("<b>Credits</b>", paragraphStyle);
            EditorGUILayout.LabelField("Developed by RealSoft Games", paragraphStyle);
            EditorGUILayout.LabelField("© 2023-2025 RealSoft Games. All rights reserved.", paragraphStyle);
            EditorGUILayout.EndVertical();

            // End the scroll view AFTER the credits section
            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }
    }
}
