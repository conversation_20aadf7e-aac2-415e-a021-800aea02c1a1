using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using RealSoftGames.AdvancedLevelingSystem;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Handles file operations for the algorithm designer
    /// </summary>
    public class AlgorithmDesignerFileOperations
    {
        // Pattern selection
        private string[] patternNames = new string[] { "None" };
        private string[] patternPaths = new string[] { "" };
        private int selectedPatternIndex = 0;

        // Events
        public event Action<LevelingAlgorithmBase, string> OnPatternLoaded;
        public event Action<string[]> OnPatternsFound;

        public string[] PatternNames => patternNames;
        public string[] PatternPaths => patternPaths;
        public int SelectedPatternIndex => selectedPatternIndex;

        /// <summary>
        /// Finds all existing pattern algorithms in the project
        /// </summary>
        public void FindExistingPatterns(string currentPatternPath = "")
        {
            // Find all LevelingAlgorithmBase assets in the project
            string[] guids = AssetDatabase.FindAssets("t:LevelingAlgorithmBase");

            // Create a list to hold valid patterns
            List<string> validNames = new List<string> { "None" };
            List<string> validPaths = new List<string> { "" };

            // Default to "None" selection
            selectedPatternIndex = 0;

            // Process each found asset
            if (guids.Length > 0)
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guids[i]);
                    LevelingAlgorithmBase pattern = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                    if (pattern != null)
                    {
                        // Include both DrawnPattern and CodedFormula algorithms
                        // Add algorithm type to the name for clarity
                        string algorithmTypeLabel = pattern.algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern ?
                            "[Pattern]" : "[Formula]";
                        validNames.Add($"{pattern.algorithmName} {algorithmTypeLabel}");
                        validPaths.Add(path);

                        // If we're editing this pattern, select it in the dropdown
                        if (path == currentPatternPath)
                        {
                            selectedPatternIndex = validNames.Count - 1;
                        }
                    }
                }
            }

            // Convert lists to arrays
            patternNames = validNames.ToArray();
            patternPaths = validPaths.ToArray();

            // Debug log to help diagnose issues
            Debug.Log($"Found {patternNames.Length - 1} algorithm(s) for editing. Selected index: {selectedPatternIndex}");

            // Log more detailed information about the algorithms found
            if (patternNames.Length > 1)
            {
                Debug.Log("Algorithm names found:");
                for (int i = 1; i < patternNames.Length; i++)
                {
                    Debug.Log($"  {i}: {patternNames[i]} (Path: {patternPaths[i]})");
                }
            }
            else
            {
                Debug.LogWarning("No algorithms found. Try using the 'Create Default Algorithms' button to create some algorithms.");
            }

            // Notify listeners
            OnPatternsFound?.Invoke(patternPaths);
        }

        /// <summary>
        /// Loads a pattern by path
        /// </summary>
        public void LoadPatternByPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return;

            try
            {
                // Load the pattern asset
                LevelingAlgorithmBase loadedPattern = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                if (loadedPattern == null)
                {
                    Debug.LogError($"Failed to load pattern at path: {path}");
                    return;
                }

                // Create a working copy of the loaded pattern
                LevelingAlgorithmBase algorithmData = ScriptableObject.Instantiate(loadedPattern);

                // Notify listeners
                OnPatternLoaded?.Invoke(algorithmData, path);

                // Try to load settings from EditorPrefs using the asset's GUID as a key
                string guid = AssetDatabase.AssetPathToGUID(path);
                string settingsKey = $"AlgorithmDesigner_{guid}_Settings";
                string settingsJson = EditorPrefs.GetString(settingsKey, "");

                if (!string.IsNullOrEmpty(settingsJson))
                {
                    try
                    {
                        // Deserialize settings
                        PatternEditorSettings settings = JsonUtility.FromJson<PatternEditorSettings>(settingsJson);

                        // Apply settings
                        if (settings != null)
                        {
                            // Notify listeners about settings
                            OnSettingsLoaded?.Invoke(settings);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to load pattern settings: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error loading pattern: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads a pattern using a file dialog
        /// </summary>
        public void LoadPattern()
        {
            // Show a file selection dialog
            string path = EditorUtility.OpenFilePanelWithFilters(
                "Load Pattern",
                "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms",
                new string[] { "Pattern Assets", "asset" });

            // Convert to asset path
            if (!string.IsNullOrEmpty(path))
            {
                // Convert absolute path to project-relative path
                string projectPath = Path.GetFullPath(Application.dataPath + "/..");
                path = path.Replace(projectPath + "/", "");

                // Load the pattern
                LoadPatternByPath(path);

                // Refresh the pattern list
                FindExistingPatterns(path);
            }
        }

        /// <summary>
        /// Saves the current pattern
        /// </summary>
        public void SavePattern(LevelingAlgorithmBase algorithmData, bool isEditingExistingPattern, string currentPatternPath)
        {
            // Ensure the algorithm data is valid
            algorithmData.Validate();

            string path;

            if (isEditingExistingPattern && !string.IsNullOrEmpty(currentPatternPath))
            {
                // Use the existing path
                path = currentPatternPath;

                // Confirm overwrite
                if (!EditorUtility.DisplayDialog("Save Changes",
                    $"Do you want to save changes to '{Path.GetFileNameWithoutExtension(path)}'?",
                    "Save", "Cancel"))
                {
                    return;
                }
            }
            else
            {
                // Show a save file dialog
                path = EditorUtility.SaveFilePanelInProject(
                    "Save Pattern",
                    algorithmData.algorithmName,
                    "asset",
                    "Save pattern as",
                    "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms");

                if (string.IsNullOrEmpty(path))
                {
                    return;
                }
            }

            try
            {
                // Create the directory if it doesn't exist
                string directory = Path.GetDirectoryName(path);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Pre-calculate points for the algorithm
                algorithmData.PreCalculatePoints();

                // Save the asset
                AssetDatabase.CreateAsset(algorithmData, path);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                // Create a new instance for editing
                LevelingAlgorithmBase newInstance = ScriptableObject.Instantiate(AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path));
                newInstance.algorithmName = Path.GetFileNameWithoutExtension(path);

                // Create default settings to save
                PatternEditorSettings settings = new PatternEditorSettings();
                // Save editor settings
                SaveEditorSettings(path, settings);

                // Add the new algorithm to the AlgorithmDatabase
                AddToAlgorithmDatabase(path);

                // Show a success message
                EditorUtility.DisplayDialog("Pattern Saved",
                    $"Pattern '{newInstance.Name}' has been saved successfully.", "OK");

                // Notify listeners
                OnPatternSaved?.Invoke(newInstance, path);

                // Refresh the pattern list
                FindExistingPatterns(path);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving pattern: {ex.Message}");
                EditorUtility.DisplayDialog("Save Error", $"Failed to save pattern: {ex.Message}", "OK");
            }
        }

        /// <summary>
        /// Adds the algorithm to the AlgorithmDatabase
        /// </summary>
        private void AddToAlgorithmDatabase(string path)
        {
            try
            {
                // Load the algorithm
                LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);
                if (algorithm == null)
                {
                    Debug.LogError($"Failed to load algorithm at path: {path}");
                    return;
                }

                // Load the algorithm database
                string databasePath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset";
                AlgorithmDatabase database = AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(databasePath);

                if (database == null)
                {
                    Debug.LogError("Algorithm database not found at path: " + databasePath);
                    return;
                }

                // Check if the algorithm is already in the database
                bool alreadyExists = false;
                foreach (var existingAlgo in database.algorithms)
                {
                    if (existingAlgo == algorithm)
                    {
                        alreadyExists = true;
                        break;
                    }
                }

                // Add the algorithm to the database if it's not already there
                if (!alreadyExists)
                {
                    database.AddAlgorithm(algorithm);
                    EditorUtility.SetDirty(database);
                    AssetDatabase.SaveAssets();
                    // Debug.Log($"Added algorithm '{algorithm.Name}' to the AlgorithmDatabase");

                    // Reinitialize the registry to pick up the new algorithm
                    ScriptableAlgorithmRegistry.Initialize();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error adding algorithm to database: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds all coded algorithm types in the project
        /// </summary>
        public List<Type> FindCodedAlgorithmTypes()
        {
            var result = new List<Type>();

            // Directly check for our sample algorithms
            try
            {
                // Try to get the SampleAlgorithm type directly
                Type sampleType = Type.GetType("SampleAlgorithm, Assembly-CSharp");
                if (sampleType != null)
                {
                    Debug.Log($"Found SampleAlgorithm directly: {sampleType.FullName}");
                    result.Add(sampleType);
                }
                else
                {
                    Debug.LogWarning("Could not find SampleAlgorithm directly");
                }

                // Try to get the WaveAlgorithm type directly
                Type waveType = Type.GetType("WaveAlgorithm, Assembly-CSharp");
                if (waveType != null)
                {
                    Debug.Log($"Found WaveAlgorithm directly: {waveType.FullName}");
                    result.Add(waveType);
                }
                else
                {
                    Debug.LogWarning("Could not find WaveAlgorithm directly");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error finding sample algorithms directly: {ex.Message}");
            }

            // Get all assemblies in the current domain
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();

            // Specifically check the Assembly-CSharp assembly
            var mainAssembly = assemblies.FirstOrDefault(a => a.GetName().Name == "Assembly-CSharp");
            if (mainAssembly != null)
            {
                Debug.Log($"Found Assembly-CSharp: {mainAssembly.FullName}");
                try
                {
                    // Get all types in the assembly
                    var types = mainAssembly.GetTypes();
                    Debug.Log($"Found {types.Length} types in Assembly-CSharp");

                    // Find the ILevelingAlgorithm interface
                    Type interfaceType = types.FirstOrDefault(t => t.Name == "ILevelingAlgorithm");
                    if (interfaceType != null)
                    {
                        Debug.Log($"Found ILevelingAlgorithm interface: {interfaceType.FullName}");

                        // Look for types that implement ILevelingAlgorithm
                        foreach (var type in types)
                        {
                            if (type != interfaceType && interfaceType.IsAssignableFrom(type) && !type.IsAbstract && !type.IsInterface)
                            {
                                // Check if it's a ScriptableObject
                                Type scriptableObjectType = types.FirstOrDefault(t => t.FullName == "UnityEngine.ScriptableObject");
                                bool isScriptableObject = scriptableObjectType != null && scriptableObjectType.IsAssignableFrom(type);

                                Debug.Log($"Found type implementing ILevelingAlgorithm: {type.FullName}, IsScriptableObject: {isScriptableObject}");

                                if (!isScriptableObject && !result.Contains(type))
                                {
                                    Debug.Log($"Adding non-ScriptableObject algorithm: {type.FullName}");
                                    result.Add(type);
                                }
                            }
                        }
                    }
                    else
                    {
                        Debug.LogWarning("Could not find ILevelingAlgorithm interface in Assembly-CSharp");
                    }

                    // Find the LevelingAlgorithmBase type
                    Type baseType = types.FirstOrDefault(t => t.Name == "LevelingAlgorithmBase");
                    if (baseType != null)
                    {
                        Debug.Log($"Found LevelingAlgorithmBase: {baseType.FullName}");

                        // Look for types that inherit from LevelingAlgorithmBase
                        foreach (var type in types)
                        {
                            if (type != baseType && baseType.IsAssignableFrom(type) && !type.IsAbstract)
                            {
                                Debug.Log($"Found algorithm inheriting from LevelingAlgorithmBase: {type.FullName}");
                                if (!result.Contains(type))
                                {
                                    result.Add(type);
                                }
                            }
                        }
                    }
                    else
                    {
                        Debug.LogWarning("Could not find LevelingAlgorithmBase in Assembly-CSharp");

                        // Fallback to direct name check
                        foreach (var type in types)
                        {
                            if (type.Name == "SampleAlgorithm" || type.Name == "WaveAlgorithm")
                            {
                                Debug.Log($"Found algorithm by name in Assembly-CSharp: {type.FullName}, Namespace: {type.Namespace}");
                                if (!result.Contains(type))
                                {
                                    result.Add(type);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error processing Assembly-CSharp: {ex.Message}");
                }
            }
            else
            {
                Debug.LogWarning("Could not find Assembly-CSharp assembly");
            }

            foreach (var assembly in assemblies)
            {
                try
                {
                    // Skip system assemblies
                    if (assembly.FullName.StartsWith("System.") ||
                        assembly.FullName.StartsWith("UnityEngine.") ||
                        assembly.FullName.StartsWith("UnityEditor."))
                    {
                        continue;
                    }

                    // Debug assembly name
                    Debug.Log($"Checking assembly: {assembly.FullName}");

                    // Get all types in the assembly
                    var types = assembly.GetTypes();

                    foreach (var type in types)
                    {
                        try
                        {
                            // Check if the type implements ILevelingAlgorithm but is not a ScriptableObject
                            if (typeof(ILevelingAlgorithm).IsAssignableFrom(type))
                            {
                                // Debug type info
                                Debug.Log($"Found ILevelingAlgorithm type: {type.FullName}, IsScriptableObject: {typeof(ScriptableObject).IsAssignableFrom(type)}, IsInterface: {type.IsInterface}, IsAbstract: {type.IsAbstract}");

                                if (!typeof(ScriptableObject).IsAssignableFrom(type) &&
                                    !type.IsInterface &&
                                    !type.IsAbstract)
                                {
                                    Debug.Log($"Adding algorithm type: {type.FullName}");
                                    result.Add(type);
                                }
                            }
                        }
                        catch (Exception)
                        {
                            // Skip types that cause errors
                        }
                    }
                }
                catch (Exception)
                {
                    // Skip assemblies that cause errors
                }
            }

            return result;
        }

        /// <summary>
        /// Samples points from an algorithm to create a detailed point representation
        /// </summary>
        private List<Vector2> SamplePointsFromAlgorithm(ILevelingAlgorithm algorithm, int numPoints = 20, int startingLevel = 1, int maxLevel = 50)
        {
            // Pre-calculate points to ensure we have data
            algorithm.PreCalculatePoints();

            // Use a neutral multiplier to get the raw algorithm shape
            float levelUpMultiplier = 1.0f;

            // Get raw formula values
            var rawValues = algorithm.CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);

            // Find min/max values to ensure we capture the full range
            float minValue = float.MaxValue;
            float maxValue = float.MinValue;

            foreach (var value in rawValues)
            {
                minValue = Mathf.Min(minValue, value);
                maxValue = Mathf.Max(maxValue, value);
            }

            // Ensure we have a reasonable range for visualization
            // If the algorithm has very small variations, amplify them
            float valueRange = maxValue - minValue;
            if (valueRange < 0.5f)
            {
                // Amplify small variations to make them more visible
                float amplificationFactor = 0.5f / valueRange;
                for (int i = 0; i < rawValues.Count; i++)
                {
                    // Center around the midpoint and amplify
                    float midpoint = (minValue + maxValue) / 2;
                    rawValues[i] = midpoint + (rawValues[i] - midpoint) * amplificationFactor;
                }
            }

            // Create points with actual level numbers for X values
            List<Vector2> levelPoints = new List<Vector2>();

            // Sample evenly across the range
            for (int i = 0; i < numPoints; i++)
            {
                float normalizedPosition = (float)i / (numPoints - 1); // 0 to 1
                int level = Mathf.RoundToInt(startingLevel + normalizedPosition * (maxLevel - startingLevel));

                // Ensure level is within bounds
                level = Mathf.Clamp(level, startingLevel, maxLevel);

                // Get the index in the raw values array
                int index = level - startingLevel;
                if (index >= 0 && index < rawValues.Count)
                {
                    // Use actual level number for X value
                    levelPoints.Add(new Vector2(level, rawValues[index]));
                }
            }

            // Ensure we have points at the extremes of the range
            if (levelPoints.Count > 0)
            {
                // Make sure first point is at startingLevel
                levelPoints[0] = new Vector2(startingLevel, rawValues[0]);

                // Make sure last point is at maxLevel
                levelPoints[levelPoints.Count - 1] = new Vector2(maxLevel, rawValues[rawValues.Count - 1]);
            }

            return levelPoints;
        }

        /// <summary>
        /// Converts a coded algorithm to a scriptable object
        /// </summary>
        private LevelingAlgorithmBase ConvertCodedAlgorithmToScriptableObject(Type algorithmType)
        {
            try
            {
                // Create an instance of the coded algorithm
                var instance = Activator.CreateInstance(algorithmType) as ILevelingAlgorithm;

                if (instance == null)
                {
                    Debug.LogError($"Failed to create instance of {algorithmType.Name}");
                    return null;
                }

                // Create a scriptable object to hold the algorithm
                var scriptableObject = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();

                // Set properties from the coded algorithm
                scriptableObject.algorithmName = instance.Name;
                scriptableObject.description = instance.Description;
                scriptableObject.formulaExplanation = instance.FormulaExplanation + "\n\nConverted from coded algorithm to drawn pattern.";
                scriptableObject.difficultyRating = instance.DifficultyRating;
                scriptableObject.algorithmType = AlgorithmEnums.AlgorithmType.DrawnPattern;

                // Sample points from the algorithm to create a detailed point representation
                // Use 40 points for better accuracy, with a configurable level range (1-50 by default)
                var points = SamplePointsFromAlgorithm(instance, 40, 1, 50);

                // Convert the points to the format expected by LevelingAlgorithm
                scriptableObject.points = new List<Vector2>(points);

                // Set cubic interpolation for smoother curves
                scriptableObject.interpolationMethod = AlgorithmEnums.InterpolationMethod.Cubic;

                // Save the scriptable object as an asset in a Converted subfolder
                string assetPath = $"Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms/Converted/{algorithmType.Name}.asset";

                // Ensure the directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    AssetDatabase.Refresh();
                }

                // Check if the asset already exists
                var existingAsset = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(assetPath);
                if (existingAsset != null)
                {
                    // Update the existing asset
                    existingAsset.algorithmName = scriptableObject.algorithmName;
                    existingAsset.description = scriptableObject.description;
                    existingAsset.formulaExplanation = scriptableObject.formulaExplanation;
                    existingAsset.difficultyRating = scriptableObject.difficultyRating;
                    existingAsset.points = scriptableObject.points;
                    existingAsset.interpolationMethod = scriptableObject.interpolationMethod;

                    EditorUtility.SetDirty(existingAsset);

                    // Pre-calculate points
                    existingAsset.PreCalculatePoints();

                    return existingAsset;
                }
                else
                {
                    // Create a new asset
                    AssetDatabase.CreateAsset(scriptableObject, assetPath);

                    // Pre-calculate points
                    scriptableObject.PreCalculatePoints();

                    return scriptableObject;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error converting {algorithmType.Name} to scriptable object: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Finds all coded algorithm types in the project and converts them to scriptable objects
        /// </summary>
        public void ConvertAllCodedAlgorithms()
        {
            // Find all types that implement ILevelingAlgorithm but are not ScriptableObjects
            var algorithmTypes = FindCodedAlgorithmTypes();

            if (algorithmTypes.Count == 0)
            {
                EditorUtility.DisplayDialog("Convert Coded Algorithms", "No coded algorithms found to convert.", "OK");
                return;
            }

            // Debug.Log($"Found {algorithmTypes.Count} coded algorithm types to convert.");

            // Create the Resources/Algorithms directory if it doesn't exist
            string resourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
            string algorithmsPath = $"{resourcesPath}/Algorithms";
            string convertedPath = $"{algorithmsPath}/Converted";

            if (!Directory.Exists(algorithmsPath))
            {
                Directory.CreateDirectory(algorithmsPath);
                AssetDatabase.Refresh();
            }

            if (!Directory.Exists(convertedPath))
            {
                Directory.CreateDirectory(convertedPath);
                AssetDatabase.Refresh();
            }

            // Load the algorithm database
            string databasePath = $"{resourcesPath}/AlgorithmDatabase.asset";
            AlgorithmDatabase database = AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(databasePath);

            if (database == null)
            {
                EditorUtility.DisplayDialog("Convert Coded Algorithms", "Algorithm database not found. Please create it first.", "OK");
                return;
            }

            // Convert each algorithm type to a scriptable object
            int convertedCount = 0;
            int duplicateCount = 0;
            List<string> convertedAlgorithms = new List<string>();

            // Show progress dialog
            EditorUtility.DisplayProgressBar("Converting Algorithms", "Preparing to convert algorithms...", 0f);

            try
            {
                for (int i = 0; i < algorithmTypes.Count; i++)
                {
                    var type = algorithmTypes[i];

                    // Update progress
                    float progress = (float)i / algorithmTypes.Count;
                    EditorUtility.DisplayProgressBar("Converting Algorithms", $"Converting {type.Name}...", progress);

                    try
                    {
                        // Create a scriptable object for the algorithm
                        var scriptableObject = ConvertCodedAlgorithmToScriptableObject(type);

                        if (scriptableObject != null)
                        {
                            // Check if algorithm with same name already exists
                            bool isDuplicate = database.algorithms.Any(a => a != null && a.Name == scriptableObject.Name);

                            // Add to database if not already present
                            if (!database.algorithms.Contains(scriptableObject))
                            {
                                database.AddAlgorithm(scriptableObject);
                                EditorUtility.SetDirty(database);

                                if (isDuplicate)
                                {
                                    duplicateCount++;
                                    convertedAlgorithms.Add($"{scriptableObject.Name} (duplicate)");
                                }
                                else
                                {
                                    convertedCount++;
                                    convertedAlgorithms.Add(scriptableObject.Name);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Error converting algorithm type {type.Name}: {ex.Message}");
                    }
                }
            }
            finally
            {
                // Clear progress bar
                EditorUtility.ClearProgressBar();
            }

            // Save changes
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Show detailed results with list of converted algorithms
            string resultMessage = $"Successfully converted {convertedCount} coded algorithms to scriptable objects.";

            if (duplicateCount > 0)
            {
                resultMessage += $"\n\n{duplicateCount} duplicate algorithm(s) were found and highlighted.";
            }

            if (convertedAlgorithms.Count > 0)
            {
                resultMessage += "\n\nConverted algorithms:";
                foreach (var name in convertedAlgorithms)
                {
                    resultMessage += $"\n• {name}";
                }
            }

            // Ask if the user wants to remove the old coded algorithm implementations
            bool removeOldImplementations = EditorUtility.DisplayDialog(
                "Convert Coded Algorithms",
                resultMessage + "\n\nDo you want to remove the old coded algorithm implementations?",
                "Yes, Remove Old Implementations",
                "No, Keep Both");

            if (removeOldImplementations)
            {
                RemoveOldCodedAlgorithmImplementations(algorithmTypes);
            }

            // Refresh the pattern list
            FindExistingPatterns();
        }

        /// <summary>
        /// Removes the old coded algorithm implementations
        /// </summary>
        private void RemoveOldCodedAlgorithmImplementations(List<Type> algorithmTypes)
        {
            if (algorithmTypes == null || algorithmTypes.Count == 0)
                return;

            try
            {
                // Show progress dialog
                EditorUtility.DisplayProgressBar("Removing Old Implementations", "Preparing to remove old implementations...", 0f);

                List<string> removedFiles = new List<string>();

                // Find the script files for each algorithm type
                for (int i = 0; i < algorithmTypes.Count; i++)
                {
                    var type = algorithmTypes[i];

                    // Update progress
                    float progress = (float)i / algorithmTypes.Count;
                    EditorUtility.DisplayProgressBar("Removing Old Implementations", $"Removing {type.Name}...", progress);

                    try
                    {
                        // Find the script file for this type
                        string[] guids = AssetDatabase.FindAssets(type.Name);

                        foreach (string guid in guids)
                        {
                            string path = AssetDatabase.GUIDToAssetPath(guid);

                            // Only delete .cs files that match the algorithm name
                            if (path.EndsWith(".cs") && Path.GetFileNameWithoutExtension(path) == type.Name)
                            {
                                // Delete the file
                                if (File.Exists(path))
                                {
                                    File.Delete(path);
                                    removedFiles.Add(path);
                                }

                                // Delete the meta file
                                string metaPath = path + ".meta";
                                if (File.Exists(metaPath))
                                {
                                    File.Delete(metaPath);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Error removing algorithm type {type.Name}: {ex.Message}");
                    }
                }

                // Refresh the asset database
                AssetDatabase.Refresh();

                // Show results
                string resultMessage = $"Successfully removed {removedFiles.Count} coded algorithm implementations.";

                if (removedFiles.Count > 0)
                {
                    resultMessage += "\n\nRemoved files:";
                    foreach (var path in removedFiles)
                    {
                        resultMessage += $"\n• {path}";
                    }
                }

                EditorUtility.DisplayDialog("Remove Old Implementations", resultMessage, "OK");
            }
            finally
            {
                // Clear progress bar
                EditorUtility.ClearProgressBar();
            }
        }

        /// <summary>
        /// Saves editor settings for a pattern
        /// </summary>
        public void SaveEditorSettings(string assetPath, PatternEditorSettings settings)
        {
            try
            {
                // Convert to JSON
                string settingsJson = JsonUtility.ToJson(settings);

                // Get the GUID for the asset
                string guid = AssetDatabase.AssetPathToGUID(assetPath);
                string settingsKey = $"AlgorithmDesigner_{guid}_Settings";

                // Save to EditorPrefs
                EditorPrefs.SetString(settingsKey, settingsJson);
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to save pattern settings: {ex.Message}");
            }
        }

        // Events for settings
        public event Action<PatternEditorSettings> OnSettingsLoaded;
        public event Action<LevelingAlgorithmBase, string> OnPatternSaved;

        /// <summary>
        /// Class to store pattern editor settings
        /// </summary>
        [Serializable]
        public class PatternEditorSettings
        {
            public float yAxisScale = 1.0f;
            public float yAxisMin = -5.0f;
            public float yAxisMax = 5.0f;
            public bool onePointPerLevel = false;
            public bool autoAdjustPoints = true;
            public bool showGridLines = true;
            public bool showPointValues = true;
            public bool useCustomColors = false;
            public Color customCurveColor = new Color(0.2f, 0.6f, 1.0f, 1.0f);
        }
    }
}
