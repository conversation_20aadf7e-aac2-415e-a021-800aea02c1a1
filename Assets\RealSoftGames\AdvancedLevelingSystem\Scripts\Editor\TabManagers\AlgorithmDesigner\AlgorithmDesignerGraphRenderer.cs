using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

// The namespace doesn't match the folder structure, but we're keeping it consistent with the rest of the codebase
namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Handles rendering of the algorithm designer graph
    /// </summary>
    public class AlgorithmDesignerGraphRenderer
    {
        // Graph settings
        private Rect graphRect;
        private readonly float graphPadding = 80f;
        private readonly Color gridColor = new(0.5f, 0.5f, 0.5f, 0.3f);
        private readonly Color curveColor = new(0.2f, 0.6f, 1.0f, 1.0f);
        private readonly Color pointColor = new(1.0f, 0.5f, 0.2f, 1.0f);
        private readonly Color selectedPointColor = new(1.0f, 0.2f, 0.2f, 1.0f);
        private readonly float pointSize = 10f;

        // Display options
        private bool showGridLines = true;
        private bool showPointValues = true;
        private bool useCustomColors = false;
        private Color customCurveColor = new(0.2f, 0.6f, 1.0f, 1.0f);

        // Y-axis range (default to -5 to +5 for more distinct patterns)
        private float yAxisMin = -5.0f;
        private float yAxisMax = 5.0f;

        public AlgorithmDesignerGraphRenderer()
        {
        }

        /// <summary>
        /// Sets the graph display options
        /// </summary>
        public void SetDisplayOptions(bool showGrid, bool showValues, bool useCustom, Color customColor)
        {
            showGridLines = showGrid;
            showPointValues = showValues;
            useCustomColors = useCustom;
            customCurveColor = customColor;
        }

        /// <summary>
        /// Sets the Y-axis range
        /// </summary>
        public void SetYAxisRange(float min, float max)
        {
            yAxisMin = min;
            yAxisMax = max;
        }

        /// <summary>
        /// Draws the complete graph with all elements
        /// </summary>
        public void DrawGraph(Rect rect, List<Vector2> points, List<Vector2> sortedPoints, int selectedPointIndex)
        {
            graphRect = rect;

            // Draw background
            EditorGUI.DrawRect(graphRect, new Color(0.2f, 0.2f, 0.2f, 1));

            // Calculate the inner graph area
            Rect innerRect = new(
                graphRect.x + graphPadding,
                graphRect.y + graphPadding,
                graphRect.width - (2 * graphPadding),
                graphRect.height - (2 * graphPadding)
            );

            // Draw grid
            DrawGrid(innerRect);

            // Draw axes
            DrawAxes(innerRect);

            // Draw curve
            DrawCurve(innerRect, sortedPoints);

            // Draw points
            DrawPoints(innerRect, points, selectedPointIndex);
        }

        /// <summary>
        /// Draws the grid lines
        /// </summary>
        private void DrawGrid(Rect rect)
        {
            if (!showGridLines)
                return;

            // Draw grid lines
            Handles.color = gridColor;

            // Vertical grid lines (X-axis)
            for (int i = 0; i <= 10; i++)
            {
                float x = rect.x + i / 10f * rect.width;
                Handles.DrawLine(new Vector3(x, rect.y, 0), new Vector3(x, rect.y + rect.height, 0));
            }

            // Horizontal grid lines (Y-axis)
            for (int i = 0; i <= 10; i++)
            {
                float y = rect.y + i / 10f * rect.height;
                Handles.DrawLine(new Vector3(rect.x, y, 0), new Vector3(rect.x + rect.width, y, 0));
            }
        }

        // Level range for X-axis labels
        private int minLevel = 1;
        private int maxLevel = 50;

        /// <summary>
        /// Sets the level range for X-axis labels
        /// </summary>
        public void SetLevelRange(int min, int max)
        {
            minLevel = min;
            maxLevel = max;
        }

        // Flag to track if we're using level-based X values
        private bool usesLevelBasedX = false;

        /// <summary>
        /// Sets whether to use level-based X values
        /// </summary>
        public void SetUseLevelBasedX(bool useLevelBased)
        {
            usesLevelBasedX = useLevelBased;
        }

        /// <summary>
        /// Draws the X and Y axes with labels
        /// </summary>
        private void DrawAxes(Rect rect)
        {
            // Draw axes
            Handles.color = Color.white;

            // X-axis labels
            for (int i = 0; i <= 10; i++)
            {
                float x = rect.x + i / 10f * rect.width;
                float normalizedPosition = i / 10f;

                // Determine label value based on whether we're using level-based X or normalized X
                string label;
                if (usesLevelBasedX)
                {
                    // Calculate the level at this position
                    int level = Mathf.RoundToInt(minLevel + normalizedPosition * (maxLevel - minLevel));
                    label = level.ToString();
                }
                else
                {
                    // Use normalized value (0-1)
                    label = normalizedPosition.ToString("F1");
                }

                // Draw tick mark
                Handles.DrawLine(new Vector3(x, rect.y + rect.height, 0), new Vector3(x, rect.y + rect.height + 5, 0));

                // Draw label
                GUI.color = Color.white;
                GUI.Label(new Rect(x - 15, rect.y + rect.height + 5, 30, 20), label);
            }

            // Y-axis labels
            for (int i = 0; i <= 10; i++)
            {
                float normalizedY = i / 10f;
                float y = rect.y + (1 - normalizedY) * rect.height; // Invert Y-axis
                float labelValue = yAxisMin + normalizedY * (yAxisMax - yAxisMin);
                string label = labelValue.ToString("F1");

                // Draw tick mark
                Handles.DrawLine(new Vector3(rect.x, y, 0), new Vector3(rect.x - 5, y, 0));

                // Draw label
                GUI.color = Color.white;
                GUI.Label(new Rect(rect.x - 50, y - 10, 45, 20), label);
            }

            // X-axis title
            GUI.color = Color.white;
            GUI.Label(new Rect(rect.x + rect.width / 2 - 50, rect.y + rect.height + 25, 100, 20), "Level Progress");

            // Y-axis title
            GUIUtility.RotateAroundPivot(-90, new Vector2(rect.x - 60, rect.y + rect.height / 2));
            GUI.Label(new Rect(rect.x - 60, rect.y + rect.height / 2 - 50, 100, 20), "Multiplier Value");
            GUI.matrix = Matrix4x4.identity; // Reset rotation
        }

        /// <summary>
        /// Draws the curve connecting the points
        /// </summary>
        private void DrawCurve(Rect rect, List<Vector2> sortedPoints)
        {
            if (sortedPoints.Count < 2)
                return;

            // Set curve color
            if (useCustomColors)
            {
                Handles.color = customCurveColor;
            }
            else
            {
                Handles.color = curveColor;
            }

            // Draw lines connecting the points
            for (int i = 0; i < sortedPoints.Count - 1; i++)
            {
                Vector2 start = ConvertToScreenSpace(sortedPoints[i], rect);
                Vector2 end = ConvertToScreenSpace(sortedPoints[i + 1], rect);
                Handles.DrawLine(start, end);
            }
        }

        /// <summary>
        /// Draws the points on the graph
        /// </summary>
        private void DrawPoints(Rect rect, List<Vector2> points, int selectedPointIndex)
        {
            for (int i = 0; i < points.Count; i++)
            {
                Vector2 screenPoint = ConvertToScreenSpace(points[i], rect);

                // Draw the point
                if (useCustomColors)
                {
                    Handles.color = (i == selectedPointIndex) ? Color.red : customCurveColor;
                }
                else
                {
                    Handles.color = (i == selectedPointIndex) ? selectedPointColor : pointColor;
                }

                Handles.DrawSolidDisc(screenPoint, Vector3.forward, pointSize);

                // Add cursor rect to change cursor when hovering over points
                Rect pointRect = new(screenPoint.x - pointSize, screenPoint.y - pointSize, pointSize * 2, pointSize * 2);
                EditorGUIUtility.AddCursorRect(pointRect, MouseCursor.MoveArrow);

                // Draw point value if enabled
                if (showPointValues)
                {
                    GUI.color = Color.white;
                    string valueText = points[i].y.ToString("F2");
                    GUI.Label(new Rect(screenPoint.x - 20, screenPoint.y - 25, 40, 20), valueText);
                }
            }

            // Add cursor rect for the graph area to indicate you can add points
            if (Event.current.type == EventType.Repaint)
            {
                EditorGUIUtility.AddCursorRect(rect, MouseCursor.Arrow);
            }
        }

        /// <summary>
        /// Draws the coordinate editor popup for a selected point
        /// </summary>
        public void DrawCoordinateEditor(Rect rect, Vector2 point, System.Action<float, float> onCoordinateChanged)
        {
            // Draw background
            EditorGUI.DrawRect(rect, new Color(0.2f, 0.2f, 0.2f, 0.9f));

            // Draw border
            Rect borderRect = new(rect.x - 1, rect.y - 1, rect.width + 2, rect.height + 2);
            EditorGUI.DrawRect(borderRect, new Color(0.5f, 0.5f, 0.5f, 0.8f));

            // Create a GUIStyle for labels
            GUIStyle labelStyle = new(EditorStyles.label)
            {
                normal = { textColor = Color.white },
                fontSize = 10
            };

            // Create a GUIStyle for input fields
            GUIStyle fieldStyle = new(EditorStyles.textField)
            {
                fontSize = 10,
                normal = { textColor = Color.white, background = EditorGUIUtility.whiteTexture },
                focused = { textColor = Color.white, background = EditorGUIUtility.whiteTexture }
            };

            // Create a GUIStyle for help text
            GUIStyle helpStyle = new(EditorStyles.label)
            {
                normal = { textColor = new Color(0.8f, 0.8f, 0.5f) },
                fontSize = 9,
                wordWrap = true
            };

            // Draw title
            GUI.Label(new Rect(rect.x + 5, rect.y + 5, rect.width - 10, 20), "Point Coordinates", labelStyle);

            // Draw X coordinate field
            GUI.Label(new Rect(rect.x + 5, rect.y + 25, 15, 15), "X:", labelStyle);

            // Use the current point values
            string xCoordText = point.x.ToString("F4");
            string yCoordText = point.y.ToString("F4");

            // Create a focused text field style
            GUIStyle focusedFieldStyle = new(fieldStyle)
            {
                normal = { background = EditorGUIUtility.whiteTexture },
                focused = { background = EditorGUIUtility.whiteTexture }
            };

            // Handle X coordinate input with better focus handling
            GUI.SetNextControlName("XCoordField");
            string newXText = EditorGUI.TextField(new Rect(rect.x + 20, rect.y + 25, 50, 15), xCoordText, focusedFieldStyle);
            if (float.TryParse(newXText, out float newX) && newXText != xCoordText)
            {
                // Check if we're using level-based X values
                if (point.x > 1.5f) // If X is clearly above 1, we're using level-based X
                {
                    // Clamp to level range
                    newX = Mathf.Clamp(newX, minLevel, maxLevel);
                }
                else
                {
                    // Clamp X to 0-1 range for normalized values
                    newX = Mathf.Clamp01(newX);
                }

                // Update the point
                onCoordinateChanged(newX, point.y);
            }

            // Draw Y coordinate field
            GUI.Label(new Rect(rect.x + 75, rect.y + 25, 15, 15), "Y:", labelStyle);

            // Handle Y coordinate input with better focus handling
            GUI.SetNextControlName("YCoordField");
            string newYText = EditorGUI.TextField(new Rect(rect.x + 90, rect.y + 25, 50, 15), yCoordText, focusedFieldStyle);
            if (float.TryParse(newYText, out float newY) && newYText != yCoordText)
            {
                // Clamp Y to the current Y-axis range
                newY = Mathf.Clamp(newY, yAxisMin, yAxisMax);

                // Update the point
                onCoordinateChanged(point.x, newY);
            }

            // Draw apply button
            if (GUI.Button(new Rect(rect.x + 5, rect.y + 45, rect.width - 10, 15), "Apply", EditorStyles.miniButton))
            {
                // Parse and apply both values
                if (float.TryParse(newXText, out newX) && float.TryParse(newYText, out newY))
                {
                    // Clamp values to valid ranges
                    if (point.x > 1.5f) // If X is clearly above 1, we're using level-based X
                    {
                        // Clamp to level range
                        newX = Mathf.Clamp(newX, minLevel, maxLevel);
                    }
                    else
                    {
                        // Clamp X to 0-1 range for normalized values
                        newX = Mathf.Clamp01(newX);
                    }
                    newY = Mathf.Clamp(newY, yAxisMin, yAxisMax);

                    // Update the point
                    onCoordinateChanged(newX, newY);
                }
            }

            // Add help text about negative values
            string helpText = "Negative Y values create levels that require LESS experience than the previous level.";
            GUI.Label(new Rect(rect.x + 5, rect.y + 65, rect.width - 10, 40), helpText, helpStyle);

            // Try to focus the text field if it was just clicked
            if (Event.current.type == EventType.MouseDown &&
                new Rect(rect.x + 20, rect.y + 25, 50, 15).Contains(Event.current.mousePosition))
            {
                GUI.FocusControl("XCoordField");
                Event.current.Use();
            }
            else if (Event.current.type == EventType.MouseDown &&
                     new Rect(rect.x + 90, rect.y + 25, 50, 15).Contains(Event.current.mousePosition))
            {
                GUI.FocusControl("YCoordField");
                Event.current.Use();
            }
        }

        /// <summary>
        /// Converts a point from graph space to screen space
        /// </summary>
        public Vector2 ConvertToScreenSpace(Vector2 point, Rect rect)
        {
            // Calculate the normalized Y value (0 to 1)
            float normalizedY = Mathf.InverseLerp(yAxisMin, yAxisMax, point.y);

            // Calculate the normalized X value (0 to 1)
            float normalizedX;

            // Check if we're using level-based X values (significantly greater than 1)
            if (point.x > 1.5f) // If X is clearly above 1, we're using level-based X
            {
                // Normalize the level to 0-1 range
                normalizedX = Mathf.InverseLerp(minLevel, maxLevel, point.x);
            }
            else
            {
                // Already normalized
                normalizedX = point.x;
            }

            // Convert to screen space (invert Y-axis)
            float screenX = rect.x + normalizedX * rect.width;
            float screenY = rect.y + (1 - normalizedY) * rect.height;

            return new Vector2(screenX, screenY);
        }

        /// <summary>
        /// Converts a point from screen space to graph space
        /// </summary>
        public Vector2 ConvertToGraphSpace(Vector2 screenPoint, Rect rect)
        {
            // Calculate normalized coordinates (0 to 1)
            float normalizedX = Mathf.Clamp01((screenPoint.x - rect.x) / rect.width);
            float normalizedY = 1 - Mathf.Clamp01((screenPoint.y - rect.y) / rect.height);

            // Convert to graph space
            float x;

            // Use the class-level flag to determine if we should use level-based X values

            // If we're using level-based X, convert normalized X to level
            if (usesLevelBasedX)
            {
                x = Mathf.Lerp(minLevel, maxLevel, normalizedX);
            }
            else
            {
                // Keep as normalized value
                x = normalizedX;
            }

            float y = Mathf.Lerp(yAxisMin, yAxisMax, normalizedY);

            return new Vector2(x, y);
        }
    }
}
