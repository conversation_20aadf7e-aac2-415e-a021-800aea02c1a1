using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Handles input events for the algorithm designer graph
    /// </summary>
    public class AlgorithmDesignerInputHandler
    {
        // Reference to the graph renderer for coordinate conversion
        private AlgorithmDesignerGraphRenderer graphRenderer;

        // Reference to the parent window for repainting
        private EditorWindow window;

        // Input state
        private int selectedPointIndex = -1;
        private bool isDragging = false;
        private float pointSize = 10f;

        // Y-axis range
        private float yAxisMin = -5.0f;
        private float yAxisMax = 5.0f;

        // Coordinate editor
        private Rect coordinateEditorRect;
        private string xCoordText = "";
        private string yCoordText = "";

        // Events
        public event Action<Vector2> OnPointAdded;
        public event Action<int> OnPointRemoved;
        public event Action<int, Vector2> OnPointMoved;
        public event Action<List<Vector2>> OnPointsReplaced;
        public event Action OnGraphClicked;

        public int SelectedPointIndex => selectedPointIndex;

        public AlgorithmDesignerInputHandler(EditorWindow window = null, AlgorithmDesignerGraphRenderer graphRenderer = null)
        {
            this.window = window;
            this.graphRenderer = graphRenderer;
        }

        /// <summary>
        /// Sets the graph renderer reference
        /// </summary>
        public void SetGraphRenderer(AlgorithmDesignerGraphRenderer renderer)
        {
            this.graphRenderer = renderer;
        }

        /// <summary>
        /// Sets the window reference
        /// </summary>
        public void SetWindow(EditorWindow window)
        {
            this.window = window;
        }

        /// <summary>
        /// Sets the Y-axis range
        /// </summary>
        public void SetYAxisRange(float min, float max)
        {
            yAxisMin = min;
            yAxisMax = max;
        }

        /// <summary>
        /// Handles input events for the graph
        /// </summary>
        public void HandleInputEvents(Rect innerRect, List<Vector2> points)
        {
            Event currentEvent = Event.current;
            Vector2 mousePosition = currentEvent.mousePosition;

            // Handle different event types
            switch (currentEvent.type)
            {
                case EventType.MouseDown:
                    // Check if we're clicking inside the coordinate editor
                    if (selectedPointIndex >= 0 && coordinateEditorRect.Contains(mousePosition))
                    {
                        // Don't handle the event here, let the text fields handle it
                        break;
                    }

                    if (innerRect.Contains(mousePosition))
                    {
                        if (currentEvent.button == 0) // Left mouse button
                        {
                            // Get a control ID for this operation
                            int controlID = GUIUtility.GetControlID(FocusType.Passive);
                            GUIUtility.hotControl = controlID;

                            // Check if we clicked on an existing point
                            selectedPointIndex = -1;
                            for (int i = 0; i < points.Count; i++)
                            {
                                Vector2 screenPoint = graphRenderer.ConvertToScreenSpace(points[i], innerRect);
                                if (Vector2.Distance(screenPoint, mousePosition) <= pointSize)
                                {
                                    selectedPointIndex = i;
                                    isDragging = true;
                                    break;
                                }
                            }

                            // If we didn't click on a point, add a new one
                            if (selectedPointIndex == -1)
                            {
                                // Convert mouse position to graph space
                                Vector2 newPoint = graphRenderer.ConvertToGraphSpace(mousePosition, innerRect);

                                // Add the new point
                                OnPointAdded?.Invoke(newPoint);

                                // Notify that the graph was clicked
                                OnGraphClicked?.Invoke();
                            }

                            // Use the event
                            currentEvent.Use();
                            window.Repaint();
                        }
                    }
                    break;

                case EventType.MouseUp:
                    if (isDragging)
                    {
                        isDragging = false;
                        GUIUtility.hotControl = 0;
                        currentEvent.Use();
                        window.Repaint();
                    }
                    break;

                case EventType.MouseDrag:
                    if (isDragging && selectedPointIndex >= 0 && selectedPointIndex < points.Count)
                    {
                        // Convert mouse position to graph space
                        Vector2 newPosition = graphRenderer.ConvertToGraphSpace(mousePosition, innerRect);

                        // Update the point position
                        OnPointMoved?.Invoke(selectedPointIndex, newPosition);

                        // Use the event
                        currentEvent.Use();
                        window.Repaint();
                    }
                    break;

                case EventType.MouseMove:
                    // Repaint on mouse move when dragging to ensure smooth updates
                    if (isDragging)
                    {
                        window.Repaint();
                    }
                    break;

                case EventType.ContextClick:
                    if (innerRect.Contains(mousePosition))
                    {
                        // Show context menu
                        GenericMenu menu = new GenericMenu();

                        // Check if we right-clicked on a point
                        int pointIndex = -1;
                        for (int i = 0; i < points.Count; i++)
                        {
                            Vector2 screenPoint = graphRenderer.ConvertToScreenSpace(points[i], innerRect);
                            if (Vector2.Distance(screenPoint, mousePosition) <= pointSize)
                            {
                                pointIndex = i;
                                break;
                            }
                        }

                        if (pointIndex >= 0)
                        {
                            // Point-specific options
                            menu.AddItem(new GUIContent("Remove Point"), false, () => {
                                OnPointRemoved?.Invoke(pointIndex);
                                window.Repaint();
                            });
                        }
                        else
                        {
                            // General options
                            menu.AddItem(new GUIContent("Add Point"), false, () => {
                                Vector2 newPoint = graphRenderer.ConvertToGraphSpace(mousePosition, innerRect);
                                OnPointAdded?.Invoke(newPoint);
                                window.Repaint();
                            });
                        }

                        menu.ShowAsContext();
                        currentEvent.Use();
                    }
                    break;

                case EventType.KeyDown:
                    if (currentEvent.keyCode == KeyCode.Delete && selectedPointIndex >= 0)
                    {
                        // Remove the selected point
                        OnPointRemoved?.Invoke(selectedPointIndex);
                        selectedPointIndex = -1;
                        currentEvent.Use();
                        window.Repaint();
                    }
                    break;
            }
        }

        /// <summary>
        /// Draws the coordinate editor for the selected point
        /// </summary>
        public void DrawCoordinateEditorForSelectedPoint(Rect innerRect, List<Vector2> points)
        {
            if (selectedPointIndex >= 0 && selectedPointIndex < points.Count)
            {
                Vector2 screenPoint = graphRenderer.ConvertToScreenSpace(points[selectedPointIndex], innerRect);

                // Position the coordinate editor below the point
                coordinateEditorRect = new Rect(
                    screenPoint.x - 75,
                    screenPoint.y + 15, // Changed from -70 to +15 to position below the point
                    150,
                    65
                );

                // Draw the coordinate editor
                graphRenderer.DrawCoordinateEditor(
                    coordinateEditorRect,
                    points[selectedPointIndex],
                    (newX, newY) => {
                        // Update the point
                        OnPointMoved?.Invoke(selectedPointIndex, new Vector2(newX, newY));
                        window.Repaint();
                    }
                );
            }
        }

        /// <summary>
        /// Clears the selection
        /// </summary>
        public void ClearSelection()
        {
            selectedPointIndex = -1;
            isDragging = false;
        }
    }
}
