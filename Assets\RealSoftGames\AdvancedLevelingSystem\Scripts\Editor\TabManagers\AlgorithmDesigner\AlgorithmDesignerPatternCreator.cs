using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using RealSoftGames.AdvancedLevelingSystem;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Handles pattern creation and manipulation for the algorithm designer
    /// </summary>
    public class AlgorithmDesignerPatternCreator
    {
        // Generated points
        private List<Vector2> generatedPoints = new List<Vector2>();

        // Y-axis range
        private float yAxisMin = -5.0f;
        private float yAxisMax = 5.0f;
        // Template types
        public enum CurveTemplate
        {
            None,
            Linear,
            Bell,
            SCurve,
            Exponential,
            Logarithmic,
            Sinusoidal,
            Heartbeat,
            Sawtooth,
            Step,
            Plateau
        }

        // Smoothing methods
        public enum SmoothingMethod
        {
            None,
            Light,
            Medium,
            Strong,
            Bezier,
            CatmullRom
        }

        // Template settings
        private CurveTemplate selectedTemplate = CurveTemplate.None;
        private SmoothingMethod selectedSmoothingMethod = SmoothingMethod.None;
        private float templateIntensity = 1.0f;
        private string mathematicalFormula = "1.1 + 0.05 * x";
        private bool showFormulaInput = false;
        private string formulaError = "";

        // Events
        public event Action<List<Vector2>> OnPointsGenerated;

        // Properties
        public CurveTemplate SelectedTemplate
        {
            get => selectedTemplate;
            set => selectedTemplate = value;
        }

        public SmoothingMethod SelectedSmoothingMethod
        {
            get => selectedSmoothingMethod;
            set => selectedSmoothingMethod = value;
        }

        public float TemplateIntensity
        {
            get => templateIntensity;
            set => templateIntensity = value;
        }

        public string MathematicalFormula
        {
            get => mathematicalFormula;
            set => mathematicalFormula = value;
        }

        public bool ShowFormulaInput
        {
            get => showFormulaInput;
            set => showFormulaInput = value;
        }

        public string FormulaError
        {
            get => formulaError;
            set => formulaError = value;
        }

        /// <summary>
        /// Sets the Y-axis range
        /// </summary>
        public void SetYAxisRange(float min, float max)
        {
            yAxisMin = min;
            yAxisMax = max;
        }

        /// <summary>
        /// Gets the generated points
        /// </summary>
        public List<Vector2> GetGeneratedPoints()
        {
            return generatedPoints;
        }

        /// <summary>
        /// Applies a template to generate points
        /// </summary>
        public void ApplyTemplate(CurveTemplate template, float intensity, int numPoints = 20)
        {
            if (template == CurveTemplate.None)
                return;

            // Clear the generated points
            generatedPoints.Clear();

            // Calculate padding to keep points within graph boundaries
            float padding = (yAxisMax - yAxisMin) * 0.1f; // 10% padding

            // Calculate the safe range for Y values to keep points within graph boundaries
            float safeMin = yAxisMin + padding;
            float safeMax = yAxisMax - padding;
            float safeRange = safeMax - safeMin;

            // Calculate the midpoint of the safe range
            float midY = (safeMin + safeMax) / 2.0f;

            // Generate points based on the selected template
            switch (template)
            {
                case CurveTemplate.Linear:
                    GenerateLinearPoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Bell:
                    GenerateBellCurvePoints(midY, safeRange, intensity, numPoints);
                    break;

                case CurveTemplate.SCurve:
                    GenerateSCurvePoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Exponential:
                    GenerateExponentialPoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Logarithmic:
                    GenerateLogarithmicPoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Sinusoidal:
                    GenerateSinusoidalPoints(midY, safeRange, intensity, numPoints);
                    break;

                case CurveTemplate.Heartbeat:
                    GenerateHeartbeatPoints(midY, safeRange, intensity, numPoints);
                    break;

                case CurveTemplate.Sawtooth:
                    GenerateSawtoothPoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Step:
                    GenerateStepPoints(safeMin, safeMax, intensity, numPoints);
                    break;

                case CurveTemplate.Plateau:
                    GeneratePlateauPoints(safeMin, safeMax, intensity, numPoints);
                    break;
            }
        }

        /// <summary>
        /// Applies a smoothing method to the points
        /// </summary>
        public void ApplySmoothingMethod(SmoothingMethod method, List<Vector2> points)
        {
            if (method == SmoothingMethod.None || points.Count < 3)
                return;

            // Sort points by X coordinate
            var sortedPoints = points.OrderBy(p => p.x).ToList();

            // Apply the selected smoothing method
            List<Vector2> smoothedPoints = new List<Vector2>();

            switch (method)
            {
                case SmoothingMethod.Light:
                    smoothedPoints = ApplySimpleSmoothing(sortedPoints, 0.3f);
                    break;

                case SmoothingMethod.Medium:
                    smoothedPoints = ApplySimpleSmoothing(sortedPoints, 0.5f);
                    break;

                case SmoothingMethod.Strong:
                    smoothedPoints = ApplySimpleSmoothing(sortedPoints, 0.7f);
                    break;

                case SmoothingMethod.Bezier:
                    smoothedPoints = ApplyBezierSmoothing(sortedPoints);
                    break;

                case SmoothingMethod.CatmullRom:
                    smoothedPoints = ApplyCatmullRomSmoothing(sortedPoints);
                    break;
            }

            // Notify listeners
            OnPointsGenerated?.Invoke(smoothedPoints);
        }

        /// <summary>
        /// Applies smoothing to a list of points based on the specified method
        /// </summary>
        public List<Vector2> ApplySmoothingToPoints(List<Vector2> points, SmoothingMethod method)
        {
            if (method == SmoothingMethod.None || points.Count < 3)
                return new List<Vector2>(points);

            // Sort points by X coordinate
            var sortedPoints = points.OrderBy(p => p.x).ToList();

            // Apply the selected smoothing method
            switch (method)
            {
                case SmoothingMethod.Light:
                    return ApplySimpleSmoothing(sortedPoints, 0.3f);
                case SmoothingMethod.Medium:
                    return ApplySimpleSmoothing(sortedPoints, 0.5f);
                case SmoothingMethod.Strong:
                    return ApplySimpleSmoothing(sortedPoints, 0.7f);
                case SmoothingMethod.Bezier:
                    return ApplyBezierSmoothing(sortedPoints);
                case SmoothingMethod.CatmullRom:
                    return ApplyCatmullRomSmoothing(sortedPoints);
                default:
                    return new List<Vector2>(sortedPoints);
            }
        }

        /// <summary>
        /// Applies a mathematical formula to generate points
        /// </summary>
        public void ApplyMathematicalFormula(string formula)
        {
            try
            {
                // Create a parser for the formula
                // This is a simplified implementation - in a real application, you would use a proper expression parser

                // For now, we'll just generate some points based on a simple formula
                List<Vector2> points = new List<Vector2>();

                for (int i = 0; i <= 20; i++)
                {
                    float x = i / 20f;
                    float y = EvaluateFormula(formula, x);

                    // Clamp Y to the valid range
                    y = Mathf.Clamp(y, yAxisMin, yAxisMax);

                    points.Add(new Vector2(x, y));
                }

                // Clear any previous error
                formulaError = "";

                // Notify listeners
                OnPointsGenerated?.Invoke(points);
            }
            catch (Exception ex)
            {
                // Set the error message
                formulaError = $"Error: {ex.Message}";
                Debug.LogError($"Error applying formula: {ex.Message}");
            }
        }

        /// <summary>
        /// Evaluates a mathematical formula for a given X value
        /// </summary>
        private float EvaluateFormula(string formula, float x)
        {
            // This is a simplified implementation - in a real application, you would use a proper expression parser
            // For now, we'll just support a few basic formulas

            // Replace x with the actual value
            string expr = formula.Replace("x", x.ToString());

            // Evaluate the expression
            // This is a very simplified approach - in a real application, you would use a proper expression evaluator
            if (expr == "1.1 + 0.05 * " + x.ToString())
            {
                return 1.1f + 0.05f * x;
            }
            else if (expr.Contains("sin"))
            {
                // Simple sine function
                return 1.1f + 0.2f * Mathf.Sin(x * 10f);
            }
            else if (expr.Contains("exp"))
            {
                // Simple exponential function
                return 1.1f + 0.1f * Mathf.Exp(x * 2f);
            }
            else if (expr.Contains("log"))
            {
                // Simple logarithmic function
                return 1.1f + 0.2f * Mathf.Log(x + 0.1f);
            }
            else
            {
                // Default linear function
                return 1.1f + 0.05f * x;
            }
        }

        /// <summary>
        /// Generates points for a linear curve
        /// </summary>
        private void GenerateLinearPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);
                float y = min + range * x;
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a bell curve
        /// </summary>
        private void GenerateBellCurvePoints(float midY, float range, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float adjustedRange = range * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Bell curve formula: y = a * e^(-(x-b)^2 / (2*c^2))
                // where a is the height, b is the center, and c is the width
                float a = adjustedRange;
                float b = 0.5f;
                float c = 0.2f;

                float y = midY + a * Mathf.Exp(-Mathf.Pow(x - b, 2) / (2 * Mathf.Pow(c, 2)));
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for an S-curve
        /// </summary>
        private void GenerateSCurvePoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // S-curve formula using sigmoid function: y = 1 / (1 + e^(-k*(x-0.5)))
                // where k controls the steepness
                float k = 10f * intensity;
                float sigmoid = 1f / (1f + Mathf.Exp(-k * (x - 0.5f)));

                float y = min + range * sigmoid;
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for an exponential curve
        /// </summary>
        private void GenerateExponentialPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Exponential formula: y = a * e^(b*x)
                // where a is the initial value and b is the growth rate
                float a = min;
                float b = 3f * intensity;

                float y = a + range * (Mathf.Exp(b * x) - 1) / (Mathf.Exp(b) - 1);
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a logarithmic curve
        /// </summary>
        private void GenerateLogarithmicPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Logarithmic formula: y = a + b * log(c*x + d)
                // where a, b, c, and d are constants
                float a = min;
                float b = range;
                float c = 10f;
                float d = 1f;

                float y = a + b * Mathf.Log10(c * x + d) / Mathf.Log10(c + d);
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a sinusoidal curve
        /// </summary>
        private void GenerateSinusoidalPoints(float midY, float range, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float adjustedRange = range * 0.5f * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Sinusoidal formula: y = a + b * sin(c*x)
                // where a is the midpoint, b is the amplitude, and c is the frequency
                float a = midY;
                float b = adjustedRange;
                float c = 2f * Mathf.PI;

                float y = a + b * Mathf.Sin(c * x);
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a heartbeat curve
        /// </summary>
        private void GenerateHeartbeatPoints(float midY, float range, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float adjustedRange = range * 0.5f * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Heartbeat formula: combination of sine waves
                float a = midY;
                float b = adjustedRange;

                // Create a heartbeat-like pattern with multiple sine waves
                float y = a + b * (
                    Mathf.Sin(2f * Mathf.PI * x) +
                    0.5f * Mathf.Sin(4f * Mathf.PI * x) +
                    0.25f * Mathf.Sin(8f * Mathf.PI * x)
                );

                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a sawtooth curve
        /// </summary>
        private void GenerateSawtoothPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Number of teeth
            int numTeeth = 3;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Sawtooth formula: y = a + b * (x * n - floor(x * n))
                // where a is the minimum, b is the range, and n is the number of teeth
                float a = min;
                float b = range;
                float n = numTeeth;

                float y = a + b * (x * n - Mathf.Floor(x * n));
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a step curve
        /// </summary>
        private void GenerateStepPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Number of steps
            int numSteps = 5;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Step formula: y = a + b * floor(x * n) / (n - 1)
                // where a is the minimum, b is the range, and n is the number of steps
                float a = min;
                float b = range;
                float n = numSteps;

                float y = a + b * Mathf.Floor(x * n) / (n - 1);
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Generates points for a plateau curve
        /// </summary>
        private void GeneratePlateauPoints(float min, float max, float intensity, int numPoints)
        {
            List<Vector2> points = new List<Vector2>();

            // Calculate the range based on intensity
            float range = (max - min) * intensity;

            // Generate points
            for (int i = 0; i < numPoints; i++)
            {
                float x = i / (float)(numPoints - 1);

                // Plateau formula: combination of sigmoid functions
                float k = 10f * intensity;
                float sigmoid1 = 1f / (1f + Mathf.Exp(-k * (x - 0.25f)));
                float sigmoid2 = 1f / (1f + Mathf.Exp(-k * (x - 0.75f)));

                float y = min + range * (sigmoid1 - sigmoid2);
                points.Add(new Vector2(x, y));
            }

            // Store the generated points
            generatedPoints = new List<Vector2>(points);

            // Notify listeners
            OnPointsGenerated?.Invoke(points);
        }

        /// <summary>
        /// Applies simple smoothing to the points
        /// </summary>
        private List<Vector2> ApplySimpleSmoothing(List<Vector2> inputPoints, float smoothingFactor)
        {
            if (inputPoints.Count < 3)
                return new List<Vector2>(inputPoints);

            List<Vector2> smoothedPoints = new List<Vector2>();

            // Add the first point unchanged
            smoothedPoints.Add(inputPoints[0]);

            // Smooth the middle points
            for (int i = 1; i < inputPoints.Count - 1; i++)
            {
                Vector2 prev = inputPoints[i - 1];
                Vector2 curr = inputPoints[i];
                Vector2 next = inputPoints[i + 1];

                // Calculate the weighted average
                float x = curr.x;
                float y = (1 - smoothingFactor) * curr.y + smoothingFactor * (prev.y + next.y) / 2f;

                smoothedPoints.Add(new Vector2(x, y));
            }

            // Add the last point unchanged
            smoothedPoints.Add(inputPoints[inputPoints.Count - 1]);

            return smoothedPoints;
        }

        /// <summary>
        /// Applies Bezier smoothing to the points
        /// </summary>
        private List<Vector2> ApplyBezierSmoothing(List<Vector2> inputPoints)
        {
            if (inputPoints.Count < 3)
                return new List<Vector2>(inputPoints);

            List<Vector2> smoothedPoints = new List<Vector2>();

            // Number of points to generate between each pair of input points
            int numPointsBetween = 5;

            // Add the first point
            smoothedPoints.Add(inputPoints[0]);

            // Generate Bezier curves between each pair of points
            for (int i = 0; i < inputPoints.Count - 1; i++)
            {
                Vector2 p0 = inputPoints[i];
                Vector2 p3 = inputPoints[i + 1];

                // Calculate control points
                Vector2 p1, p2;

                if (i == 0)
                {
                    // First segment
                    p1 = p0 + (p3 - p0) * 0.33f;
                    p2 = p0 + (p3 - p0) * 0.66f;
                }
                else if (i == inputPoints.Count - 2)
                {
                    // Last segment
                    p1 = p0 + (p3 - p0) * 0.33f;
                    p2 = p0 + (p3 - p0) * 0.66f;
                }
                else
                {
                    // Middle segments
                    Vector2 prevTangent = (inputPoints[i + 1] - inputPoints[i - 1]) * 0.5f;
                    Vector2 nextTangent = (inputPoints[i + 2] - inputPoints[i]) * 0.5f;

                    p1 = p0 + prevTangent * 0.33f;
                    p2 = p3 - nextTangent * 0.33f;
                }

                // Generate points along the Bezier curve
                for (int j = 1; j <= numPointsBetween; j++)
                {
                    float t = j / (float)(numPointsBetween + 1);
                    Vector2 point = CalculateBezierPoint(p0, p1, p2, p3, t);
                    smoothedPoints.Add(point);
                }
            }

            // Add the last point
            smoothedPoints.Add(inputPoints[inputPoints.Count - 1]);

            return smoothedPoints;
        }

        /// <summary>
        /// Calculates a point on a cubic Bezier curve
        /// </summary>
        private Vector2 CalculateBezierPoint(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float u = 1 - t;
            float tt = t * t;
            float uu = u * u;
            float uuu = uu * u;
            float ttt = tt * t;

            Vector2 p = uuu * p0;
            p += 3 * uu * t * p1;
            p += 3 * u * tt * p2;
            p += ttt * p3;

            return p;
        }

        /// <summary>
        /// Applies Catmull-Rom smoothing to the points
        /// </summary>
        private List<Vector2> ApplyCatmullRomSmoothing(List<Vector2> inputPoints)
        {
            if (inputPoints.Count < 4)
                return new List<Vector2>(inputPoints);

            List<Vector2> smoothedPoints = new List<Vector2>();

            // Number of points to generate between each pair of input points
            int numPointsBetween = 5;

            // Add the first point
            smoothedPoints.Add(inputPoints[0]);

            // Generate Catmull-Rom splines
            for (int i = 0; i < inputPoints.Count - 1; i++)
            {
                Vector2 p1 = inputPoints[i];
                Vector2 p2 = inputPoints[i + 1];

                // Get the previous and next points
                Vector2 p0 = (i > 0) ? inputPoints[i - 1] : p1 - (p2 - p1);
                Vector2 p3 = (i < inputPoints.Count - 2) ? inputPoints[i + 2] : p2 + (p2 - p1);

                // Generate points along the Catmull-Rom spline
                for (int j = 1; j <= numPointsBetween; j++)
                {
                    float t = j / (float)(numPointsBetween + 1);
                    Vector2 point = CalculateCatmullRomPoint(p0, p1, p2, p3, t);
                    smoothedPoints.Add(point);
                }
            }

            // Add the last point
            smoothedPoints.Add(inputPoints[inputPoints.Count - 1]);

            return smoothedPoints;
        }

        /// <summary>
        /// Calculates a point on a Catmull-Rom spline
        /// </summary>
        private Vector2 CalculateCatmullRomPoint(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float t2 = t * t;
            float t3 = t2 * t;

            float b0 = 0.5f * (-t3 + 2f * t2 - t);
            float b1 = 0.5f * (3f * t3 - 5f * t2 + 2f);
            float b2 = 0.5f * (-3f * t3 + 4f * t2 + t);
            float b3 = 0.5f * (t3 - t2);

            return b0 * p0 + b1 * p1 + b2 * p2 + b3 * p3;
        }

        /// <summary>
        /// Resets the pattern to default values
        /// </summary>
        public void ResetPattern()
        {
            // Create default points
            List<Vector2> defaultPoints = new List<Vector2> {
                new Vector2(0.0f, 1.1f),
                new Vector2(0.25f, 1.15f),
                new Vector2(0.5f, 1.2f),
                new Vector2(0.75f, 1.25f),
                new Vector2(1.0f, 1.3f)
            };

            // Store the generated points
            generatedPoints = new List<Vector2>(defaultPoints);

            // Notify listeners
            OnPointsGenerated?.Invoke(defaultPoints);
        }

        /// <summary>
        /// Resets a LevelingAlgorithmBase to default values
        /// </summary>
        public void ResetAlgorithmData(LevelingAlgorithmBase algorithmData)
        {
            if (algorithmData == null)
                return;

            // Clear all points
            algorithmData.points.Clear();

            // Add default points
            algorithmData.points.Add(new Vector2(0.0f, 1.1f));
            algorithmData.points.Add(new Vector2(0.25f, 1.15f));
            algorithmData.points.Add(new Vector2(0.5f, 1.2f));
            algorithmData.points.Add(new Vector2(0.75f, 1.25f));
            algorithmData.points.Add(new Vector2(1.0f, 1.3f));

            // Reset the pattern
            ResetPattern();
        }

        /// <summary>
        /// Converts the points to one point per level
        /// </summary>
        public void ConvertToOnePointPerLevel(List<Vector2> points, int startingLevel, int maxLevel)
        {
            if (points.Count == 0)
                return;

            // Sort points by X coordinate
            var sortedPoints = points.OrderBy(p => p.x).ToList();

            // Calculate the number of levels
            int numLevels = maxLevel - startingLevel + 1;

            // Create a new list with one point per level
            List<Vector2> newPoints = new List<Vector2>();

            for (int i = 0; i < numLevels; i++)
            {
                // Calculate the normalized X coordinate for this level
                float x = i / (float)Mathf.Max(1, numLevels - 1);

                // Interpolate the Y value from the existing points
                float y = InterpolateYValue(sortedPoints, x);

                newPoints.Add(new Vector2(x, y));
            }

            // Notify listeners
            OnPointsGenerated?.Invoke(newPoints);
        }

        /// <summary>
        /// Interpolates a Y value from a list of points at a given X coordinate
        /// </summary>
        public float InterpolateYValue(List<Vector2> points, float x)
        {
            // Find the two points that bracket the X coordinate
            for (int i = 0; i < points.Count - 1; i++)
            {
                if (x >= points[i].x && x <= points[i + 1].x)
                {
                    // Calculate the interpolation factor
                    float t = (x - points[i].x) / (points[i + 1].x - points[i].x);

                    // Interpolate the Y value
                    return Mathf.Lerp(points[i].y, points[i + 1].y, t);
                }
            }

            // If X is outside the range, return the Y value of the closest point
            if (x < points[0].x)
                return points[0].y;
            else
                return points[points.Count - 1].y;
        }

        /// <summary>
        /// Gets sorted points with caching
        /// </summary>
        public List<Vector2> GetSortedPoints(List<Vector2> points, ref List<Vector2> cachedSortedPoints, ref bool pointsNeedSorting)
        {
            if (pointsNeedSorting || cachedSortedPoints.Count != points.Count)
            {
                cachedSortedPoints = points.OrderBy(p => p.x).ToList();
                pointsNeedSorting = false;
            }
            return cachedSortedPoints;
        }

        /// <summary>
        /// Rescales all points from the old Y-axis range to the new Y-axis range with boundary checking
        /// </summary>
        public List<Vector2> RescalePointsToNewRange(List<Vector2> points, float oldMin, float oldMax, float newMin, float newMax)
        {
            if (points.Count == 0 || oldMax <= oldMin || newMax <= newMin)
                return new List<Vector2>(points);

            List<Vector2> rescaledPoints = new List<Vector2>();

            // Calculate padding for boundaries (5% of the new range)
            float boundaryPadding = (newMax - newMin) * 0.05f;
            float boundaryMin = newMin + boundaryPadding;
            float boundaryMax = newMax - boundaryPadding;

            // First pass: map points to the new range
            foreach (var point in points)
            {
                // Get the normalized position in the old range (0-1)
                float normalizedY = (point.y - oldMin) / (oldMax - oldMin);

                // Map to the new range
                float newY = (normalizedY * (newMax - newMin)) + newMin;

                // Ensure the point stays within the boundaries
                newY = Mathf.Clamp(newY, boundaryMin, boundaryMax);

                // Add the rescaled point
                rescaledPoints.Add(new Vector2(point.x, newY));
            }

            // Second pass: check if any points are at the boundaries and adjust if needed
            bool needsAdjustment = false;
            foreach (var point in rescaledPoints)
            {
                if (Mathf.Approximately(point.y, boundaryMin) || Mathf.Approximately(point.y, boundaryMax))
                {
                    needsAdjustment = true;
                    break;
                }
            }

            if (needsAdjustment)
            {
                // Find min and max Y values
                float minY = float.MaxValue;
                float maxY = float.MinValue;

                foreach (var point in rescaledPoints)
                {
                    minY = Mathf.Min(minY, point.y);
                    maxY = Mathf.Max(maxY, point.y);
                }

                // Calculate scale factor to fit all points within boundaries
                float currentRange = maxY - minY;
                float targetRange = boundaryMax - boundaryMin;
                float scaleFactor = currentRange > 0 ? targetRange / currentRange : 1.0f;

                // Calculate midpoint for scaling
                float scalingMidpoint = (minY + maxY) / 2.0f;

                // If midpoint is too far from the center of the new range, use the center
                float newRangeCenter = (newMin + newMax) / 2.0f;
                if (Mathf.Abs(scalingMidpoint - newRangeCenter) > (newMax - newMin) / 2.0f)
                {
                    scalingMidpoint = newRangeCenter;
                }

                // Adjust all points to fit within boundaries
                List<Vector2> adjustedPoints = new List<Vector2>();
                foreach (var point in rescaledPoints)
                {
                    float offset = point.y - scalingMidpoint;
                    float adjustedY = scalingMidpoint + (offset * scaleFactor);

                    // Ensure the point is within boundaries
                    adjustedY = Mathf.Clamp(adjustedY, boundaryMin, boundaryMax);

                    adjustedPoints.Add(new Vector2(point.x, adjustedY));
                }

                return adjustedPoints;
            }

            return rescaledPoints;
        }
    }
}
