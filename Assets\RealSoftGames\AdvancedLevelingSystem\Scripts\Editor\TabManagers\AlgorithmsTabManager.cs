using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using System.IO;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Tab manager for displaying and editing algorithms
    /// </summary>
    public class AlgorithmsTabManager : ITabManager
    {
        private readonly AdvancedLevelingSystemEditorWindow window;
        private readonly DifficultyRatingDatabase difficultyRatingDatabase;
        private Vector2 scrollPosition;
        private readonly Dictionary<string, bool> foldoutStates = new();

        // Dictionary to track scroll positions for formula displays
        private readonly Dictionary<string, Vector2> formulaScrollPositions = new();

        // Tab properties
        public string TabName => "Algorithms";

        // UI Styles
        private GUIStyle headerStyle;
        private GUIStyle descriptionStyle;
        private GUIStyle ratingStyle;
        private GUIStyle algorithmNameStyle;
        private GUIStyle formulaHeaderStyle;
        private GUIStyle formulaDescriptionStyle;
        private GUIStyle searchFieldStyle;
        private GUIStyle filterButtonStyle;
        private GUIStyle activeFilterButtonStyle;
        private GUIStyle tooltipStyle;
        private GUIStyle buttonStyle;

        // Filtering and search
        private string searchText = "";
        private int difficultyFilter = 0; // 0 = All, 1+ = specific difficulty rating index

        // Preview and comparison
        private readonly LevelingCurvePreview curvePreview;
        private int? comparisonAlgorithmIndex;

        // Reference to global settings (now shared between tabs)
        private float GlobalLevelUpMultiplier => window.SharedLevelUpMultiplier;
        private int GlobalStartingExperience => window.SharedStartingExperience;
        private int GlobalStartingLevel => window.SharedStartingLevel;
        private int GlobalMaxLevel => window.SharedMaxLevel;

        // Filter labels for the difficulty filter buttons
        private string[] FilterLabels => new[] { "All" }.Concat(
            difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null
                ? difficultyRatingDatabase.difficultyRatings.Select(r => r.ratingName)
                : Array.Empty<string>()
        ).ToArray();

        public AlgorithmsTabManager(AdvancedLevelingSystemEditorWindow window, DifficultyRatingDatabase difficultyRatingDatabase)
        {
            this.window = window;
            this.difficultyRatingDatabase = difficultyRatingDatabase;

            // Initialize curve preview
            curvePreview = new LevelingCurvePreview();

            // We'll initialize the registry and foldout states in OnEnable
            // This prevents initialization loops
        }

        // Flag to track if foldout states have been initialized
        private bool foldoutStatesInitialized = false;

        public void OnEnable()
        {
            try
            {
                // Initialize foldout states here, but silently
                InitializeFoldoutStates(silent: true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error in AlgorithmsTabManager.OnEnable: {ex.Message}");
            }
        }

        // We'll use ScriptableAlgorithmRegistry.IsInitialized instead of a separate flag

        private void InitializeFoldoutStates(bool silent = false)
        {
            // Skip if already initialized
            if (foldoutStatesInitialized)
            {
                // if (!silent) Debug.Log("AlgorithmsTabManager: Foldout states already initialized, skipping");
                return;
            }

            try
            {
                // if (!silent) Debug.Log("AlgorithmsTabManager: Initializing foldout states");

                // Check if the AlgorithmDatabase exists
                AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (database == null)
                {
                    if (!silent) Debug.LogError("AlgorithmsTabManager: AlgorithmDatabase not found in Resources folder");
                }
                else
                {
                    // if (!silent) Debug.Log($"AlgorithmsTabManager: Found AlgorithmDatabase with {database.algorithms.Count} algorithms");

                    // Check if any algorithms are null
                    int nullCount = database.algorithms.Count(a => a == null);
                    if (nullCount > 0 && !silent)
                    {
                        Debug.LogWarning($"AlgorithmsTabManager: Found {nullCount} null algorithms in the database");
                    }

                    // Log the names of the algorithms in the database (only if not silent)
                    // if (!silent)
                    // {
                    //     foreach (var algo in database.algorithms)
                    //     {
                    //         if (algo != null)
                    //         {
                    //             Debug.Log($"AlgorithmsTabManager: Database contains algorithm: {algo.Name}");
                    //         }
                    //     }
                    // }
                }

                // Initialize the registry only once
                if (!ScriptableAlgorithmRegistry.IsInitialized)
                {
                    // if (!silent) Debug.Log("AlgorithmsTabManager: Initializing ScriptableAlgorithmRegistry for the first time");
                    ScriptableAlgorithmRegistry.Initialize();
                }
                else
                {
                    // if (!silent) Debug.Log("AlgorithmsTabManager: ScriptableAlgorithmRegistry already initialized, skipping");
                }

                // Initialize foldout states for algorithms
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();

                // Log the number of algorithms found (only once during initialization)
                // if (!silent) Debug.Log($"AlgorithmsTabManager: Found {(algorithms != null ? algorithms.Count() : 0)} algorithms from registry");

                // Pre-calculate points for each algorithm if needed
                if (algorithms != null && algorithms.Any())
                {
                    // if (!silent) Debug.Log("AlgorithmsTabManager: Pre-calculating points for algorithms");
                    foreach (var algorithm in algorithms)
                    {
                        if (algorithm == null)
                        {
                            if (!silent) Debug.LogWarning("AlgorithmsTabManager: Null algorithm found in registry");
                            continue;
                        }

                        try
                        {
                            // if (!silent) Debug.Log($"AlgorithmsTabManager: Pre-calculating points for algorithm: {algorithm.Name}");

                            // Use default values for pre-calculation
                            int startingExperience = GlobalStartingExperience;
                            int startingLevel = GlobalStartingLevel;
                            int maxLevel = GlobalMaxLevel;
                            float levelUpMultiplier = GlobalLevelUpMultiplier;

                            // Calculate experience requirements
                            var requirements = algorithm.CalculateRequirementCurve(startingExperience, startingLevel, maxLevel, levelUpMultiplier);
                            // if (!silent) Debug.Log($"AlgorithmsTabManager: Calculated {requirements.Count} requirement points for {algorithm.Name}");

                            // Calculate raw formula curve
                            var rawCurve = algorithm.CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);
                            // if (!silent) Debug.Log($"AlgorithmsTabManager: Calculated {rawCurve.Count} raw curve points for {algorithm.Name}");
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"Error pre-calculating points for algorithm {algorithm.Name}: {ex.Message}");
                        }
                    }
                }
                else
                {
                    if (!silent) Debug.LogWarning("AlgorithmsTabManager: No algorithms found in registry");
                }

                if (algorithms != null)
                {
                    foreach (var algorithm in algorithms)
                    {
                        if (algorithm == null)
                            continue;

                        // Use the algorithm name as the key
                        string key = !string.IsNullOrEmpty(algorithm.Name) ? algorithm.Name : "Unnamed Algorithm";
                        if (!foldoutStates.ContainsKey(key))
                        {
                            foldoutStates.Add(key, false);
                            // if (!silent) Debug.Log($"AlgorithmsTabManager: Added foldout state for algorithm: {key}");
                        }
                    }
                }

                // Mark as initialized
                foldoutStatesInitialized = true;
                // if (!silent) Debug.Log("AlgorithmsTabManager: Foldout states initialization complete");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error initializing foldout states: {ex.Message}");
            }
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            headerStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            descriptionStyle ??= new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                richText = true,
                padding = new RectOffset(5, 5, 5, 5)
            };

            ratingStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(0, 0, 5, 5)
            };

            algorithmNameStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleLeft
            };

            formulaHeaderStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 0)
            };

            formulaDescriptionStyle ??= new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                richText = true,
                fontStyle = FontStyle.Italic,
                padding = new RectOffset(5, 5, 0, 5),
                fontSize = 12
            };

            // Try to use a monospace font for formula text if available
            Font monoFont = EditorGUIUtility.Load("Fonts/RobotoMono/RobotoMono-Regular.ttf") as Font;
            if (monoFont != null)
            {
                formulaDescriptionStyle.font = monoFont;
            }
            else
            {
                // Fallback to a system monospace font
                formulaDescriptionStyle.font = Font.CreateDynamicFontFromOSFont("Courier New", 12);
            }

            searchFieldStyle ??= new GUIStyle(EditorStyles.toolbarSearchField)
            {
                margin = new RectOffset(5, 5, 5, 5),
                fixedWidth = 200 // Reduced from 250 to give more space
            };

            filterButtonStyle ??= new GUIStyle(EditorStyles.miniButton)
            {
                margin = new RectOffset(2, 2, 5, 5),
                padding = new RectOffset(5, 5, 2, 2)
            };

            activeFilterButtonStyle ??= new GUIStyle(filterButtonStyle)
            {
                normal = { background = EditorStyles.toolbarButton.active.background },
                fontStyle = FontStyle.Bold
            };

            tooltipStyle ??= new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 12,
                wordWrap = true,
                richText = true,
                padding = new RectOffset(10, 10, 10, 10)
            };

            buttonStyle ??= new GUIStyle(EditorStyles.miniButton)
            {
                padding = new RectOffset(10, 10, 5, 5)
            };
        }

        public void OnGUI()
        {
            // Ensure the registry is initialized
            if (!ScriptableAlgorithmRegistry.IsInitialized)
            {
                // Debug.Log("AlgorithmsTabManager: Registry not initialized in OnGUI, initializing now");
                ScriptableAlgorithmRegistry.Initialize(true);
            }

            // If we still don't have any algorithms, try to force load them from the database
            if (ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().Count() == 0)
            {
                // Debug.LogWarning("AlgorithmsTabManager: No algorithms found in registry in OnGUI, attempting to force load from database");
                ScriptableAlgorithmRegistry.ForceLoadFromDatabase();
            }

            // Initialize styles
            InitializeStyles();

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Algorithm Editor", headerStyle);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Global settings for previews
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Global Preview Settings", EditorStyles.boldLabel);

            // Level Up Multiplier (minimum 1.0 to avoid issues with values below 1.0)
            EditorGUI.BeginChangeCheck();
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", GlobalLevelUpMultiplier, 1.0f, 5.0f);
            if (newLevelUpMultiplier != GlobalLevelUpMultiplier)
            {
                // Update the shared setting in the window
                window.UpdateSharedSettings(levelUpMultiplier: newLevelUpMultiplier);
                // Force repaint to update the graph immediately
                window.Repaint();
            }

            // Show a tooltip about the multiplier
            EditorGUILayout.HelpBox("The Level Up Multiplier affects how quickly experience requirements increase between levels. Higher values create steeper curves. Minimum value is 1.0 to ensure proper progression.", MessageType.Info);

            // Starting Experience
            EditorGUILayout.BeginHorizontal();
            int newStartingExperience = EditorGUILayout.IntField("Starting Experience", GlobalStartingExperience);
            if (newStartingExperience < 1) newStartingExperience = 1; // Ensure positive value
            if (newStartingExperience != GlobalStartingExperience)
            {
                window.UpdateSharedSettings(startingExperience: newStartingExperience);
                window.Repaint();
            }
            EditorGUILayout.EndHorizontal();

            // Starting Level
            int newStartingLevel = EditorGUILayout.IntField("Starting Level", GlobalStartingLevel);
            if (newStartingLevel < 1) newStartingLevel = 1; // Ensure positive value
            if (newStartingLevel != GlobalStartingLevel)
            {
                window.UpdateSharedSettings(startingLevel: newStartingLevel);
                window.Repaint();
            }

            // Max Level
            int newMaxLevel = EditorGUILayout.IntField("Max Level", GlobalMaxLevel);
            if (newMaxLevel < GlobalStartingLevel + 5) newMaxLevel = GlobalStartingLevel + 5; // Ensure reasonable range
            if (newMaxLevel != GlobalMaxLevel)
            {
                window.UpdateSharedSettings(maxLevel: newMaxLevel);
                window.Repaint();
            }

            // Graph Zooming Toggle
            bool enableGraphZooming = LevelingCurvePreview.IsGraphZoomingEnabled;
            EditorGUI.BeginChangeCheck();
            enableGraphZooming = EditorGUILayout.Toggle(new GUIContent("Enable Graph Zooming", "Automatically use logarithmic scale for steep curves"), enableGraphZooming);
            if (EditorGUI.EndChangeCheck())
            {
                LevelingCurvePreview.IsGraphZoomingEnabled = enableGraphZooming;

                // Force repaint to update the graph immediately
                window.Repaint();
            }

            EditorGUI.EndChangeCheck();

            // Reset All button
            EditorGUILayout.Space(5);
            if (GUILayout.Button("Reset All to Default", GUILayout.Height(25)))
            {
                // Reset all global settings to default values
                window.UpdateSharedSettings(
                    levelUpMultiplier: 1f,
                    startingExperience: 250,
                    startingLevel: 1,
                    maxLevel: 30
                );
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Search and filter - split into two rows to prevent overlap
            // First row: Search field
            EditorGUILayout.BeginHorizontal();

            // Search field
            EditorGUILayout.LabelField("Search:", GUILayout.Width(50));
            searchText = EditorGUILayout.TextField(searchText, searchFieldStyle);

            // Clear search button
            if (!string.IsNullOrEmpty(searchText))
            {
                if (GUILayout.Button("Clear", GUILayout.Width(50)))
                {
                    searchText = "";
                    GUI.FocusControl(null);
                }
            }

            // Add flexible space to push the comparison dropdown to the right
            GUILayout.FlexibleSpace();

            EditorGUILayout.EndHorizontal();

            // Add a small space between rows
            EditorGUILayout.Space(5);

            // Second row: Comparison dropdown
            EditorGUILayout.BeginHorizontal();

            // Global comparison dropdown using the searchable popup
            // Get all algorithms from the registry
            var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().ToList();

            // Create a list of algorithm names for the dropdown
            var algorithmNames = new List<string> { "None" };
            algorithmNames.AddRange(algorithms.Select(a => a.Name));

            // Determine the currently selected index
            int selectedIndex = 0; // Default to "None"
            if (comparisonAlgorithmIndex.HasValue && comparisonAlgorithmIndex.Value < algorithms.Count)
            {
                selectedIndex = comparisonAlgorithmIndex.Value + 1; // +1 because "None" is at index 0
            }

            EditorGUILayout.LabelField("Compare With:", GUILayout.Width(90));

            // Use the searchable popup from Utilities
            Utilities.SearchablePopup(
                selectedIndex,
                "",  // Empty label since we're using a separate label
                algorithmNames.ToArray(),
                (newIndex) => {
                    if (newIndex == 0)
                    {
                        // "None" selected
                        comparisonAlgorithmIndex = null;
                    }
                    else
                    {
                        // An algorithm was selected (subtract 1 to account for "None" at index 0)
                        comparisonAlgorithmIndex = newIndex - 1;
                    }
                    window.Repaint();
                },
                GUILayout.Width(200)  // Increased width for better visibility
            );

            // Add flexible space to push the dropdown to the left
            GUILayout.FlexibleSpace();

            EditorGUILayout.EndHorizontal();

            // Add a small space between rows
            EditorGUILayout.Space(5);

            // Difficulty filter buttons
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Filter:", GUILayout.Width(50));

            for (int i = 0; i < FilterLabels.Length; i++)
            {
                GUIStyle style = (i == difficultyFilter) ? activeFilterButtonStyle : filterButtonStyle;
                if (GUILayout.Button(FilterLabels[i], style))
                {
                    difficultyFilter = (difficultyFilter == i) ? 0 : i; // Toggle off if already selected
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Collapse/Expand All buttons - larger and centered
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Center buttons

            // Create a custom style for larger buttons with blue background and white text
            var largeButtonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(15, 15, 8, 8),
                normal = { background = CreateColorTexture(new Color(0.2f, 0.4f, 0.8f)), textColor = Color.white }
            };

            if (GUILayout.Button("Collapse All", largeButtonStyle, GUILayout.Width(120), GUILayout.Height(30)))
            {
                CollapseAll();
            }

            GUILayout.Space(10); // Space between buttons

            if (GUILayout.Button("Expand All", largeButtonStyle, GUILayout.Width(120), GUILayout.Height(30)))
            {
                ExpandAll();
            }

            GUILayout.Space(10); // Space between buttons

            // Create a custom style for the find duplicates button with a different color
            var findDuplicatesButtonStyle = new GUIStyle(largeButtonStyle)
            {
                normal = { background = CreateColorTexture(new Color(0.8f, 0.6f, 0.0f)), textColor = Color.white }
            };

            if (GUILayout.Button("Find Duplicates", findDuplicatesButtonStyle, GUILayout.Width(150), GUILayout.Height(30)))
            {
                FindDuplicateAlgorithms();
            }

            GUILayout.Space(10); // Space between buttons

            // Create a custom style for the refresh algorithms button with a different color
            var refreshAlgorithmsButtonStyle = new GUIStyle(largeButtonStyle)
            {
                normal = { background = CreateColorTexture(new Color(0.6f, 0.3f, 0.8f)), textColor = Color.white }
            };

            if (GUILayout.Button("Force Refresh All Algorithms", refreshAlgorithmsButtonStyle, GUILayout.Width(200), GUILayout.Height(30)))
            {
                ForceRefreshAllAlgorithms();
            }

            GUILayout.Space(10); // Space between buttons

            // Create a custom style for the convert coded algorithms button with a different color
            var convertButtonStyle = new GUIStyle(largeButtonStyle)
            {
                normal = { background = CreateColorTexture(new Color(0.0f, 0.6f, 0.4f)), textColor = Color.white }
            };

            if (GUILayout.Button("Convert & Clean Coded Algorithms", convertButtonStyle, GUILayout.Width(250), GUILayout.Height(30)))
            {
                if (EditorUtility.DisplayDialog("Convert Coded Algorithms",
                    "This will find all coded algorithms in the project, convert them to scriptable objects, and optionally remove the old implementations. Continue?",
                    "Yes, Convert", "Cancel"))
                {
                    // Call the method to convert coded algorithms in the Algorithm Designer tab
                    var algorithmDesignerTab = window.GetTabManager<AlgorithmDesignerTabManager>();
                    if (algorithmDesignerTab != null)
                    {
                        algorithmDesignerTab.ConvertAllCodedAlgorithms();

                        // Show a message about the conversion process
                        EditorUtility.DisplayDialog("Conversion Complete",
                            "The conversion process is complete. The converted algorithms have been added to the database and normalized to use X values between 0-1 for proper display in the Algorithm Designer.",
                            "OK");
                    }
                    else
                    {
                        Debug.LogError("Algorithm Designer tab not found. Cannot convert coded algorithms.");
                        EditorUtility.DisplayDialog("Error", "Algorithm Designer tab not found. Cannot convert coded algorithms.", "OK");
                    }

                    // Reinitialize the registry with force=true to ensure it reloads all algorithms
                    ScriptableAlgorithmRegistry.Initialize(true);

                    // Initialize foldout states
                    foldoutStatesInitialized = false;
                    InitializeFoldoutStates();

                    // Repaint the window
                    window.Repaint();
                }
            }

            GUILayout.FlexibleSpace(); // Center buttons
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            try
            {
                // Make sure foldout states are initialized
                if (!foldoutStatesInitialized)
                {
                    // Debug.Log("AlgorithmsTabManager: Foldout states not initialized, initializing now"); // Commented out to prevent console spam
                    InitializeFoldoutStates();
                }

                // Check if we have any algorithms in the registry
                if (ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().Count() == 0)
                {
                    // Debug.Log("AlgorithmsTabManager: No algorithms found in registry"); // Commented out to prevent console spam

                    // Only check the database if the registry is already initialized
                    // This prevents initialization loops
                    if (ScriptableAlgorithmRegistry.IsInitialized)
                    {
                        // Check if the database has null entries and fix them
                        AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                        if (database != null && database.algorithms != null)
                        {
                            bool hadNullAlgorithms = false;

                            // Remove null algorithms
                            for (int i = database.algorithms.Count - 1; i >= 0; i--)
                            {
                                if (database.algorithms[i] == null)
                                {
                                    // Debug.Log("AlgorithmsTabManager: Removing null algorithm from database");
                                    database.algorithms.RemoveAt(i);
                                    hadNullAlgorithms = true;
                                }
                            }

                            if (hadNullAlgorithms)
                            {
                                // Debug.Log("AlgorithmsTabManager: Saving database after removing null algorithms");
                                EditorUtility.SetDirty(database);
                                AssetDatabase.SaveAssets();
                            }

                            // We no longer automatically create default algorithms
                            // The user can click the "Create Default Algorithms" button instead
                        }
                        // We no longer automatically create the database
                        // The user can click the "Create Default Algorithms" button instead
                    }
                }

                // Use the already initialized algorithms from the registry
                // This avoids reloading algorithms every frame
                var allAlgorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().ToList();

                // Check if we have any algorithms
                if (allAlgorithms == null || allAlgorithms.Count == 0)
                {

                    // Display a warning icon and message
                    EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                    // Create a warning style with icon
                    GUIStyle warningStyle = new GUIStyle(EditorStyles.boldLabel);
                    warningStyle.fontSize = 14;
                    warningStyle.normal.textColor = new Color(0.9f, 0.6f, 0.1f);

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("⚠", warningStyle, GUILayout.Width(25));
                    EditorGUILayout.LabelField("No algorithms found", warningStyle);
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.Space(5);

                    // More detailed explanation
                    AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                    if (database != null && database.algorithms != null && database.algorithms.Count > 0)
                    {
                        EditorGUILayout.LabelField($"AlgorithmDatabase exists with {database.algorithms.Count} algorithm(s), but they are not loaded in the registry. Try refreshing the registry or creating default algorithms.", EditorStyles.wordWrappedLabel);
                    }
                    else
                    {
                        EditorGUILayout.LabelField("No algorithms found in the database. Click the button below to create default algorithms.", EditorStyles.wordWrappedLabel);
                    }

                    EditorGUILayout.Space(10);

                    // Create button styles
                    GUIStyle createButtonStyle = new GUIStyle(GUI.skin.button);
                    createButtonStyle.fontSize = 14;
                    createButtonStyle.fontStyle = FontStyle.Bold;
                    createButtonStyle.normal.background = CreateColorTexture(new Color(0.2f, 0.7f, 0.3f));
                    createButtonStyle.normal.textColor = Color.white;
                    createButtonStyle.padding = new RectOffset(15, 15, 10, 10);

                    GUIStyle refreshButtonStyle = new GUIStyle(GUI.skin.button);
                    refreshButtonStyle.fontSize = 14;
                    refreshButtonStyle.fontStyle = FontStyle.Bold;
                    refreshButtonStyle.normal.background = CreateColorTexture(new Color(0.2f, 0.5f, 0.8f));
                    refreshButtonStyle.normal.textColor = Color.white;
                    refreshButtonStyle.padding = new RectOffset(15, 15, 10, 10);

                    // Create a horizontal layout for the buttons
                    EditorGUILayout.BeginHorizontal();

                    // Force refresh registry button
                    if (GUILayout.Button("Force Refresh Registry", refreshButtonStyle, GUILayout.Height(40), GUILayout.Width(200)))
                    {
                        ForceRefreshRegistry();
                    }

                    GUILayout.FlexibleSpace();

                    // Add a prominent button to create default algorithms
                    if (GUILayout.Button("Create Default Algorithms", createButtonStyle, GUILayout.Height(40), GUILayout.Width(200)))
                    {
                        if (EditorUtility.DisplayDialog("Create Default Algorithms",
                            "This will create a set of common algorithms (Linear, Exponential, Sinusoidal, and Heartbeat). Continue?",
                            "Yes, Create", "Cancel"))
                        {
                            // Call the method to create default algorithms
                            CreateDefaultAlgorithms();
                        }
                    }

                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.Space(5);

                    EditorGUILayout.EndVertical();
                }
                else
                {
                    // Apply filtering
                    var filteredAlgorithms = allAlgorithms;

                    // Apply difficulty filter
                    if (difficultyFilter > 0 && difficultyRatingDatabase != null &&
                        difficultyRatingDatabase.difficultyRatings != null &&
                        difficultyFilter <= difficultyRatingDatabase.difficultyRatings.Count)
                    {
                        try
                        {
                            // Filter by the selected rating (difficultyFilter - 1 because index 0 is "All")
                            int ratingIndex = difficultyFilter - 1;
                            var selectedRating = difficultyRatingDatabase.difficultyRatings[ratingIndex];
                            filteredAlgorithms = allAlgorithms.Where(a =>
                                a is LevelingAlgorithmBase scriptableAlgorithm &&
                                scriptableAlgorithm.difficultyRating != null &&
                                scriptableAlgorithm.difficultyRating.ratingName == selectedRating.ratingName).ToList();
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"Error applying difficulty filter: {ex.Message}");
                            // Reset filter on error
                            difficultyFilter = 0;
                        }
                    }

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchText))
                    {
                        try
                        {
                            string search = searchText.ToLowerInvariant();
                            filteredAlgorithms = filteredAlgorithms.Where(a =>
                                a.Name.ToLowerInvariant().Contains(search) ||
                                a.Description.ToLowerInvariant().Contains(search) ||
                                a.FormulaExplanation.ToLowerInvariant().Contains(search)
                            ).ToList();
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"Error applying search filter: {ex.Message}");
                            // Clear search on error
                            searchText = "";
                        }
                    }

                    // Display filtered algorithms
                    if (filteredAlgorithms.Count == 0)
                    {
                        EditorGUILayout.HelpBox("No algorithms match your search or filter criteria.", MessageType.Info);
                    }
                    else
                    {
                        // Display algorithms in a vertical list (one per line)
                        foreach (var algorithm in filteredAlgorithms)
                        {
                            try
                            {
                                bool isSelected = comparisonAlgorithmIndex.HasValue &&
                                                 comparisonAlgorithmIndex.Value < allAlgorithms.Count &&
                                                 allAlgorithms[comparisonAlgorithmIndex.Value] == algorithm;

                                // Draw the algorithm section
                                DrawAlgorithmSection(algorithm, allAlgorithms);
                            }
                            catch (Exception ex)
                            {
                                EditorGUILayout.HelpBox($"Error drawing algorithm: {ex.Message}", MessageType.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EditorGUILayout.HelpBox($"Error loading algorithms: {ex.Message}", MessageType.Error);
            }

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space(10);
            if (GUILayout.Button("Save Changes", GUILayout.Height(30)))
            {
                SaveChanges();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawAlgorithmSection(ILevelingAlgorithm algorithm, List<ILevelingAlgorithm> allAlgorithms)
        {
            if (algorithm == null)
            {
                EditorGUILayout.HelpBox("Invalid algorithm (null reference).", MessageType.Error);
                return;
            }

            try
            {
                // Get the foldout state for this algorithm using the algorithm name as the key
                string key = !string.IsNullOrEmpty(algorithm.Name) ? algorithm.Name : "Unnamed Algorithm";
                bool foldout = foldoutStates.TryGetValue(key, out bool value) && value;

                // Create a box for the algorithm section - one card per line
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                // Header row with foldout, name, and rating
                EditorGUILayout.BeginHorizontal();

                // Get the difficulty rating color if available
                Color algorithmColor = Color.white;
                if (algorithm is LevelingAlgorithmBase scriptableAlgorithm && scriptableAlgorithm.difficultyRating != null)
                {
                    algorithmColor = scriptableAlgorithm.difficultyRating.ratingColor;
                }

                // Use the algorithm color for the background of the header
                Color originalColor = GUI.backgroundColor;
                GUI.backgroundColor = algorithmColor;

                // Display comparison indicator near the foldout if this is the currently compared algorithm
                if (comparisonAlgorithmIndex.HasValue &&
                    comparisonAlgorithmIndex.Value < allAlgorithms.Count &&
                    allAlgorithms[comparisonAlgorithmIndex.Value] == algorithm)
                {
                    var comparedStyle = new GUIStyle(EditorStyles.miniLabel)
                    {
                        normal = { textColor = Color.cyan },
                        alignment = TextAnchor.MiddleRight
                    };
                    EditorGUILayout.LabelField("⟲", comparedStyle, GUILayout.Width(15));
                }

                // Display warning icon for duplicate algorithms
                if (IsDuplicateAlgorithm(algorithm))
                {
                    var warningStyle = new GUIStyle(EditorStyles.miniLabel)
                    {
                        normal = { textColor = Color.yellow },
                        alignment = TextAnchor.MiddleRight,
                        fontSize = 14
                    };
                    EditorGUILayout.LabelField("⚠", warningStyle, GUILayout.Width(20));

                    // Add tooltip for the warning
                    Rect lastRect = GUILayoutUtility.GetLastRect();
                    GUI.Label(lastRect, new GUIContent("", "Duplicate algorithm name detected. Multiple assets with this name exist."));
                }

                // Foldout control
                foldout = EditorGUILayout.Foldout(foldout, "", true, EditorStyles.foldout);
                foldoutStates[key] = foldout;

                // Algorithm name with white text for better visibility
                var coloredNameStyle = new GUIStyle(algorithmNameStyle)
                {
                    normal = { textColor = Color.white }
                };
                EditorGUILayout.LabelField(algorithm.Name, coloredNameStyle);

                // Display difficulty rating if available
                if (algorithm is LevelingAlgorithmBase scriptableAlg && scriptableAlg.difficultyRating != null)
                {
                    string ratingStars = "";
                    for (int i = 0; i < scriptableAlg.difficultyRating.stars; i++)
                    {
                        ratingStars += "★";
                    }
                    for (int i = scriptableAlg.difficultyRating.stars; i < 5; i++)
                    {
                        ratingStars += "☆";
                    }

                    // Create a temporary style with the algorithm color from the difficulty rating
                    var coloredRatingStyle = new GUIStyle(ratingStyle)
                    {
                        normal = { textColor = scriptableAlg.difficultyRating.ratingColor },
                        fontSize = 14 // Slightly larger stars for better visibility
                    };

                    EditorGUILayout.LabelField(ratingStars, coloredRatingStyle, GUILayout.Width(80));
                }
                else
                {
                    // No difficulty rating available
                    EditorGUILayout.LabelField("", GUILayout.Width(80));
                }

                // Reset background color
                GUI.backgroundColor = originalColor;

                // Add a compare dropdown at the end of the header row
                // Create a list of algorithm names for the dropdown
                var allAlgorithmNames = new List<string> { "None" };
                allAlgorithmNames.AddRange(allAlgorithms.Select(a => a.Name));

                // Determine the currently selected index
                int currentIndex = 0; // Default to "None"
                if (comparisonAlgorithmIndex.HasValue &&
                    comparisonAlgorithmIndex.Value < allAlgorithms.Count)
                {
                    // If this algorithm is the one being compared, show "None" as selected
                    if (allAlgorithms[comparisonAlgorithmIndex.Value] == algorithm)
                    {
                        currentIndex = 0;
                    }
                    else
                    {
                        // Otherwise, show the currently compared algorithm
                        currentIndex = comparisonAlgorithmIndex.Value + 1; // +1 because "None" is at index 0
                    }
                }

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    currentIndex,
                    "", // No label needed
                    allAlgorithmNames.ToArray(),
                    (newIndex) => {
                        if (newIndex == 0)
                        {
                            // "None" selected
                            comparisonAlgorithmIndex = null;
                        }
                        else
                        {
                            // An algorithm was selected (subtract 1 to account for "None" at index 0)
                            comparisonAlgorithmIndex = newIndex - 1;
                        }
                        window.Repaint();
                    },
                    GUILayout.Width(80)
                );

                EditorGUILayout.EndHorizontal();

                // If the foldout is expanded, show the details
                if (foldout)
                {
                    EditorGUILayout.Space(5);

                    // Description in a vertical layout (full width)
                    EditorGUILayout.BeginVertical();
                    EditorGUILayout.LabelField("Description:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField(algorithm.Description, descriptionStyle, GUILayout.Height(40));
                    EditorGUILayout.EndVertical();

                    // Add space between description and formula
                    EditorGUILayout.Space(10);

                    // Formula in a vertical layout (full width)
                    EditorGUILayout.BeginVertical();
                    EditorGUILayout.LabelField("Formula:", EditorStyles.boldLabel);

                    // Create a label style for the formula with monospace font for better readability
                    GUIStyle formulaTextAreaStyle = new GUIStyle(EditorStyles.label)
                    {
                        wordWrap = true,
                        richText = true,
                        fontStyle = FontStyle.Normal,
                        fontSize = 12,
                        padding = new RectOffset(10, 10, 10, 10),
                        margin = new RectOffset(5, 5, 5, 5),
                        font = EditorGUIUtility.Load("Fonts/RobotoMono/RobotoMono-Regular.ttf") as Font,
                        normal = { textColor = EditorGUIUtility.isProSkin ? new Color(0.9f, 0.9f, 0.9f) : new Color(0.1f, 0.1f, 0.1f) }
                    };

                    // Fallback to a system monospace font if the custom font isn't available
                    if (formulaTextAreaStyle.font == null)
                    {
                        formulaTextAreaStyle.font = Font.CreateDynamicFontFromOSFont("Courier New", 12);
                    }

                    // Display the formula in a scrollable area to handle longer formulas
                    // Create a background box to make the formula stand out
                    GUIStyle formulaBackgroundStyle = new GUIStyle(EditorStyles.textArea);

                    // Set a slightly different background color based on whether we're in Pro skin or not
                    Color bgColor = EditorGUIUtility.isProSkin
                        ? new Color(0.2f, 0.2f, 0.25f) // Darker blue for dark theme
                        : new Color(0.9f, 0.9f, 0.95f); // Light blue for light theme

                    // Create a texture for the background
                    Texture2D bgTexture = new Texture2D(1, 1);
                    bgTexture.SetPixel(0, 0, bgColor);
                    bgTexture.Apply();
                    formulaBackgroundStyle.normal.background = bgTexture;

                    EditorGUILayout.BeginVertical(formulaBackgroundStyle);

                    // Store the current scroll position in a dictionary using algorithm name as key
                    string scrollKey = algorithm.Name + "_formulaScroll";
                    if (!formulaScrollPositions.ContainsKey(scrollKey))
                    {
                        formulaScrollPositions[scrollKey] = Vector2.zero;
                    }

                    // Begin a scrollable area with fixed height
                    formulaScrollPositions[scrollKey] = EditorGUILayout.BeginScrollView(
                        formulaScrollPositions[scrollKey],
                        GUILayout.Height(80), // Increased height for better visibility
                        GUILayout.ExpandWidth(true)
                    );

                    // Calculate the height needed for the content based on the text
                    float contentHeight = formulaTextAreaStyle.CalcHeight(
                        new GUIContent(algorithm.FormulaExplanation),
                        EditorWindowTemplate.MainBodyRect.width - 40 // Account for padding and scrollbar
                    );

                    // Display the formula as a label with auto-height based on content
                    EditorGUILayout.LabelField(algorithm.FormulaExplanation, formulaTextAreaStyle,
                        GUILayout.Height(Mathf.Max(contentHeight, 60)), // Use at least 60 pixels height
                        GUILayout.ExpandWidth(true)
                    );

                    // End the scroll view
                    EditorGUILayout.EndScrollView();
                    EditorGUILayout.EndVertical();
                    EditorGUILayout.EndVertical();

                    // Add space before the curve preview
                    EditorGUILayout.Space(5);

                    // Curve preview
                    EditorGUILayout.LabelField("Experience Curve Preview", EditorStyles.boldLabel);

                    try
                    {
                        // Get the comparison algorithm if one is selected
                        ILevelingAlgorithm comparisonAlgorithm = null;
                        if (comparisonAlgorithmIndex.HasValue &&
                            comparisonAlgorithmIndex.Value < allAlgorithms.Count)
                        {
                            comparisonAlgorithm = allAlgorithms[comparisonAlgorithmIndex.Value];
                        }

                        // Pass the main body width from the editor window template
                        float availableWidth = EditorWindowTemplate.MainBodyRect.width;

                        try
                        {
                            // Get colors for the algorithms
                            Color mainColor = algorithmColor;
                            Color comparisonColor = Color.white;

                            if (comparisonAlgorithm != null &&
                                comparisonAlgorithm is LevelingAlgorithmBase compScriptableAlg &&
                                compScriptableAlg.difficultyRating != null)
                            {
                                comparisonColor = compScriptableAlg.difficultyRating.ratingColor;
                            }

                            // Draw the curve with comparison if available
                            curvePreview.DrawCurvePreview(
                                algorithm,
                                GlobalLevelUpMultiplier,
                                comparisonAlgorithm,
                                GlobalStartingExperience,
                                GlobalStartingLevel,
                                GlobalMaxLevel,
                                availableWidth
                            );

                            // Add legend if we have a comparison algorithm
                            if (comparisonAlgorithm != null)
                            {
                                EditorGUILayout.BeginHorizontal();
                                EditorGUILayout.LabelField(
                                    $"<color=#{ColorUtility.ToHtmlStringRGB(mainColor)}>{algorithm.Name}</color> vs <color=#{ColorUtility.ToHtmlStringRGB(comparisonColor)}>{comparisonAlgorithm.Name}</color>",
                                    new GUIStyle(EditorStyles.label) { richText = true }
                                );
                                EditorGUILayout.EndHorizontal();
                            }
                        }
                        catch (Exception ex)
                        {
                            EditorGUILayout.HelpBox($"Error drawing curve: {ex.Message}", MessageType.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        EditorGUILayout.HelpBox($"Error drawing curve: {ex.Message}", MessageType.Error);
                    }

                    EditorGUILayout.Space(5);
                }

                EditorGUILayout.EndVertical();

                // Add a small space between algorithm entries
                EditorGUILayout.Space(2);
            }
            catch (Exception ex)
            {
                EditorGUILayout.HelpBox($"Error displaying algorithm: {ex.Message}", MessageType.Error);
                Debug.LogException(ex);

                // Make sure we end any open layout groups to prevent layout errors
                EditorGUILayout.EndVertical();
            }
        }

        private void SaveChanges()
        {
            try
            {
                // Save any changes to scriptable objects
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();
                if (algorithms == null)
                {
                    Debug.LogError("Failed to get algorithms from registry.");
                    EditorUtility.DisplayDialog("Error", "Failed to get algorithms from registry.", "OK");
                    return;
                }

                int savedCount = 0;
                foreach (var algorithm in algorithms)
                {
                    if (algorithm == null)
                        continue;

                    if (algorithm is ScriptableObject scriptableObject)
                    {
                        EditorUtility.SetDirty(scriptableObject);
                        savedCount++;
                    }
                }

                AssetDatabase.SaveAssets();
                Debug.Log($"Algorithm changes saved successfully ({savedCount} algorithms).");

                // Show a success message
                EditorUtility.DisplayDialog("Changes Saved", $"All changes have been saved successfully ({savedCount} algorithms).", "OK");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving changes: {ex.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to save changes: {ex.Message}", "OK");
            }
        }

        /// <summary>
        /// Dictionary to track duplicate algorithms
        /// </summary>
        private readonly Dictionary<string, List<string>> duplicateAlgorithms = new();

        /// <summary>
        /// Finds duplicate algorithms and stores them for highlighting in the UI
        /// </summary>
        private void FindDuplicateAlgorithms()
        {
            try
            {
                // Clear previous duplicates
                duplicateAlgorithms.Clear();

                // Find all DrawnPatternAlgorithm assets in the project
                string[] guids = AssetDatabase.FindAssets("t:DrawnPatternAlgorithm");

                // Group algorithms by name
                Dictionary<string, List<string>> algorithmsByName = new();

                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                    if (algorithm != null)
                    {
                        string name = algorithm.algorithmName;

                        if (!algorithmsByName.ContainsKey(name))
                        {
                            algorithmsByName[name] = new();
                        }

                        algorithmsByName[name].Add(path);
                    }
                }

                // Find duplicates (any name with more than one path)
                int duplicateCount = 0;

                foreach (var kvp in algorithmsByName)
                {
                    if (kvp.Value.Count > 1)
                    {
                        // Store the duplicate for highlighting
                        duplicateAlgorithms[kvp.Key] = kvp.Value;
                        duplicateCount++;
                    }
                }

                // Show a message to the user
                if (duplicateCount > 0)
                {
                    EditorUtility.DisplayDialog(
                        "Duplicates Found",
                        $"Found {duplicateCount} algorithm name(s) with duplicates. " +
                        "Duplicates will be highlighted in the list with a warning icon.",
                        "OK"
                    );
                }
                else
                {
                    EditorUtility.DisplayDialog(
                        "No Duplicates Found",
                        "No duplicate algorithms were found.",
                        "OK"
                    );
                }

                // Repaint the window to show the highlights
                window.Repaint();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error finding duplicates: {ex.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to find duplicates: {ex.Message}", "OK");
            }
        }

        /// <summary>
        /// Checks if an algorithm is a duplicate by comparing names
        /// </summary>
        private bool IsDuplicateAlgorithm(ILevelingAlgorithm algorithm)
        {
            if (algorithm == null || duplicateAlgorithms.Count == 0)
                return false;

            return duplicateAlgorithms.ContainsKey(algorithm.Name);
        }

        // Set all foldout states to collapsed (false)
        private void CollapseAll()
        {
            try
            {
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();
                if (algorithms != null)
                {
                    foreach (var algorithm in algorithms)
                    {
                        if (algorithm == null)
                            continue;

                        // Use the algorithm name as the key
                        string key = !string.IsNullOrEmpty(algorithm.Name) ? algorithm.Name : "Unnamed Algorithm";
                        foldoutStates[key] = false;
                    }
                }
                // Force repaint to update the UI immediately
                window.Repaint();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error collapsing all: {ex.Message}");
            }
        }

        // Set all foldout states to expanded (true)
        private void ExpandAll()
        {
            try
            {
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();
                if (algorithms != null)
                {
                    foreach (var algorithm in algorithms)
                    {
                        if (algorithm == null)
                            continue;

                        // Use the algorithm name as the key
                        string key = !string.IsNullOrEmpty(algorithm.Name) ? algorithm.Name : "Unnamed Algorithm";
                        foldoutStates[key] = true;
                    }
                }
                // Force repaint to update the UI immediately
                window.Repaint();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error expanding all: {ex.Message}");
            }
        }

        // Helper method to create a colored texture for UI elements
        private Texture2D CreateColorTexture(Color color)
        {
            var texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }



        /// <summary>
        /// Forces a refresh of the algorithm registry
        /// </summary>
        private void ForceRefreshRegistry()
        {
            try
            {
                Debug.Log("AlgorithmsTabManager: Forcing refresh of ScriptableAlgorithmRegistry");

                // First try to force load directly from the database
                ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

                // If that didn't work, try the normal initialization with force=true
                if (ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().Count() == 0)
                {
                    Debug.Log("AlgorithmsTabManager: Force load didn't work, trying normal initialization with force=true");
                    ScriptableAlgorithmRegistry.Initialize(true);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error refreshing registry: {ex.Message}");
            }
        }

        /// <summary>
        /// Forces a refresh of all algorithms by clearing their cached values
        /// </summary>
        private void ForceRefreshAllAlgorithms()
        {
            try
            {
                Debug.Log("AlgorithmsTabManager: Forcing refresh of all algorithms");

                // First refresh the registry to ensure we have all algorithms
                ForceRefreshRegistry();

                // Get all algorithms
                var algorithms = ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface();
                int count = 0;

                // Clear cached values for each algorithm
                foreach (var algorithm in algorithms)
                {
                    if (algorithm is LevelingAlgorithmBase scriptableAlgorithm)
                    {
                        Debug.Log($"Clearing cached values for {algorithm.Name}");
                        scriptableAlgorithm.ClearCachedValues();

                        // Force pre-calculation with current parameters
                        scriptableAlgorithm.PreCalculatePoints(
                            GlobalStartingExperience,
                            GlobalStartingLevel,
                            GlobalMaxLevel,
                            GlobalLevelUpMultiplier
                        );
                        count++;
                    }
                }

                // Reinitialize foldout states
                foldoutStatesInitialized = false;
                InitializeFoldoutStates(silent: true);

                // Repaint the window
                window.Repaint();

                // Show a success message
                EditorUtility.DisplayDialog("Algorithms Refreshed",
                    $"Successfully refreshed {count} algorithms. All cached values have been cleared and recalculated.",
                    "OK");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error refreshing algorithms: {ex.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to refresh algorithms: {ex.Message}", "OK");
            }
        }

        /// <summary>
        /// Creates default algorithms and adds them to the database
        /// </summary>
        private void CreateDefaultAlgorithms()
        {
            try
            {
                // Check if the Resources directory exists
                string resourcesPath = Path.Combine(Application.dataPath, "RealSoftGames/AdvancedLevelingSystem/Resources");
                if (!Directory.Exists(resourcesPath))
                {
                    Directory.CreateDirectory(resourcesPath);
                }

                // Check if the Algorithms directory exists
                string algorithmsPath = Path.Combine(resourcesPath, "Algorithms");
                if (!Directory.Exists(algorithmsPath))
                {
                    Directory.CreateDirectory(algorithmsPath);
                }

                // Create the database if it doesn't exist
                string databasePath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset";
                AlgorithmDatabase database = AssetDatabase.LoadAssetAtPath<AlgorithmDatabase>(databasePath);

                if (database == null)
                {
                    database = ScriptableObject.CreateInstance<AlgorithmDatabase>();
                    AssetDatabase.CreateAsset(database, databasePath);
                }

                // Create default algorithms
                CreateLinearAlgorithm(database);
                CreateExponentialAlgorithm(database);
                CreateSinusoidalAlgorithm(database);
                CreateHeartbeatAlgorithm(database);

                // Save assets
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                // Debug.Log($"AlgorithmsTabManager: Created default algorithms. Database now contains {database.algorithms.Count} algorithms.");

                // Log the names of all algorithms in the database
                foreach (var algo in database.algorithms)
                {
                    if (algo != null)
                    {
                        // Debug.Log($"AlgorithmsTabManager: Database contains algorithm: {algo.Name}");
                    }
                    else
                    {
                        Debug.LogWarning("AlgorithmsTabManager: Database contains a null algorithm");
                    }
                }

                // Reinitialize the registry with force=true to ensure it reloads all algorithms
                // Debug.Log("AlgorithmsTabManager: Forcing refresh of ScriptableAlgorithmRegistry after creating default algorithms");

                // First try to force load directly from the database
                ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

                // If that didn't work, try the normal initialization with force=true
                if (ScriptableAlgorithmRegistry.GetAllAlgorithmsAsInterface().Count() == 0)
                {
                    // Debug.Log("AlgorithmsTabManager: Force load didn't work, trying normal initialization with force=true");
                    ScriptableAlgorithmRegistry.Initialize(true);
                }

                // Initialize foldout states
                foldoutStatesInitialized = false;
                InitializeFoldoutStates(silent: true);

                // Update the UI
                window.Repaint();

                // Show success message
                EditorUtility.DisplayDialog("Success", "Default algorithms created successfully!", "OK");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error creating default algorithms: {ex.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to create default algorithms: {ex.Message}", "OK");
            }
        }

        private void CreateLinearAlgorithm(AlgorithmDatabase database)
        {
            // Create Linear algorithm (Easy)
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms/LinearAlgorithm.asset";

            // Check if it already exists
            var existing = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(assetPath);
            if (existing != null)
            {
                // Make sure it's in the database
                if (!database.algorithms.Contains(existing))
                {
                    database.AddAlgorithm(existing);
                    EditorUtility.SetDirty(database);
                }

                return; // Skip if already exists
            }

            // Create the algorithm
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();
            algorithm.algorithmName = "Linear";
            algorithm.description = "A simple linear progression with consistent level-up requirements.";
            algorithm.formulaExplanation = "Experience = Previous * 1.1";

            // Set difficulty rating
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings.Count > 0)
            {
                var rating = difficultyRatingDatabase.difficultyRatings.Find(r => r.ratingName == "Easy");
                if (rating != null)
                {
                    algorithm.difficultyRating = rating;
                }
            }

            // Save the asset
            AssetDatabase.CreateAsset(algorithm, assetPath);
            EditorUtility.SetDirty(algorithm);

            // Add to database
            database.AddAlgorithm(algorithm);
            EditorUtility.SetDirty(database);
        }

        private void CreateExponentialAlgorithm(AlgorithmDatabase database)
        {
            // Create Exponential algorithm (Hard)
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms/ExponentialAlgorithm.asset";

            // Check if it already exists
            var existing = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(assetPath);
            if (existing != null)
            {
                // Make sure it's in the database
                if (!database.algorithms.Contains(existing))
                {
                    database.AddAlgorithm(existing);
                    EditorUtility.SetDirty(database);
                }

                return; // Skip if already exists
            }

            // Create the algorithm
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();
            algorithm.algorithmName = "Exponential";
            algorithm.description = "An exponential progression that gets increasingly difficult at higher levels.";
            algorithm.formulaExplanation = "Experience = Previous * (1.1 + 0.01 * Level)";

            // Set difficulty rating
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings.Count > 0)
            {
                var rating = difficultyRatingDatabase.difficultyRatings.Find(r => r.ratingName == "Hard");
                if (rating != null)
                {
                    algorithm.difficultyRating = rating;
                }
            }

            // Save the asset
            AssetDatabase.CreateAsset(algorithm, assetPath);
            EditorUtility.SetDirty(algorithm);

            // Add to database
            database.AddAlgorithm(algorithm);
            EditorUtility.SetDirty(database);
        }

        private void CreateSinusoidalAlgorithm(AlgorithmDatabase database)
        {
            // Create Sinusoidal algorithm (Medium)
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms/SinusoidalAlgorithm.asset";

            // Check if it already exists
            var existing = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(assetPath);
            if (existing != null)
            {
                // Make sure it's in the database
                if (!database.algorithms.Contains(existing))
                {
                    database.AddAlgorithm(existing);
                    EditorUtility.SetDirty(database);
                }

                return; // Skip if already exists
            }

            // Create the algorithm
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();
            algorithm.algorithmName = "Sinusoidal";
            algorithm.description = "A wave-like progression with alternating easier and harder levels.";
            algorithm.formulaExplanation = "Experience = Previous * (1.1 + 0.1 * sin(Level * π/5))";

            // Set difficulty rating
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings.Count > 0)
            {
                var rating = difficultyRatingDatabase.difficultyRatings.Find(r => r.ratingName == "Medium");
                if (rating != null)
                {
                    algorithm.difficultyRating = rating;
                }
            }

            // Save the asset
            AssetDatabase.CreateAsset(algorithm, assetPath);
            EditorUtility.SetDirty(algorithm);

            // Add to database
            database.AddAlgorithm(algorithm);
            EditorUtility.SetDirty(database);
        }

        private void CreateHeartbeatAlgorithm(AlgorithmDatabase database)
        {
            // Create Heartbeat algorithm (Medium)
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/Algorithms/HeartbeatAlgorithm.asset";

            // Check if it already exists
            var existing = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(assetPath);
            if (existing != null)
            {
                // Make sure it's in the database
                if (!database.algorithms.Contains(existing))
                {
                    database.AddAlgorithm(existing);
                    EditorUtility.SetDirty(database);
                }

                return; // Skip if already exists
            }

            // Create the algorithm
            var algorithm = ScriptableObject.CreateInstance<LevelingAlgorithmBase>();
            algorithm.algorithmName = "Heartbeat";
            algorithm.description = "A heartbeat-like progression with double peaks.";
            algorithm.formulaExplanation = "Experience = Previous * (1.1 + 0.15 * heartbeat(Level))";

            // Set difficulty rating
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings.Count > 0)
            {
                var rating = difficultyRatingDatabase.difficultyRatings.Find(r => r.ratingName == "Medium");
                if (rating != null)
                {
                    algorithm.difficultyRating = rating;
                }
            }

            // Save the asset
            AssetDatabase.CreateAsset(algorithm, assetPath);
            EditorUtility.SetDirty(algorithm);

            // Add to database
            database.AddAlgorithm(algorithm);
            EditorUtility.SetDirty(database);
        }
    }
}
