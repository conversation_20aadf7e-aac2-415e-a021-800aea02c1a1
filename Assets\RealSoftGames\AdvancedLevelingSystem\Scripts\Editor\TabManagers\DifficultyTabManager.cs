using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Tab manager for displaying and editing difficulty ratings
    /// </summary>
    public class DifficultyTabManager : ITabManager
    {
        private readonly AdvancedLevelingSystemEditorWindow window;
        private readonly DifficultyRatingDatabase difficultyRatingDatabase;
        private Vector2 scrollPosition;
        private readonly Dictionary<string, bool> foldoutStates = new();

        // Tab properties
        public string TabName => "Difficulties";

        // UI Styles
        private GUIStyle headerStyle;
        private GUIStyle descriptionStyle;
        private GUIStyle ratingStyle;
        private GUIStyle difficultyNameStyle;
        private GUIStyle formulaHeaderStyle;
        private GUIStyle formulaDescriptionStyle;
        private GUIStyle searchFieldStyle;
        private GUIStyle filterButtonStyle;
        private GUIStyle activeFilterButtonStyle;
        private GUIStyle tooltipStyle;
        private GUIStyle buttonStyle;

        // Filtering and search
        private string searchText = "";
        private int difficultyFilter = 0; // 0 = All, 1+ = specific difficulty rating index

        // Preview and comparison
        private readonly LevelingCurvePreview curvePreview;
        private int? comparisonDifficultyIndex;

        // Reference to global settings (now shared between tabs)
        private float GlobalLevelUpMultiplier => window.SharedLevelUpMultiplier;
        private int GlobalStartingExperience => window.SharedStartingExperience;
        private int GlobalStartingLevel => window.SharedStartingLevel;
        private int GlobalMaxLevel => window.SharedMaxLevel;

        // Filter labels for the difficulty filter buttons
        private string[] FilterLabels => new[] { "All" }.Concat(
            difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null
                ? difficultyRatingDatabase.difficultyRatings.Select(r => r.ratingName)
                : Array.Empty<string>()
        ).ToArray();

        public DifficultyTabManager(AdvancedLevelingSystemEditorWindow window, DifficultyRatingDatabase difficultyRatingDatabase)
        {
            this.window = window;
            this.difficultyRatingDatabase = difficultyRatingDatabase;

            // Initialize foldout states
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null)
            {
                foreach (var rating in difficultyRatingDatabase.difficultyRatings)
                {
                    // Use the rating name as the key since uniqueID might not be available
                    string key = rating.ratingName;
                    if (!foldoutStates.ContainsKey(key))
                    {
                        foldoutStates.Add(key, false);
                    }
                }
            }

            // Initialize curve preview
            curvePreview = new LevelingCurvePreview();
        }

        public void OnEnable()
        {
            // Don't initialize styles here - will be done in OnGUI
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            headerStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            descriptionStyle ??= new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                richText = true,
                padding = new RectOffset(5, 5, 5, 5)
            };

            ratingStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(0, 0, 5, 5)
            };

            difficultyNameStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleLeft
            };

            formulaHeaderStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 0)
            };

            formulaDescriptionStyle ??= new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                richText = true,
                fontStyle = FontStyle.Italic,
                padding = new RectOffset(5, 5, 0, 5)
            };

            searchFieldStyle ??= new GUIStyle(EditorStyles.toolbarSearchField)
            {
                margin = new RectOffset(5, 5, 5, 5),
                fixedWidth = 250
            };

            filterButtonStyle ??= new GUIStyle(EditorStyles.miniButton)
            {
                margin = new RectOffset(2, 2, 5, 5),
                padding = new RectOffset(5, 5, 2, 2)
            };

            activeFilterButtonStyle ??= new GUIStyle(filterButtonStyle)
            {
                normal = { background = EditorStyles.toolbarButton.active.background },
                fontStyle = FontStyle.Bold
            };

            tooltipStyle ??= new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 12,
                wordWrap = true,
                richText = true,
                padding = new RectOffset(10, 10, 10, 10)
            };

            buttonStyle ??= new GUIStyle(EditorStyles.miniButton)
            {
                padding = new RectOffset(10, 10, 5, 5)
            };
        }

        public void OnGUI()
        {
            InitializeStyles();

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Difficulty Rating Editor", headerStyle);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Global settings for previews
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Global Preview Settings", EditorStyles.boldLabel);

            // Level Up Multiplier (allow 0 to turn off the multiplier in equations)
            EditorGUI.BeginChangeCheck();
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", GlobalLevelUpMultiplier, 0f, 5.0f);
            if (newLevelUpMultiplier != GlobalLevelUpMultiplier)
            {
                // Update the shared setting in the window
                window.UpdateSharedSettings(levelUpMultiplier: newLevelUpMultiplier);
                // Force repaint to update the graph immediately
                window.Repaint();
            }

            // Show a tooltip about the multiplier
            EditorGUILayout.HelpBox("The Level Up Multiplier affects how quickly experience requirements increase between levels. Higher values create steeper curves.", MessageType.Info);

            // Starting Experience
            EditorGUILayout.BeginHorizontal();
            int newStartingExperience = EditorGUILayout.IntField("Starting Experience", GlobalStartingExperience);
            if (newStartingExperience < 1) newStartingExperience = 1; // Ensure positive value
            if (newStartingExperience != GlobalStartingExperience)
            {
                window.UpdateSharedSettings(startingExperience: newStartingExperience);
                window.Repaint();
            }
            EditorGUILayout.EndHorizontal();

            // Starting Level
            int newStartingLevel = EditorGUILayout.IntField("Starting Level", GlobalStartingLevel);
            if (newStartingLevel < 1) newStartingLevel = 1; // Ensure positive value
            if (newStartingLevel != GlobalStartingLevel)
            {
                window.UpdateSharedSettings(startingLevel: newStartingLevel);
                window.Repaint();
            }

            // Max Level
            int newMaxLevel = EditorGUILayout.IntField("Max Level", GlobalMaxLevel);
            if (newMaxLevel < GlobalStartingLevel + 5) newMaxLevel = GlobalStartingLevel + 5; // Ensure reasonable range
            if (newMaxLevel != GlobalMaxLevel)
            {
                window.UpdateSharedSettings(maxLevel: newMaxLevel);
                window.Repaint();
            }

            // Graph Zooming Toggle
            bool enableGraphZooming = LevelingCurvePreview.IsGraphZoomingEnabled;
            EditorGUI.BeginChangeCheck();
            enableGraphZooming = EditorGUILayout.Toggle(new GUIContent("Enable Graph Zooming", "Automatically use logarithmic scale for steep curves"), enableGraphZooming);
            if (EditorGUI.EndChangeCheck())
            {
                LevelingCurvePreview.IsGraphZoomingEnabled = enableGraphZooming;

                // Force repaint to update the graph immediately
                window.Repaint();
            }

            EditorGUI.EndChangeCheck();

            // Reset All button
            EditorGUILayout.Space(5);
            if (GUILayout.Button("Reset All to Default", GUILayout.Height(25)))
            {
                // Reset all global settings to default values
                window.UpdateSharedSettings(
                    levelUpMultiplier: 1f,
                    startingExperience: 250,
                    startingLevel: 1,
                    maxLevel: 30
                );
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Search and filter
            EditorGUILayout.BeginHorizontal();

            // Search field
            EditorGUILayout.LabelField("Search:", GUILayout.Width(50));
            searchText = EditorGUILayout.TextField(searchText, searchFieldStyle);

            // Clear search button
            if (!string.IsNullOrEmpty(searchText))
            {
                if (GUILayout.Button("Clear", GUILayout.Width(50)))
                {
                    searchText = "";
                    GUI.FocusControl(null);
                }
            }

            // Add some space between search and comparison
            GUILayout.Space(10);

            // Global comparison dropdown button
            if (GUILayout.Button(comparisonDifficultyIndex.HasValue ? "Clear Comparison" : "Compare With...", GUILayout.Width(120)))
            {
                if (comparisonDifficultyIndex.HasValue)
                {
                    comparisonDifficultyIndex = null; // Clear comparison
                }
                else
                {
                    // Show a dropdown to select a difficulty to compare with
                    var menu = new GenericMenu();

                    // Use the difficulty ratings from the database
                    if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null)
                    {
                        for (int i = 0; i < difficultyRatingDatabase.difficultyRatings.Count; i++)
                        {
                            var rating = difficultyRatingDatabase.difficultyRatings[i];
                            int index = i; // Capture the index for the lambda
                            menu.AddItem(new GUIContent(rating.ratingName), false, () =>
                            {
                                // Store the index of the rating as the comparison difficulty
                                comparisonDifficultyIndex = index;
                                window.Repaint();
                            });
                        }
                    }

                    menu.ShowAsContext();
                }
            }

            EditorGUILayout.EndHorizontal();

            // Difficulty filter buttons
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Filter:", GUILayout.Width(50));

            for (int i = 0; i < FilterLabels.Length; i++)
            {
                GUIStyle style = (i == difficultyFilter) ? activeFilterButtonStyle : filterButtonStyle;
                if (GUILayout.Button(FilterLabels[i], style))
                {
                    difficultyFilter = (difficultyFilter == i) ? 0 : i; // Toggle off if already selected
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Collapse/Expand All buttons - larger and centered
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Center buttons

            // Create a custom style for larger buttons with blue background and white text
            var largeButtonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(15, 15, 8, 8),
                normal = { background = CreateColorTexture(new Color(0.2f, 0.4f, 0.8f)), textColor = Color.white }
            };

            if (GUILayout.Button("Collapse All", largeButtonStyle, GUILayout.Width(150), GUILayout.Height(30)))
            {
                CollapseAll();
            }

            GUILayout.Space(10); // Space between buttons

            if (GUILayout.Button("Expand All", largeButtonStyle, GUILayout.Width(150), GUILayout.Height(30)))
            {
                ExpandAll();
            }

            GUILayout.FlexibleSpace(); // Center buttons
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Check if we have a valid database
            if (difficultyRatingDatabase == null || difficultyRatingDatabase.difficultyRatings == null || difficultyRatingDatabase.difficultyRatings.Count == 0)
            {
                EditorGUILayout.HelpBox("No difficulty ratings found. Please create a DifficultyRatingDatabase asset.", MessageType.Warning);
            }
            else
            {
                var ratings = difficultyRatingDatabase.difficultyRatings;

                // Apply filtering
                var filteredRatings = ratings;

                // Apply difficulty filter
                if (difficultyFilter > 0 && difficultyFilter <= ratings.Count)
                {
                    // Filter by the selected rating (difficultyFilter - 1 because index 0 is "All")
                    int ratingIndex = difficultyFilter - 1;
                    filteredRatings = ratings.Where((r, i) => i == ratingIndex).ToList();
                }

                // Apply search filter
                if (!string.IsNullOrEmpty(searchText))
                {
                    string search = searchText.ToLowerInvariant();
                    filteredRatings = filteredRatings.Where(r =>
                        r.ratingName.ToLowerInvariant().Contains(search) ||
                        r.description.ToLowerInvariant().Contains(search) ||
                        // Try to get the algorithm for this rating
                        CheckAlgorithmNameContainsSearch(r.ratingName, search)
                    ).ToList();
                }

                // Display filtered ratings
                if (filteredRatings.Count == 0)
                {
                    EditorGUILayout.HelpBox("No difficulty ratings match your search or filter criteria.", MessageType.Info);
                }
                else
                {
                    foreach (var rating in filteredRatings)
                    {
                        DrawDifficultySection(rating);
                    }
                }
            }

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space(10);
            if (GUILayout.Button("Save Changes", GUILayout.Height(30)))
            {
                SaveChanges();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawDifficultySection(DifficultyRating rating)
        {
            // Get the foldout state for this difficulty using the rating name as the key
            string key = rating.ratingName;
            bool foldout = foldoutStates.TryGetValue(key, out bool value) ? value : false;

            // Create a box for the difficulty section
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Header row with foldout, name, and rating
            EditorGUILayout.BeginHorizontal();

            // Use the difficulty color for the background of the header
            Color originalColor = GUI.backgroundColor;
            GUI.backgroundColor = rating.ratingColor;

            // Display comparison indicator near the foldout if this is the currently compared difficulty
            if (comparisonDifficultyIndex.HasValue &&
                comparisonDifficultyIndex.Value < difficultyRatingDatabase.difficultyRatings.Count &&
                difficultyRatingDatabase.difficultyRatings[comparisonDifficultyIndex.Value] == rating)
            {
                var comparedStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = Color.cyan },
                    alignment = TextAnchor.MiddleRight
                };
                EditorGUILayout.LabelField("⟲", comparedStyle, GUILayout.Width(15));
            }

            // Foldout control
            foldout = EditorGUILayout.Foldout(foldout, "", true, EditorStyles.foldout);
            foldoutStates[key] = foldout;

            // Difficulty name with difficulty color
            var coloredNameStyle = new GUIStyle(difficultyNameStyle)
            {
                normal = { textColor = rating.ratingColor }
            };
            EditorGUILayout.LabelField(rating.ratingName, coloredNameStyle);

            // Difficulty rating as stars with difficulty color
            string ratingStars = "";
            for (int i = 0; i < rating.stars; i++)
            {
                ratingStars += "★";
            }
            for (int i = rating.stars; i < 5; i++)
            {
                ratingStars += "☆";
            }

            // Create a temporary style with the difficulty color
            var coloredRatingStyle = new GUIStyle(ratingStyle)
            {
                normal = { textColor = rating.ratingColor }
            };

            EditorGUILayout.LabelField(ratingStars, coloredRatingStyle, GUILayout.Width(100));

            // Reset background color
            GUI.backgroundColor = originalColor;

            EditorGUILayout.EndHorizontal();

            // If the foldout is expanded, show the details
            if (foldout)
            {
                EditorGUILayout.Space(5);

                // Description
                EditorGUILayout.LabelField("Description", EditorStyles.boldLabel);
                EditorGUI.BeginChangeCheck();
                string newDescription = EditorGUILayout.TextArea(rating.description, descriptionStyle, GUILayout.Height(60));
                if (EditorGUI.EndChangeCheck())
                {
                    rating.description = newDescription;
                    EditorUtility.SetDirty(difficultyRatingDatabase);
                }

                // Add more space between description and formula
                EditorGUILayout.Space(10);

                // Algorithm information
                EditorGUILayout.LabelField("Algorithm", EditorStyles.boldLabel);

                // Get the algorithm from the ScriptableAlgorithmRegistry
                var algorithm = ScriptableAlgorithmRegistry.GetAlgorithmByDifficultyName(rating.ratingName);

                if (algorithm != null)
                {
                    EditorGUILayout.LabelField("Name: " + algorithm.Name);
                    EditorGUILayout.LabelField("Formula:", formulaHeaderStyle);
                    EditorGUILayout.LabelField(algorithm.FormulaExplanation, formulaDescriptionStyle);
                }
                else
                {
                    EditorGUILayout.HelpBox("No algorithm found for this difficulty rating.", MessageType.Warning);

                    // Add a button to view available algorithms
                    if (GUILayout.Button("View Available Algorithms"))
                    {
                        // Show a dropdown with all available algorithms
                        var menu = new GenericMenu();

                        // Get all algorithms from the registry
                        var algorithmsByCategory = ScriptableAlgorithmRegistry.GetAlgorithmsByCategory();

                        foreach (var category in algorithmsByCategory)
                        {
                            foreach (var alg in category.Value)
                            {
                                menu.AddItem(new GUIContent($"{category.Key}/{alg.Name}"), false, () =>
                                {
                                    // Just show information about the algorithm
                                    Debug.Log($"Algorithm: {alg.Name}\nDescription: {alg.Description}\nFormula: {alg.FormulaExplanation}");
                                });
                            }
                        }

                        menu.ShowAsContext();
                    }
                }

                // Add space before the curve preview
                EditorGUILayout.Space(10);

                // Curve preview
                EditorGUILayout.LabelField("Experience Curve Preview", EditorStyles.boldLabel);

                try
                {
                    // Get the algorithm for this rating
                    var ratingAlgorithm = ScriptableAlgorithmRegistry.GetAlgorithmByDifficultyName(rating.ratingName);

                    // If comparison is selected, draw both curves
                    if (comparisonDifficultyIndex.HasValue &&
                        comparisonDifficultyIndex.Value < difficultyRatingDatabase.difficultyRatings.Count &&
                        difficultyRatingDatabase.difficultyRatings[comparisonDifficultyIndex.Value] != rating)
                    {
                        var comparisonRating = difficultyRatingDatabase.difficultyRatings[comparisonDifficultyIndex.Value];
                        var comparisonAlgorithm = ScriptableAlgorithmRegistry.GetAlgorithmByDifficultyName(comparisonRating.ratingName);

                        if (comparisonRating != null && ratingAlgorithm != null && comparisonAlgorithm != null)
                        {
                            try
                            {
                                // Draw both curves with the comparison
                                // Pass the main body width from the editor window template
                                float availableWidth = EditorWindowTemplate.MainBodyRect.width;
                                curvePreview.DrawCurvePreview(
                                    ratingAlgorithm,
                                    GlobalLevelUpMultiplier,
                                    comparisonAlgorithm,
                                    GlobalStartingExperience,
                                    GlobalStartingLevel,
                                    GlobalMaxLevel,
                                    availableWidth
                                );

                                // Add legend
                                EditorGUILayout.BeginHorizontal();
                                EditorGUILayout.LabelField($"<color=#{ColorUtility.ToHtmlStringRGB(rating.ratingColor)}>{rating.ratingName}</color> vs <color=#{ColorUtility.ToHtmlStringRGB(comparisonRating.ratingColor)}>{comparisonRating.ratingName}</color>",
                                    new GUIStyle(EditorStyles.label) { richText = true });
                                EditorGUILayout.EndHorizontal();
                            }
                            catch (Exception ex)
                            {
                                EditorGUILayout.HelpBox($"Error drawing comparison curve: {ex.Message}", MessageType.Error);
                            }
                        }
                    }
                    else if (ratingAlgorithm != null)
                    {
                        // Draw just the single curve
                        // Pass the main body width from the editor window template
                        float availableWidth = EditorWindowTemplate.MainBodyRect.width;
                        curvePreview.DrawCurvePreview(
                            ratingAlgorithm,
                            GlobalLevelUpMultiplier,
                            null,
                            GlobalStartingExperience,
                            GlobalStartingLevel,
                            GlobalMaxLevel,
                            availableWidth
                        );
                    }
                    else
                    {
                        EditorGUILayout.HelpBox("Cannot draw curve preview: No algorithm found for this difficulty rating.", MessageType.Warning);
                    }
                }
                catch (Exception ex)
                {
                    EditorGUILayout.HelpBox($"Error drawing curve: {ex.Message}", MessageType.Error);
                }

                EditorGUILayout.Space(5);
            }

            EditorGUILayout.EndVertical();
        }



        private void SaveChanges()
        {
            EditorUtility.SetDirty(difficultyRatingDatabase);
            AssetDatabase.SaveAssets();
            Debug.Log("Difficulty ratings saved successfully.");
        }

        // Set all foldout states to collapsed (false)
        private void CollapseAll()
        {
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null)
            {
                foreach (var rating in difficultyRatingDatabase.difficultyRatings)
                {
                    // Use the rating name as the key
                    string key = rating.ratingName;
                    foldoutStates[key] = false;
                }
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        // Set all foldout states to expanded (true)
        private void ExpandAll()
        {
            if (difficultyRatingDatabase != null && difficultyRatingDatabase.difficultyRatings != null)
            {
                foreach (var rating in difficultyRatingDatabase.difficultyRatings)
                {
                    // Use the rating name as the key
                    string key = rating.ratingName;
                    foldoutStates[key] = true;
                }
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        // Helper method to create a colored texture for UI elements
        private Texture2D CreateColorTexture(Color color)
        {
            var texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }

        /// <summary>
        /// Helper method to check if an algorithm name contains the search text
        /// </summary>
        private bool CheckAlgorithmNameContainsSearch(string ratingName, string searchText)
        {
            var algorithm = ScriptableAlgorithmRegistry.GetAlgorithmByDifficultyName(ratingName);
            if (algorithm != null)
            {
                return algorithm.Name.ToLowerInvariant().Contains(searchText);
            }
            return false;
        }
    }
}
