using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class ExperienceRewardsTabManager : ITabManager
    {
        private AdvancedLevelingSystemEditorWindow window;
        private ExperienceRewardsData rewardsData;
        private Vector2 leftScrollPosition;
        private Vector2 rightScrollPosition;


        // New property that uses ScriptableObject-based algorithm
        private LevelingAlgorithmBase selectedAlgorithm;

        // Tab properties
        public string TabName => "Experience Rewards";

        // Use shared settings from the window
        private float LevelUpMultiplier => window.SharedLevelUpMultiplier;
        private int StartingExperience => window.SharedStartingExperience;
        private int StartingLevel => window.SharedStartingLevel;
        private int MaxLevel => window.SharedMaxLevel;
        private int levelRangeSize = 5;
        private bool showDetailedBreakdown = true;
        // Split view ratio is now hardcoded in the OnGUI method

        // Graph visualization
        private LevelingCurvePreview curvePreview;

        // Foldout states for level ranges
        private Dictionary<int, bool> levelRangeFoldoutStates = new Dictionary<int, bool>();

        // Reward categories and their multipliers (stored as decimal values, displayed as percentages)
        private Dictionary<string, float> rewardCategories = new Dictionary<string, float>();

        // Cached experience requirements
        private List<int> experienceRequirements;

        // Styles
        private GUIStyle headerStyle;
        private GUIStyle subHeaderStyle;
        private GUIStyle labelStyle;
        private GUIStyle valueStyle;
        private GUIStyle categoryHeaderStyle;
        private GUIStyle levelRangeStyle;
        private GUIStyle tooltipStyle;

        public ExperienceRewardsTabManager(AdvancedLevelingSystemEditorWindow window, ExperienceRewardsData rewardsData)
        {
            this.window = window;
            this.rewardsData = rewardsData;

            // Initialize curve preview
            curvePreview = new LevelingCurvePreview();

            LoadData();
        }

        public void OnEnable()
        {
            LoadData();
            CalculateExperienceRequirements();
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (subHeaderStyle == null)
            {
                subHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleLeft
                };
            }

            if (labelStyle == null)
            {
                labelStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleLeft
                };
            }

            if (valueStyle == null)
            {
                valueStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleRight,
                    fontStyle = FontStyle.Bold
                };
            }

            if (categoryHeaderStyle == null)
            {
                categoryHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(0, 0, 10, 5)
                };
            }

            if (levelRangeStyle == null)
            {
                levelRangeStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 13,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };
            }

            if (tooltipStyle == null)
            {
                tooltipStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 12,
                    wordWrap = true,
                    richText = true,
                    padding = new RectOffset(10, 10, 10, 10)
                };
            }
        }

        #region Data Management
        private void LoadData()
        {
            // Try to load the default rewards data if not provided
            if (rewardsData == null)
            {
                rewardsData = Resources.Load<ExperienceRewardsData>("DefaultExperienceRewardsData");

                // If still null, create a new instance with default values
                if (rewardsData == null)
                {
                    // Initialize with default reward categories
                    InitializeDefaultRewardCategories();
                    return;
                }
            }

            // Load the algorithm if available
            selectedAlgorithm = rewardsData.selectedAlgorithm;

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // Note: levelUpMultiplier, startingExperience, startingLevel, and maxLevel are now shared settings
            // and are accessed through properties that get values from the window
            levelRangeSize = rewardsData.levelRangeSize;
            showDetailedBreakdown = rewardsData.showDetailedBreakdown;

            // Update the shared settings in the window
            window.UpdateSharedSettings(
                levelUpMultiplier: rewardsData.levelUpMultiplier,
                startingExperience: rewardsData.startingExperience,
                startingLevel: rewardsData.startingLevel,
                maxLevel: rewardsData.maxLevel
            );

            // Load reward categories
            rewardCategories.Clear();
            if (rewardsData.rewardCategories != null && rewardsData.rewardCategories.Count > 0)
            {
                foreach (var category in rewardsData.rewardCategories)
                {
                    rewardCategories[category.name] = category.multiplier;
                }
            }
            else
            {
                // Initialize with default reward categories if none exist
                InitializeDefaultRewardCategories();
            }
        }

        private void InitializeDefaultRewardCategories()
        {
            // Clear existing categories
            rewardCategories.Clear();

            // Add default reward categories with their multipliers in the desired order
            // Quest categories
            rewardCategories.Add("Epic Quest", 0.12f);
            rewardCategories.Add("Hard Quest", 0.08f);
            rewardCategories.Add("Medium Quest", 0.05f);
            rewardCategories.Add("Easy Quest", 0.03f);
            rewardCategories.Add("Daily Activity", 0.02f);

            // Boss and special enemies
            rewardCategories.Add("World Boss", 0.10f);
            rewardCategories.Add("Boss Enemy", 0.05f);
            rewardCategories.Add("Elite Enemy", 0.015f);

            // Regular enemies
            rewardCategories.Add("Hard Enemy", 0.003f);
            rewardCategories.Add("Medium Enemy", 0.002f);
            rewardCategories.Add("Easy Enemy", 0.001f);
            rewardCategories.Add("Very Easy Enemy", 0.0005f);

            // Crafting and gathering
            rewardCategories.Add("Crafting Item", 0.0001f);
            rewardCategories.Add("Gathering Resource", 0.0001f);

            // If we have a rewards data object, update it
            if (rewardsData != null)
            {
                UpdateRewardCategoriesInScriptableObject();
            }
        }

        private void UpdateRewardCategoriesInScriptableObject()
        {
            if (rewardsData != null)
            {
                // Clear and update reward categories
                rewardsData.rewardCategories.Clear();
                foreach (var category in rewardCategories)
                {
                    rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory
                    {
                        name = category.Key,
                        multiplier = category.Value
                    });
                }

                // Mark the asset as dirty so Unity knows it needs to be saved
                EditorUtility.SetDirty(rewardsData);
                AssetDatabase.SaveAssets();
            }
        }

        private void SaveData()
        {
            if (rewardsData == null)
            {
                return;
            }

            // Update the algorithm
            rewardsData.selectedAlgorithm = selectedAlgorithm;

            rewardsData.levelUpMultiplier = LevelUpMultiplier;
            rewardsData.startingExperience = StartingExperience;
            rewardsData.startingLevel = StartingLevel;
            rewardsData.maxLevel = MaxLevel;
            rewardsData.levelRangeSize = levelRangeSize;
            rewardsData.showDetailedBreakdown = showDetailedBreakdown;

            // Save reward scaling settings
            // Note: These are already saved when changed in the UI, but we save them again here for consistency
            // and to ensure they're saved when the Save button is clicked

            // Update reward categories
            UpdateRewardCategoriesInScriptableObject();
        }
        #endregion Data Management

        public void OnGUI()
        {
            InitializeStyles();

            // Set a minimum window width to prevent horizontal scrollbars
            float minWindowWidth = 1000f; // Minimum width for the entire window
            if (window.position.width < minWindowWidth)
            {
                EditorGUILayout.HelpBox($"Please resize the window to at least {minWindowWidth}px width for optimal viewing.", MessageType.Info);
            }

            // Split view layout - using flexible layout
            EditorGUILayout.BeginHorizontal();

            #region Left Panel (Settings)
            // Calculate the available width (accounting for the left navigation panel)
            float availableWidth = EditorWindowTemplate.MainBodyRect.width;
            // Use a fixed ratio for left panel (50% of available width)
            // Subtract 20 pixels for the separator and add some buffer
            float leftPanelWidth = (availableWidth - 20) * 0.49f;
            EditorGUILayout.BeginVertical(GUILayout.Width(leftPanelWidth), GUILayout.ExpandHeight(true));

            // Always use a scrollview for the entire left panel
            leftScrollPosition = EditorGUILayout.BeginScrollView(
                leftScrollPosition,
                false, // horizontal scrollbar
                true,  // vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));


            // Add a heading for the level ranges section
            Rect experienceRewardsCalculatorRect = EditorGUILayout.GetControlRect(false, 30);
            EditorGUI.DrawRect(experienceRewardsCalculatorRect, new Color(0.3f, 0.3f, 0.4f));

            // Create a style for the level ranges header
            GUIStyle experienceRewardsCalculatorStyle = new GUIStyle(EditorStyles.boldLabel);
            experienceRewardsCalculatorStyle.fontSize = 13;
            experienceRewardsCalculatorStyle.alignment = TextAnchor.MiddleCenter;
            experienceRewardsCalculatorStyle.normal.textColor = Color.white;

            // Display the level ranges heading
            EditorGUI.LabelField(experienceRewardsCalculatorRect, "Experience Rewards Calculator", experienceRewardsCalculatorStyle);

            EditorGUILayout.Space(15);

            // Draw the leveling curve graph
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Draw the graph with a fixed height and expanded width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Add a title for the graph
                EditorGUILayout.LabelField("Leveling Curve Preview", subHeaderStyle);
                EditorGUILayout.Space(5);

                // Use the algorithm if available, otherwise fall back to the legacy difficulty
                if (selectedAlgorithm != null)
                {
                    curvePreview.DrawCurvePreview(selectedAlgorithm, LevelUpMultiplier, StartingExperience, StartingLevel, MaxLevel, leftPanelWidth - 20);
                }
                else
                {
                    // If no algorithm is selected, show a message
                    EditorGUILayout.HelpBox("Please select a leveling algorithm to preview the curve.", MessageType.Info);
                }

                EditorGUILayout.EndVertical();

                EditorGUILayout.Space(10);
            }

            // Add a separator line
            Rect mainSeparatorRect1 = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(mainSeparatorRect1, new Color(0.3f, 0.3f, 0.3f, 0.8f));

            EditorGUILayout.Space(10);

            // Settings section - ensure it expands to fill width
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));
            EditorGUILayout.LabelField("Settings", categoryHeaderStyle);

            // Algorithm selection
            EditorGUI.BeginChangeCheck();

            // Load all available algorithms
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Create a list of algorithm names for the popup
                List<string> algorithmNames = new List<string>();
                List<LevelingAlgorithmBase> algorithms = new List<LevelingAlgorithmBase>();

                // Add all algorithms from the database
                foreach (var algorithm in algorithmDB.algorithms)
                {
                    algorithmNames.Add(algorithm.Name);
                    algorithms.Add(algorithm);
                }

                // Find the index of the currently selected algorithm
                int selectedIndex = 0;
                if (selectedAlgorithm != null)
                {
                    for (int i = 0; i < algorithms.Count; i++)
                    {
                        if (algorithms[i] != null && algorithms[i].uniqueID == selectedAlgorithm.uniqueID)
                        {
                            selectedIndex = i;
                            break;
                        }
                    }
                }

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    selectedIndex,
                    "Leveling Algorithm",
                    algorithmNames.ToArray(),
                    (newIndex) => {
                        selectedAlgorithm = algorithms[newIndex];
                        CalculateExperienceRequirements();
                    }
                );
            }
            else
            {
                // Show a warning if no algorithms are available
                EditorGUILayout.HelpBox("No leveling algorithms found. Please create at least one algorithm in the Algorithm Database.", MessageType.Warning);

                // Add a button to create a default algorithm
                if (GUILayout.Button("Create Default Algorithm Database", GUILayout.Height(30)))
                {
                    // Create a default algorithm database
                    AlgorithmDatabaseManager.CreateDefaultAlgorithmDatabase();

                    // Refresh the asset database
                    AssetDatabase.Refresh();
                }
            }

            // Level Up Multiplier (minimum 1.0 to avoid issues with values below 1.0)
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", LevelUpMultiplier, 1.0f, 5.0f);
            if (newLevelUpMultiplier != LevelUpMultiplier)
            {
                window.UpdateSharedSettings(levelUpMultiplier: newLevelUpMultiplier);
                CalculateExperienceRequirements();
            }

            // Show a tooltip about the multiplier
            EditorGUILayout.HelpBox("The Level Up Multiplier affects how quickly experience requirements increase between levels. Higher values create steeper curves.", MessageType.Info);

            // Starting Experience
            int newStartingExperience = EditorGUILayout.IntField("Starting Experience", StartingExperience);
            if (newStartingExperience < 1) newStartingExperience = 1;
            if (newStartingExperience != StartingExperience)
            {
                window.UpdateSharedSettings(startingExperience: newStartingExperience);
                CalculateExperienceRequirements();
            }

            // Starting Level
            int newStartingLevel = EditorGUILayout.IntField("Starting Level", StartingLevel);
            if (newStartingLevel < 1) newStartingLevel = 1;
            if (newStartingLevel != StartingLevel)
            {
                window.UpdateSharedSettings(startingLevel: newStartingLevel);
                CalculateExperienceRequirements();
            }

            // Max Level
            int newMaxLevel = EditorGUILayout.IntField("Max Level", MaxLevel);
            if (newMaxLevel < StartingLevel + 5) newMaxLevel = StartingLevel + 5;
            if (newMaxLevel != MaxLevel)
            {
                window.UpdateSharedSettings(maxLevel: newMaxLevel);
                CalculateExperienceRequirements();
            }

            // Level range size
            int newLevelRangeSize = EditorGUILayout.IntSlider("Level Range Size", levelRangeSize, 1, 10);
            if (newLevelRangeSize != levelRangeSize)
            {
                levelRangeSize = newLevelRangeSize;
                CalculateExperienceRequirements();
            }

            // Show detailed breakdown
            bool newShowDetailedBreakdown = EditorGUILayout.Toggle("Show Detailed Breakdown", showDetailedBreakdown);
            if (newShowDetailedBreakdown != showDetailedBreakdown)
            {
                showDetailedBreakdown = newShowDetailedBreakdown;
                // No need to recalculate, just update the UI
                window.Repaint();
            }

            EditorGUILayout.Space(10);

            // Add a separator line
            Rect rewardScalingSeparatorRect = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(rewardScalingSeparatorRect, new Color(0.3f, 0.3f, 0.3f, 0.8f));

            EditorGUILayout.Space(5);

            // Reward Scaling Information
            EditorGUILayout.LabelField("Reward Scaling", categoryHeaderStyle);
            EditorGUILayout.HelpBox("Rewards automatically scale based on the selected algorithm and level up multiplier. As XP requirements increase with level, rewards become a smaller relative percentage.", MessageType.Info);

            if (selectedAlgorithm != null)
            {
                EditorGUILayout.LabelField($"Using algorithm: {selectedAlgorithm.Name}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Level Up Multiplier: {LevelUpMultiplier:F2}x", EditorStyles.boldLabel);
            }
            else
            {
                EditorGUILayout.HelpBox("No algorithm selected. Please select an algorithm to see reward calculations.", MessageType.Warning);
            }

            if (EditorGUI.EndChangeCheck())
            {
                CalculateExperienceRequirements();

                // Update the ScriptableObject with the new values
                if (rewardsData != null)
                {
                    // selectedDifficulty is no longer used
                    rewardsData.levelUpMultiplier = LevelUpMultiplier;
                    rewardsData.startingExperience = StartingExperience;
                    rewardsData.startingLevel = StartingLevel;
                    rewardsData.maxLevel = MaxLevel;
                    rewardsData.levelRangeSize = levelRangeSize;
                    rewardsData.showDetailedBreakdown = showDetailedBreakdown;

                    // Mark the asset as dirty so Unity knows it needs to be saved
                    EditorUtility.SetDirty(rewardsData);
                }
            }

            EditorGUILayout.Space(5);

            // Reset button
            if (GUILayout.Button("Reset to Default", GUILayout.Height(25)))
            {
                // Try to find a SineWave algorithm in the database
                if (algorithmDB != null)
                {
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a.Name.ToLower().Contains("sine") || a.Name.ToLower().Contains("wave"));

                    // If no SineWave algorithm is found, use the first one
                    if (selectedAlgorithm == null && algorithmDB.algorithms.Count > 0)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }
                }

                // If no algorithm is selected, try to find one in the database
                if (selectedAlgorithm == null)
                {
                    AlgorithmDatabase defaultDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                    if (defaultDB != null && defaultDB.algorithms.Count > 0)
                    {
                        selectedAlgorithm = defaultDB.algorithms[0];
                    }
                }

                window.UpdateSharedSettings(
                    levelUpMultiplier: 1.1f,
                    startingExperience: 250,
                    startingLevel: 1,
                    maxLevel: 50
                );
                levelRangeSize = 5;
                showDetailedBreakdown = true;

                // No need to reset reward scaling settings as they're now tied to the algorithm

                CalculateExperienceRequirements();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Reward Categories section - ensure it expands to fill width
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));
            EditorGUILayout.LabelField("Reward Categories", categoryHeaderStyle);

            // Display each reward category with a slider
            foreach (var category in rewardCategories.Keys.ToList())
            {
                EditorGUILayout.BeginHorizontal();

                // Allow renaming the category
                string newCategory = EditorGUILayout.TextField(category, GUILayout.Width(120));

                // If the category name changed, update the dictionary
                if (newCategory != category && !rewardCategories.ContainsKey(newCategory))
                {
                    float value = rewardCategories[category];
                    rewardCategories.Remove(category);
                    rewardCategories.Add(newCategory, value);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();
                }
                else
                {
                    // Update the multiplier value with finer control (0.001 to 1.0)
                    float multiplier = rewardCategories[category];
                    float percentValue = multiplier * 100f; // Convert to percentage for display

                    EditorGUI.BeginChangeCheck();
                    // Create a slider for the full range (0.001% - 100%)
                    float newPercentValue = EditorGUILayout.Slider(percentValue, 0.001f, 100f, GUILayout.ExpandWidth(true));
                    if (EditorGUI.EndChangeCheck())
                    {
                        percentValue = newPercentValue;
                        multiplier = percentValue / 100f;
                        rewardCategories[category] = multiplier;

                        // Update the ScriptableObject
                        UpdateRewardCategoriesInScriptableObject();
                    }

                    // Note: Direct input field is currently disabled
                    // We could re-enable it in the future if needed
                }

                // Display the % symbol
                GUILayout.Label("%", GUILayout.Width(15));

                // Delete button
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    rewardCategories.Remove(category);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    GUIUtility.ExitGUI(); // Prevent GUI errors when modifying collection during iteration
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add new category button
            EditorGUILayout.Space(5);
            EditorGUI.BeginChangeCheck();
            if (GUILayout.Button("Add New Category", GUILayout.Height(25)))
            {
                string newCategoryName = "New Category";
                int suffix = 0;

                // Ensure unique name
                while (rewardCategories.ContainsKey(newCategoryName + (suffix == 0 ? "" : suffix.ToString())))
                {
                    suffix++;
                }

                rewardCategories.Add(newCategoryName + suffix, 0.25f);

                // Update the ScriptableObject
                UpdateRewardCategoriesInScriptableObject();
            }

            if (EditorGUI.EndChangeCheck())
            {
                CalculateExperienceRequirements();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Save button
            if (GUILayout.Button("Save Settings", GUILayout.Height(30)))
            {
                SaveData();
                Debug.Log("Experience rewards settings saved successfully.");
            }

            // End the scrollview for the entire left panel
            EditorGUILayout.EndScrollView();

            EditorGUILayout.EndVertical();
            #endregion Left Panel

            // Add a visual separator between panels (thinner)
            GUILayout.Space(5);
            Rect verticalSeparatorRect = GUILayoutUtility.GetRect(1, EditorWindowTemplate.MainBodyRect.height);
            EditorGUI.DrawRect(verticalSeparatorRect, new Color(0.4f, 0.4f, 0.4f, 0.4f));
            GUILayout.Space(5);

            #region Right Panel (Results)
            // Use all remaining width for the right panel
            // Calculate the right panel width as the remaining available space
            float rightPanelWidth = (availableWidth - 20) * 0.49f; // Use same calculation as left panel
            EditorGUILayout.BeginVertical(GUILayout.Width(rightPanelWidth), GUILayout.ExpandHeight(true));

            // Begin scrollview for right panel with flexible width
            // Use GUILayout options to ensure the scrollbar is accounted for in the layout
            rightScrollPosition = EditorGUILayout.BeginScrollView(
                rightScrollPosition,
                false,  // horizontal scrollbar
                true,   // vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));

            // Results section
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Calculate average experience for the selected level range
                int totalExp = 0;
                int count = 0;
                int startIdx = Mathf.Max(0, StartingLevel - 1);
                int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + levelRangeSize - 1);

                for (int i = startIdx; i <= endIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        totalExp += experienceRequirements[i];
                        count++;
                    }
                }

                int averageExp = (count > 0) ? totalExp / count : 0;

                // We'll move the detailed breakdown to be displayed with each level range

                // Add a heading for the level ranges section that fills the available width
                Rect levelRangesHeaderRect = EditorGUILayout.GetControlRect(false, 30, GUILayout.ExpandWidth(true));
                EditorGUI.DrawRect(levelRangesHeaderRect, new Color(0.3f, 0.3f, 0.4f));

                // Create a style for the level ranges header
                GUIStyle levelRangesHeadingStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 13,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white },
                    stretchWidth = true
                };

                // Display the level ranges heading - make sure it fits within the available width
                string headerText = "Level Progression";
                EditorGUI.LabelField(levelRangesHeaderRect, headerText, levelRangesHeadingStyle);

                EditorGUILayout.Space(10);

                // Add Collapse All / Expand All buttons
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace(); // Center buttons

                // Create a compact button style with blue background and white text
                GUIStyle compactButtonStyle = new GUIStyle(GUI.skin.button)
                {
                    fontSize = 10,
                    fontStyle = FontStyle.Bold,
                    padding = new RectOffset(3, 3, 2, 2),
                    normal = { textColor = Color.white, background = CreateColorTexture(new Color(0.2f, 0.4f, 0.8f)) }
                };

                if (GUILayout.Button("Collapse", compactButtonStyle, GUILayout.Width(70), GUILayout.Height(22)))
                {
                    CollapseAllLevelRanges();
                }

                GUILayout.Space(5); // Space between buttons

                if (GUILayout.Button("Expand", compactButtonStyle, GUILayout.Width(70), GUILayout.Height(22)))
                {
                    ExpandAllLevelRanges();
                }

                GUILayout.FlexibleSpace(); // Center buttons
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(8);

                // Add a separator line
                Rect mainSeparatorRect = EditorGUILayout.GetControlRect(false, 2);
                EditorGUI.DrawRect(mainSeparatorRect, new Color(0.3f, 0.3f, 0.3f, 0.8f));

                EditorGUILayout.Space(10);

                // Display multiple level ranges with their recommended rewards
                DisplayLevelRangeRewards(StartingLevel, levelRangeSize, averageExp);

                // Calculate how many ranges we need to display all levels up to MaxLevel
                int totalRangesNeeded = Mathf.CeilToInt((float)(MaxLevel - StartingLevel + 1) / levelRangeSize);

                // Display additional level ranges
                for (int rangeIndex = 1; rangeIndex < totalRangesNeeded; rangeIndex++)
                {
                    int rangeStartLevel = StartingLevel + (rangeIndex * levelRangeSize);
                    int rangeEndLevel = Mathf.Min(MaxLevel, rangeStartLevel + levelRangeSize - 1);

                    // Calculate average experience for this level range
                    int rangeTotalExp = 0;
                    int rangeCount = 0;
                    int rangeStartIdx = Mathf.Max(0, rangeStartLevel - 1);
                    int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + levelRangeSize - 1);

                    for (int i = rangeStartIdx; i <= rangeEndIdx; i++)
                    {
                        if (i < experienceRequirements.Count)
                        {
                            rangeTotalExp += experienceRequirements[i];
                            rangeCount++;
                        }
                    }

                    int rangeAverageExp = (rangeCount > 0) ? rangeTotalExp / rangeCount : 0;

                    EditorGUILayout.Space(15);
                    DisplayLevelRangeRewards(rangeStartLevel, levelRangeSize, rangeAverageExp);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No experience requirements calculated. Please check your settings.", MessageType.Warning);
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
            #endregion Right Panel

            // Add a small space at the end to prevent horizontal scrollbar
            GUILayout.Space(2);
            EditorGUILayout.EndHorizontal();
        }

        #region UI Helper Methods
        private void DisplayLevelRangeRewards(int startLevel, int rangeSize, int averageExp)
        {
            // Get or initialize the foldout state for this level range
            if (!levelRangeFoldoutStates.ContainsKey(startLevel))
            {
                levelRangeFoldoutStates[startLevel] = true; // Default to expanded
            }

            // Level Range Header with foldout - ensure it fills the available width
            Rect levelRangeHeaderRect = EditorGUILayout.GetControlRect(false, 30, GUILayout.ExpandWidth(true));
            EditorGUI.DrawRect(levelRangeHeaderRect, new Color(0.2f, 0.3f, 0.4f));

            // Create a style for the level range header that stretches to fill width
            GUIStyle levelRangeHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = Color.white },
                stretchWidth = true
            };

            // Create a style for the foldout arrow
            GUIStyle foldoutStyle = new GUIStyle(EditorStyles.foldout)
            {
                normal = { textColor = Color.white },
                onNormal = { textColor = Color.white },
                fontStyle = FontStyle.Bold,
                fontSize = 12
            };

            // Display the level range with foldout
            int endLevel = Mathf.Min(MaxLevel, startLevel + rangeSize - 1);
            string levelRangeText = $"Level {startLevel} - {endLevel}";

            // Create a rect for the foldout arrow
            Rect foldoutRect = new Rect(levelRangeHeaderRect.x + 10, levelRangeHeaderRect.y, 20, levelRangeHeaderRect.height);

            // Create a rect for the level range text
            Rect labelRect = new Rect(levelRangeHeaderRect.x + 30, levelRangeHeaderRect.y, levelRangeHeaderRect.width - 40, levelRangeHeaderRect.height);

            // Handle the foldout click
            bool wasExpanded = levelRangeFoldoutStates[startLevel];
            levelRangeFoldoutStates[startLevel] = EditorGUI.Foldout(foldoutRect, wasExpanded, "", true, foldoutStyle);

            // Display the level range text
            EditorGUI.LabelField(labelRect, levelRangeText, levelRangeHeaderStyle);

            // Make the entire header clickable to toggle foldout
            if (Event.current.type == EventType.MouseDown && levelRangeHeaderRect.Contains(Event.current.mousePosition))
            {
                levelRangeFoldoutStates[startLevel] = !levelRangeFoldoutStates[startLevel];
                Event.current.Use();
                window.Repaint();
            }

            // Only show content if expanded
            if (levelRangeFoldoutStates[startLevel])
            {
                // Create a background for the content that expands to fill width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Display the average experience
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Average XP Required:", subHeaderStyle);
                EditorGUILayout.LabelField(averageExp.ToString("N0") + " XP", valueStyle);
                EditorGUILayout.EndHorizontal();

            // Display detailed breakdown if enabled
            if (showDetailedBreakdown)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Level Progression Breakdown", subHeaderStyle);

                // Table header with consistent widths
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", subHeaderStyle, GUILayout.Width(35));

                // Create right-aligned header style for Required XP
                GUIStyle rightAlignedLevelHeaderStyle = new GUIStyle(subHeaderStyle);
                rightAlignedLevelHeaderStyle.alignment = TextAnchor.MiddleRight;

                EditorGUILayout.LabelField("Req XP", rightAlignedLevelHeaderStyle, GUILayout.Width(80));
                EditorGUILayout.LabelField("Inc %", rightAlignedLevelHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Display each level in the range
                int startIdx = Mathf.Max(0, startLevel - 1);
                int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + rangeSize - 1);

                for (int i = startIdx; i <= endIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        EditorGUILayout.BeginHorizontal();

                        // Level number
                        EditorGUILayout.LabelField((i + 1).ToString(), labelStyle, GUILayout.Width(35));

                        // Required XP - ensure it's right-aligned
                        // Create a dedicated style for the XP values with right alignment
                        GUIStyle xpValueStyle = new GUIStyle(valueStyle);
                        xpValueStyle.alignment = TextAnchor.MiddleRight;
                        EditorGUILayout.LabelField(experienceRequirements[i].ToString("N0"), xpValueStyle, GUILayout.Width(80));

                        // Percentage increase from previous level
                        if (i > 0)
                        {
                            float increase = ((float)experienceRequirements[i] / experienceRequirements[i - 1] - 1) * 100;
                            string increaseText = increase.ToString("0.0") + "%";

                            // Color code the increase percentage
                            GUIStyle increaseStyle = new GUIStyle(valueStyle);
                            increaseStyle.alignment = TextAnchor.MiddleRight; // Right-align the increase percentage
                            if (increase < 5)
                                increaseStyle.normal.textColor = new Color(0.0f, 0.8f, 0.0f); // Green for small increases
                            else if (increase > 15)
                                increaseStyle.normal.textColor = new Color(0.8f, 0.0f, 0.0f); // Red for large increases

                            EditorGUILayout.LabelField(increaseText, increaseStyle, GUILayout.Width(60));
                        }
                        else
                        {
                            GUIStyle dashStyle = new GUIStyle(valueStyle);
                            dashStyle.alignment = TextAnchor.MiddleRight; // Right-align the dash
                            EditorGUILayout.LabelField("-", dashStyle, GUILayout.Width(60));
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }
            }

            // Rewards Header - ensure it fills the available width
            Rect rewardsHeaderRect = EditorGUILayout.GetControlRect(false, 30, GUILayout.ExpandWidth(true));
            EditorGUI.DrawRect(rewardsHeaderRect, new Color(0.3f, 0.4f, 0.2f));

            // Create a style for the rewards header that stretches to fill width
            GUIStyle rewardsHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 12,
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = Color.white },
                stretchWidth = true
            };

            // Display the rewards header with scaling indicator
            string rewardsHeaderText = "Rewards (Algorithm-Scaled)";
            EditorGUI.LabelField(rewardsHeaderRect, rewardsHeaderText, rewardsHeaderStyle);

            EditorGUILayout.Space(5);

            // Calculate total experience needed for this level range
            int totalExpForRange = 0;
            int rangeStartIdx = Mathf.Max(0, startLevel - 1);
            int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + rangeSize - 1);

            for (int i = rangeStartIdx; i < rangeEndIdx; i++)
            {
                if (i < experienceRequirements.Count - 1)
                {
                    totalExpForRange += experienceRequirements[i];
                }
            }

            // Display total experience needed for this level range
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Total XP Needed:", subHeaderStyle);
            EditorGUILayout.LabelField(totalExpForRange.ToString("N0") + " XP", valueStyle);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Table header with fixed widths to ensure proper layout
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));

            // Create right-aligned header style for XP Reward and % of Level
            GUIStyle rightAlignedHeaderStyle = new GUIStyle(subHeaderStyle)
            {
                alignment = TextAnchor.MiddleRight
            };

            EditorGUILayout.LabelField("XP", rightAlignedHeaderStyle, GUILayout.Width(60));
            EditorGUILayout.LabelField("% Level", rightAlignedHeaderStyle, GUILayout.Width(60));
            EditorGUILayout.LabelField("Need", rightAlignedHeaderStyle, GUILayout.Width(50));
            EditorGUILayout.EndHorizontal();

            // Draw a separator line
            Rect rewardsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
            EditorGUI.DrawRect(rewardsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

            // Sort categories by reward amount (descending)
            var sortedCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();

            foreach (var category in sortedCategories)
            {
                // Get the average level for this range
                int avgLevel = startLevel + (rangeSize / 2);

                // Calculate the scaled reward based on the level
                int rewardAmount = CalculateExperienceReward(category.Value, averageExp, avgLevel);

                // Calculate the effective percentage (may be different from base percentage due to scaling)
                float effectiveMultiplier = CalculateScaledRewardMultiplier(avgLevel, category.Value);
                float percentOfLevel = effectiveMultiplier * 100;

                // Add a tooltip to show the base percentage vs. scaled percentage
                string percentTooltip = "";
                // Always show the tooltip since scaling is now based on the algorithm
                float basePercent = category.Value * 100;
                percentTooltip = $"Base: {basePercent.ToString("0.000")}% → Scaled: {percentOfLevel.ToString("0.000")}%";

                // Add algorithm info to the tooltip
                if (selectedAlgorithm != null)
                {
                    percentTooltip += $"\nAlgorithm: {selectedAlgorithm.Name}, Level: {avgLevel}";
                }

                // Calculate how many of this reward type are needed to level up
                int numNeeded = rewardAmount > 0 ? Mathf.CeilToInt(averageExp / (float)rewardAmount) : 0;

                EditorGUILayout.BeginHorizontal();

                // Activity type
                EditorGUILayout.LabelField(category.Key, labelStyle, GUILayout.Width(100));

                // XP reward - ensure it's right-aligned
                GUIStyle xpRewardStyle = new GUIStyle(valueStyle)
                {
                    alignment = TextAnchor.MiddleRight
                };
                EditorGUILayout.LabelField(rewardAmount.ToString("N0"), xpRewardStyle, GUILayout.Width(60));

                // Percentage of level
                GUIStyle percentStyle = new GUIStyle(valueStyle)
                {
                    alignment = TextAnchor.MiddleRight // Right-align the percentage
                };
                if (percentOfLevel < 10)
                    percentStyle.normal.textColor = new Color(0.0f, 0.7f, 0.0f); // Green for small rewards
                else if (percentOfLevel > 50)
                    percentStyle.normal.textColor = new Color(0.7f, 0.0f, 0.0f); // Red for large rewards

                // Format percentage with appropriate precision based on its value
                string percentFormat;
                if (percentOfLevel < 0.01f)
                    percentFormat = "0.000"; // Show 3 decimal places for very small values (below 0.01%)
                else if (percentOfLevel < 0.1f)
                    percentFormat = "0.00"; // Show 2 decimal places for small values (below 0.1%)
                else
                    percentFormat = "0.0"; // Show 1 decimal place for normal values

                // Create a GUIContent with the percentage and tooltip
                GUIContent percentContent = new GUIContent(
                    percentOfLevel.ToString(percentFormat) + "%",
                    string.IsNullOrEmpty(percentTooltip) ? null : percentTooltip
                );

                EditorGUILayout.LabelField(percentContent, percentStyle, GUILayout.Width(60));

                // Number needed to level up
                GUIStyle numNeededStyle = new GUIStyle(valueStyle)
                {
                    alignment = TextAnchor.MiddleRight
                };

                // Color code based on how many are needed
                if (numNeeded <= 3)
                    numNeededStyle.normal.textColor = new Color(0.0f, 0.7f, 0.0f); // Green for few needed
                else if (numNeeded >= 20)
                    numNeededStyle.normal.textColor = new Color(0.7f, 0.0f, 0.0f); // Red for many needed
                else if (numNeeded >= 10)
                    numNeededStyle.normal.textColor = new Color(0.9f, 0.6f, 0.0f); // Orange for moderate

                EditorGUILayout.LabelField(numNeeded.ToString(), numNeededStyle, GUILayout.Width(50));

                EditorGUILayout.EndHorizontal();
            }

            // Add a section for total rewards needed for the entire level range
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Total Needed", subHeaderStyle);
            EditorGUILayout.Space(2);

            // Table header for range totals
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));
            EditorGUILayout.LabelField("Total", rightAlignedHeaderStyle, GUILayout.Width(60));
            EditorGUILayout.EndHorizontal();

            // Draw a separator line
            Rect rangeTotalsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
            EditorGUI.DrawRect(rangeTotalsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

            foreach (var category in sortedCategories)
            {
                // Get the average level for this range
                int avgLevel = startLevel + (rangeSize / 2);

                // Calculate the scaled reward based on the level
                int rewardAmount = CalculateExperienceReward(category.Value, averageExp, avgLevel);

                // Calculate total needed for the entire range
                int totalNeeded = rewardAmount > 0 ? Mathf.CeilToInt(totalExpForRange / (float)rewardAmount) : 0;

                EditorGUILayout.BeginHorizontal();

                // Activity type
                EditorGUILayout.LabelField(category.Key, labelStyle, GUILayout.Width(100));

                // Total needed for range
                GUIStyle totalNeededStyle = new GUIStyle(valueStyle)
                {
                    alignment = TextAnchor.MiddleRight,
                    fontSize = 12,
                    fontStyle = FontStyle.Bold
                };

                // Color code based on total needed
                if (totalNeeded <= 10)
                    totalNeededStyle.normal.textColor = new Color(0.0f, 0.7f, 0.0f); // Green for few needed
                else if (totalNeeded >= 100)
                    totalNeededStyle.normal.textColor = new Color(0.7f, 0.0f, 0.0f); // Red for many needed
                else if (totalNeeded >= 50)
                    totalNeededStyle.normal.textColor = new Color(0.9f, 0.6f, 0.0f); // Orange for moderate

                EditorGUILayout.LabelField(totalNeeded.ToString(), totalNeededStyle, GUILayout.Width(60));

                EditorGUILayout.EndHorizontal();
            }

            // Close the background box
            EditorGUILayout.EndVertical();
            }
        }

        #endregion UI Helper Methods

        #region Calculation Methods
        /// <summary>
        /// Calculates the scaled reward multiplier based on the player's level and the algorithm's curve
        /// </summary>
        /// <param name="level">The player's current level</param>
        /// <param name="baseMultiplier">The base reward multiplier (percentage)</param>
        /// <returns>The scaled reward multiplier</returns>
        private float CalculateScaledRewardMultiplier(int level, float baseMultiplier)
        {
            // If no algorithm is selected or no experience requirements calculated, return the base multiplier
            if (selectedAlgorithm == null || experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return baseMultiplier;
            }

            // Get the level index in our experience requirements list
            int levelIndex = level - StartingLevel;
            if (levelIndex < 0 || levelIndex >= experienceRequirements.Count)
            {
                return baseMultiplier; // Out of range, return base multiplier
            }

            // Calculate the relative increase from the starting level to this level
            float startingXP = experienceRequirements[0];
            float currentLevelXP = experienceRequirements[levelIndex];

            // Avoid division by zero
            if (startingXP <= 0)
            {
                return baseMultiplier;
            }

            // Calculate the relative XP increase from starting level
            float relativeIncrease = currentLevelXP / startingXP;

            // Scale the reward inversely to the XP increase
            // As XP requirements grow, rewards become a smaller percentage
            return baseMultiplier / Mathf.Max(1.0f, Mathf.Sqrt(relativeIncrease));
        }

        /// <summary>
        /// Calculates the experience reward for a given category and level
        /// </summary>
        /// <param name="categoryPercentage">The base percentage of the level requirement</param>
        /// <param name="expRequired">The experience required for the level</param>
        /// <param name="level">The player's current level</param>
        /// <returns>The calculated experience reward (minimum 1)</returns>
        private int CalculateExperienceReward(float categoryPercentage, int expRequired, int level)
        {
            // Get the scaled multiplier based on the player's level
            float scaledMultiplier = CalculateScaledRewardMultiplier(level, categoryPercentage);

            // Calculate the raw reward
            float rawReward = expRequired * scaledMultiplier;

            // Ensure the reward is at least 1, even for very small percentages (like 0.001%)
            // This ensures that even tiny percentage values will always grant at least 1 XP
            // This is especially important for categories with very small percentages (below 0.01%)
            return Mathf.Max(1, Mathf.RoundToInt(rawReward));
        }

        // Helper method to create a texture with a solid color
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }

        // Set all level range foldout states to collapsed (false)
        private void CollapseAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = false;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        // Set all level range foldout states to expanded (true)
        private void ExpandAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = true;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        private void CalculateExperienceRequirements()
        {
            // Initialize the list
            experienceRequirements = new List<int>();

            // Set the starting experience
            int baseRequirement = StartingExperience;
            experienceRequirements.Add(baseRequirement);

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // If we have a selected algorithm, use it
            if (selectedAlgorithm != null)
            {
                // Calculate experience requirements for each level using the algorithm
                int currentRequirement = baseRequirement;

                for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                {
                    try
                    {
                        // Calculate the next requirement using the algorithm
                        int nextRequirement = selectedAlgorithm.CalculateNextRequirement(
                            currentRequirement,
                            level - 1, // Current level (before level up)
                            LevelUpMultiplier,
                            StartingLevel,
                            MaxLevel);

                        // Ensure we have a valid value (greater than previous)
                        if (nextRequirement <= currentRequirement)
                        {
                            // If the formula produced an invalid value, use a fallback
                            nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                        }

                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                    catch (Exception e)
                    {
                        // If there's an error, use a fallback formula
                        string algorithmName = selectedAlgorithm != null ? selectedAlgorithm.Name : "Unknown";
                        Debug.LogWarning($"Error calculating curve with algorithm {algorithmName}: {e.Message}. Using fallback.");
                        float fallbackMultiplier = LevelUpMultiplier > 0.01f ? LevelUpMultiplier : 1.05f;
                        int nextRequirement = Mathf.RoundToInt(currentRequirement * fallbackMultiplier);
                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                }
            }
            else
            {
                // If no algorithm is selected, try to find one in the database
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
                {
                    // Use the first algorithm in the database
                    selectedAlgorithm = algorithmDB.algorithms[0];

                    // Calculate experience requirements for each level using the algorithm
                    int currentRequirement = baseRequirement;

                    for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                    {
                        try
                        {
                            // Calculate the next requirement using the algorithm
                            int nextRequirement = selectedAlgorithm.CalculateNextRequirement(
                                currentRequirement,
                                level - 1, // Current level (before level up)
                                LevelUpMultiplier,
                                StartingLevel,
                                MaxLevel);

                            // Ensure we have a valid value (greater than previous)
                            if (nextRequirement <= currentRequirement)
                            {
                                // If the formula produced an invalid value, use a fallback
                                nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                            }

                            experienceRequirements.Add(nextRequirement);
                            currentRequirement = nextRequirement;
                        }
                        catch (Exception e)
                        {
                            // If there's an error, use a fallback formula
                            string algorithmName = selectedAlgorithm != null ? selectedAlgorithm.Name : "Unknown";
                            Debug.LogWarning($"Error calculating curve with algorithm {algorithmName}: {e.Message}. Using fallback.");
                            float fallbackMultiplier = LevelUpMultiplier > 0.01f ? LevelUpMultiplier : 1.05f;
                            int nextRequirement = Mathf.RoundToInt(currentRequirement * fallbackMultiplier);
                            experienceRequirements.Add(nextRequirement);
                            currentRequirement = nextRequirement;
                        }
                    }
                }
                else
                {
                    // If no algorithm database exists, use a simple fallback
                    Debug.LogWarning("No algorithm database found. Using simple linear progression. Create an algorithm database to fix this.");

                    // Calculate a simple linear progression
                    int currentRequirement = baseRequirement;
                    for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                    {
                        float multiplier = 1.1f * LevelUpMultiplier;
                        if (multiplier < 1.01f) multiplier = 1.01f; // Ensure at least 1% increase

                        int nextRequirement = Mathf.RoundToInt(currentRequirement * multiplier);
                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                }
            }

            // Ensure all values are at least increasing
            for (int i = 1; i < experienceRequirements.Count; i++)
            {
                if (experienceRequirements[i] <= experienceRequirements[i - 1])
                {
                    experienceRequirements[i] = experienceRequirements[i - 1] + 1;
                }
            }

            // Force repaint to update the UI
            window.Repaint();
        }
        #endregion Calculation Methods
    }
}
