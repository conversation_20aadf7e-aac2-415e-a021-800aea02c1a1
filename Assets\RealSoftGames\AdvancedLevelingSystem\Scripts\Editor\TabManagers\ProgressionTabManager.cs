using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Combined tab manager for experience rewards and progression simulation
    /// </summary>
    public class ProgressionTabManager : ITabManager
    {
        #region Fields and Properties
        // Reference to the main editor window
        private readonly AdvancedLevelingSystemEditorWindow window;

        // Data references
        private ExperienceRewardsData rewardsData;

        // Scroll positions for each panel
        private Vector2 leftScrollPosition;
        private Vector2 rightScrollPosition;

        // Algorithm selection
        private LevelingAlgorithmBase selectedAlgorithm;

        // Tab properties
        public string TabName => "Progression";

        // Use shared settings from the window
        private float LevelUpMultiplier => window.SharedLevelUpMultiplier;
        private int StartingExperience => window.SharedStartingExperience;
        private int StartingLevel => window.SharedStartingLevel;
        private int MaxLevel => window.SharedMaxLevel;
        private int levelRangeSize = 5;
        private bool showDetailedBreakdown = true;

        // Graph visualization
        private LevelingCurvePreview curvePreview;

        // Reward categories and their multipliers (stored as decimal values, displayed as percentages)
        private Dictionary<string, float> rewardCategories = new Dictionary<string, float>();

        // Cached experience requirements
        private List<int> experienceRequirements;

        // Simulation settings
        private enum PlayerType { Casual, Regular, Hardcore }
        private PlayerType selectedPlayerType = PlayerType.Regular;

        // Player activity settings
        private float playTimePerDay = 2.0f; // hours
        private float actionsPerMinute = 10.0f;

        // Simulation results
        private List<SimulationResult> simulationResults;

        // Styles
        private GUIStyle headerStyle;
        private GUIStyle subHeaderStyle;
        private GUIStyle labelStyle;
        private GUIStyle valueStyle;
        private GUIStyle categoryHeaderStyle;
        private GUIStyle levelRangeStyle;
        private GUIStyle tooltipStyle;
        private GUIStyle sectionBoxStyle;

        // Level range foldout states
        private Dictionary<int, bool> levelRangeFoldoutStates = new Dictionary<int, bool>();
        private GUIStyle foldoutStyle;
        #endregion

        // Simulation result class
        private class SimulationResult
        {
            public int Level { get; set; }
            public int ExperienceRequired { get; set; }
            public float HoursToLevel { get; set; }
            public float DaysToLevel { get; set; }
            public float TotalDays { get; set; }
            public float TotalHours { get; set; }
            public DateTime EstimatedDate { get; set; }
        }

        // Player type settings class
        private class PlayerTypeSettings
        {
            public float playTimePerDay;
            public float actionsPerMinute;

            public PlayerTypeSettings(float playTime, float actions)
            {
                playTimePerDay = playTime;
                actionsPerMinute = actions;
            }
        }

        // Dictionary of settings for each player type
        private Dictionary<PlayerType, PlayerTypeSettings> playerTypeSettings = new Dictionary<PlayerType, PlayerTypeSettings>();

        public ProgressionTabManager(AdvancedLevelingSystemEditorWindow window, ExperienceRewardsData rewardsData)
        {
            this.window = window;
            this.rewardsData = rewardsData;

            // Initialize curve preview
            curvePreview = new LevelingCurvePreview();

            // Initialize player type settings
            InitializePlayerTypeSettings();

            // Load data
            LoadData();
        }

        private void InitializePlayerTypeSettings()
        {
            // Clear existing settings
            playerTypeSettings.Clear();

            // Add default settings for each player type
            playerTypeSettings[PlayerType.Casual] = new PlayerTypeSettings(2.0f, 5.0f);
            playerTypeSettings[PlayerType.Regular] = new PlayerTypeSettings(4.0f, 10.0f);
            playerTypeSettings[PlayerType.Hardcore] = new PlayerTypeSettings(6.0f, 15.0f);

            // Set initial values based on selected player type
            UpdatePlayerTypeSettings();
        }

        private void UpdatePlayerTypeSettings()
        {
            if (playerTypeSettings.TryGetValue(selectedPlayerType, out PlayerTypeSettings settings))
            {
                playTimePerDay = settings.playTimePerDay;
                actionsPerMinute = settings.actionsPerMinute;
            }
        }

        public void OnEnable()
        {
            InitializeStyles();
            LoadData();
            CalculateExperienceRequirements();
            RunSimulation();
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (subHeaderStyle == null)
            {
                subHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (labelStyle == null)
            {
                labelStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleLeft
                };
            }

            if (valueStyle == null)
            {
                valueStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleRight,
                    fontStyle = FontStyle.Bold
                };
            }

            if (categoryHeaderStyle == null)
            {
                categoryHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(0, 0, 10, 5)
                };
            }

            if (levelRangeStyle == null)
            {
                levelRangeStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 13,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };
            }

            if (tooltipStyle == null)
            {
                tooltipStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 12,
                    wordWrap = true,
                    richText = true,
                    padding = new RectOffset(10, 10, 10, 10)
                };
            }

            if (sectionBoxStyle == null)
            {
                sectionBoxStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(10, 10, 10, 10),
                    margin = new RectOffset(0, 0, 5, 5)
                };
            }

            if (foldoutStyle == null)
            {
                foldoutStyle = new GUIStyle(EditorStyles.foldout)
                {
                    fontStyle = FontStyle.Bold,
                    fontSize = 12,
                    normal = { textColor = Color.white }
                };
            }
        }

        public void OnGUI()
        {
            InitializeStyles();

            // Set a minimum window width to prevent horizontal scrollbars
            float minWindowWidth = 1000f; // Minimum width for the entire window
            if (window.position.width < minWindowWidth)
            {
                EditorGUILayout.HelpBox($"Please resize the window to at least {minWindowWidth}px width for optimal viewing.", MessageType.Info);
            }

            // Two-panel layout
            EditorGUILayout.BeginHorizontal();

            // Calculate the available width (accounting for the left navigation panel)
            float availableWidth = EditorWindowTemplate.MainBodyRect.width;

            // Left panel (Algorithm & Settings + Reward Categories) - 60% of available width (increased from 55%)
            float leftPanelWidth = (availableWidth - 60) * 0.60f;
            EditorGUILayout.BeginVertical(GUILayout.Width(leftPanelWidth), GUILayout.ExpandHeight(true));

            // Begin scrollview for the entire left panel
            leftScrollPosition = EditorGUILayout.BeginScrollView(
                leftScrollPosition,
                false, // No horizontal scrollbar
                true,  // Vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));

            // Draw Algorithm & Settings section
            DrawAlgorithmSettings();

            // Add a separator between sections
            EditorGUILayout.Space(10);
            Rect horizontalSeparator = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(horizontalSeparator, new Color(0.5f, 0.5f, 0.5f, 0.5f));
            EditorGUILayout.Space(10);

            // Draw Reward Categories section
            DrawRewardCategories();

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();

            // Add a visual separator between panels
            GUILayout.Space(10);
            Rect verticalSeparator = GUILayoutUtility.GetRect(2, EditorWindowTemplate.MainBodyRect.height);
            EditorGUI.DrawRect(verticalSeparator, new Color(0.5f, 0.5f, 0.5f, 0.5f));
            GUILayout.Space(10);

            // Right panel (Level Progression & Simulation) - 40% of available width (decreased from 45%)
            float rightPanelWidth = (availableWidth - 30) * 0.40f;
            EditorGUILayout.BeginVertical(GUILayout.Width(rightPanelWidth), GUILayout.ExpandHeight(true));
            DrawRightPanel();
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }

        #region Data Management
        private void LoadData()
        {
            // Try to load the default rewards data if not provided
            if (rewardsData == null)
            {
                rewardsData = Resources.Load<ExperienceRewardsData>("DefaultExperienceRewardsData");

                // If still null, create a new instance with default values
                if (rewardsData == null)
                {
                    // Initialize with default reward categories
                    InitializeDefaultRewardCategories();
                    return;
                }
            }

            // Load the algorithm if available
            selectedAlgorithm = rewardsData.selectedAlgorithm;

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // Note: levelUpMultiplier, startingExperience, startingLevel, and maxLevel are now shared settings
            // and are accessed through properties that get values from the window
            levelRangeSize = rewardsData.levelRangeSize;
            showDetailedBreakdown = rewardsData.showDetailedBreakdown;

            // Update the shared settings in the window
            window.UpdateSharedSettings(
                levelUpMultiplier: rewardsData.levelUpMultiplier,
                startingExperience: rewardsData.startingExperience,
                startingLevel: rewardsData.startingLevel,
                maxLevel: rewardsData.maxLevel
            );

            // Load reward categories
            rewardCategories.Clear();
            if (rewardsData.rewardCategories != null && rewardsData.rewardCategories.Count > 0)
            {
                foreach (var category in rewardsData.rewardCategories)
                {
                    rewardCategories[category.name] = category.multiplier;
                }
            }
            else
            {
                // Initialize with default reward categories if none exist
                InitializeDefaultRewardCategories();
            }
        }

        private void InitializeDefaultRewardCategories()
        {
            // Clear existing categories
            rewardCategories.Clear();

            // Add default reward categories with their multipliers in the desired order
            // Quest categories
            rewardCategories.Add("Epic Quest", 0.12f);
            rewardCategories.Add("Hard Quest", 0.08f);
            rewardCategories.Add("Medium Quest", 0.05f);
            rewardCategories.Add("Easy Quest", 0.03f);
            rewardCategories.Add("Daily Activity", 0.02f);

            // Boss and special enemies
            rewardCategories.Add("World Boss", 0.10f);
            rewardCategories.Add("Boss Enemy", 0.05f);
            rewardCategories.Add("Elite Enemy", 0.015f);

            // Regular enemies
            rewardCategories.Add("Hard Enemy", 0.003f);
            rewardCategories.Add("Medium Enemy", 0.002f);
            rewardCategories.Add("Easy Enemy", 0.001f);
            rewardCategories.Add("Very Easy Enemy", 0.0005f);

            // Crafting and gathering
            rewardCategories.Add("Crafting Item", 0.0001f);
            rewardCategories.Add("Gathering Resource", 0.0001f);

            // If we have a rewards data object, update it
            if (rewardsData != null)
            {
                UpdateRewardCategoriesInScriptableObject();
            }
        }

        private void UpdateRewardCategoriesInScriptableObject()
        {
            if (rewardsData != null)
            {
                // Clear and update reward categories
                rewardsData.rewardCategories.Clear();
                foreach (var category in rewardCategories)
                {
                    rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory
                    {
                        name = category.Key,
                        multiplier = category.Value
                    });
                }

                // Mark the asset as dirty so Unity knows it needs to be saved
                EditorUtility.SetDirty(rewardsData);
                AssetDatabase.SaveAssets();
            }
        }

        private void SaveData()
        {
            if (rewardsData == null)
            {
                return;
            }

            // Update the algorithm
            rewardsData.selectedAlgorithm = selectedAlgorithm;

            rewardsData.levelUpMultiplier = LevelUpMultiplier;
            rewardsData.startingExperience = StartingExperience;
            rewardsData.startingLevel = StartingLevel;
            rewardsData.maxLevel = MaxLevel;
            rewardsData.levelRangeSize = levelRangeSize;
            rewardsData.showDetailedBreakdown = showDetailedBreakdown;

            // Update reward categories
            UpdateRewardCategoriesInScriptableObject();
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Creates a texture with a solid color
        /// </summary>
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }



        /// <summary>
        /// Displays a level range with its header and progression breakdown
        /// </summary>
        private void DisplayLevelRangeWithRewards(int startLevel, int rangeSize, int averageExp, GUIStyle headerTextStyle)
        {
            // Calculate the end level (clamped to max level)
            int endLevel = Mathf.Min(startLevel + rangeSize - 1, MaxLevel);

            // Get or initialize the foldout state for this level range
            if (!levelRangeFoldoutStates.ContainsKey(startLevel))
            {
                levelRangeFoldoutStates[startLevel] = true; // Default to expanded
            }

            // Create a header rect for the level range
            Rect levelRangeHeaderRect = EditorGUILayout.GetControlRect(false, 30);
            EditorGUI.DrawRect(levelRangeHeaderRect, new Color(0.15f, 0.15f, 0.3f));

            // Display the level range with foldout
            string levelRangeText = $"Level {startLevel} - {endLevel}";

            // Create a rect for the foldout arrow
            Rect foldoutRect = new Rect(levelRangeHeaderRect.x + 10, levelRangeHeaderRect.y, 20, levelRangeHeaderRect.height);

            // Create a rect for the level range text
            Rect labelRect = new Rect(levelRangeHeaderRect.x + 30, levelRangeHeaderRect.y, levelRangeHeaderRect.width - 40, levelRangeHeaderRect.height);

            // Handle the foldout click
            bool wasExpanded = levelRangeFoldoutStates[startLevel];
            levelRangeFoldoutStates[startLevel] = EditorGUI.Foldout(foldoutRect, wasExpanded, "", true, foldoutStyle);

            // Display the level range text
            EditorGUI.LabelField(labelRect, levelRangeText, headerTextStyle);

            // Add time estimation to the header if we have simulation results
            if (simulationResults != null && simulationResults.Count > 0)
            {
                // Find the simulation result for this level range
                SimulationResult startResult = simulationResults.Find(r => r.Level == startLevel);
                SimulationResult endResult = simulationResults.Find(r => r.Level == endLevel + 1) ?? simulationResults.LastOrDefault();

                if (startResult != null && endResult != null)
                {
                    // Calculate time difference
                    float totalDays = endResult.TotalDays - startResult.TotalDays;

                    // Create a rect for the time text (right-aligned)
                    Rect timeRect = new Rect(
                        levelRangeHeaderRect.x + levelRangeHeaderRect.width - 150,
                        levelRangeHeaderRect.y,
                        140,
                        levelRangeHeaderRect.height);

                    // Format time text
                    string timeText;
                    if (totalDays < 1)
                    {
                        float hours = totalDays * 24;
                        timeText = $"~{hours:F1} hours";
                    }
                    else if (totalDays < 30)
                    {
                        timeText = $"~{totalDays:F1} days";
                    }
                    else
                    {
                        float months = totalDays / 30f;
                        timeText = $"~{months:F1} months";
                    }

                    // Display the time text
                    GUIStyle timeStyle = new GUIStyle(headerTextStyle);
                    timeStyle.alignment = TextAnchor.MiddleRight;
                    EditorGUI.LabelField(timeRect, timeText, timeStyle);
                }
            }

            // Only show content if expanded
            if (levelRangeFoldoutStates[startLevel])
            {
                // Create a background for the content that expands to fill width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Display the average experience
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Average XP Required:", subHeaderStyle);
                EditorGUILayout.LabelField(averageExp.ToString("N0") + " XP", valueStyle);
                EditorGUILayout.EndHorizontal();

                // Display detailed breakdown
                EditorGUILayout.Space(5);

                // Graph removed as it was not working well

                // Draw time estimation for each level if simulation results are available
                if (simulationResults != null && simulationResults.Count > 0)
                {
                    EditorGUILayout.LabelField("Time Estimation", subHeaderStyle);

                    // Table header with consistent widths
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Level", subHeaderStyle, GUILayout.Width(50));

                    // Create right-aligned header styles
                    GUIStyle timeHeaderStyle = new GUIStyle(subHeaderStyle) { alignment = TextAnchor.MiddleRight };

                    EditorGUILayout.LabelField("Time to Level", timeHeaderStyle, GUILayout.Width(100));
                    EditorGUILayout.LabelField("Total Time", timeHeaderStyle, GUILayout.Width(100));
                    EditorGUILayout.EndHorizontal();

                    // Draw a separator line
                    Rect timeEstimationSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
                    EditorGUI.DrawRect(timeEstimationSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                    // Display time estimation for each level in the range
                    for (int level = startLevel; level <= endLevel; level++)
                    {
                        SimulationResult result = simulationResults.Find(r => r.Level == level);
                        if (result != null)
                        {
                            EditorGUILayout.BeginHorizontal();

                            // Level
                            EditorGUILayout.LabelField(level.ToString(), labelStyle, GUILayout.Width(50));

                            // Time to level
                            string timeToLevel;
                            if (level == StartingLevel)
                                timeToLevel = "-";
                            else if (result.DaysToLevel < 1)
                                timeToLevel = $"{result.HoursToLevel:F1} hours";
                            else
                                timeToLevel = $"{result.DaysToLevel:F1} days";

                            GUIStyle rightAlignedStyle = new GUIStyle(valueStyle) { alignment = TextAnchor.MiddleRight };
                            EditorGUILayout.LabelField(timeToLevel, rightAlignedStyle, GUILayout.Width(100));

                            // Total time
                            string totalTime;
                            if (level == StartingLevel)
                                totalTime = "-";
                            else if (result.TotalDays < 1)
                                totalTime = $"{result.TotalHours:F1} hours";
                            else if (result.TotalDays < 30)
                                totalTime = $"{result.TotalDays:F1} days";
                            else if (result.TotalDays < 365)
                                totalTime = $"{result.TotalDays / 30f:F1} months";
                            else
                                totalTime = $"{result.TotalDays / 365f:F1} years";

                            EditorGUILayout.LabelField(totalTime, rightAlignedStyle, GUILayout.Width(100));

                            EditorGUILayout.EndHorizontal();
                        }
                    }

                    EditorGUILayout.Space(5);
                }

                EditorGUILayout.LabelField("Level Progression Breakdown", subHeaderStyle);

                // Table header with consistent widths
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", subHeaderStyle, GUILayout.Width(50));

                // Create right-aligned header style for Required XP
                GUIStyle rightAlignedLevelHeaderStyle = new GUIStyle(subHeaderStyle);
                rightAlignedLevelHeaderStyle.alignment = TextAnchor.MiddleRight;

                EditorGUILayout.LabelField("Req XP", rightAlignedLevelHeaderStyle, GUILayout.Width(80));
                EditorGUILayout.LabelField("Inc %", rightAlignedLevelHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Display each level in the range
                int startIdx = Mathf.Max(0, startLevel - StartingLevel);
                int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + rangeSize - 1);

                for (int i = startIdx; i <= endIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        EditorGUILayout.BeginHorizontal();

                        // Level number
                        EditorGUILayout.LabelField((i + StartingLevel).ToString(), labelStyle, GUILayout.Width(50));

                        // Required XP - ensure it's right-aligned
                        // Create a dedicated style for the XP values with right alignment
                        GUIStyle xpValueStyle = new GUIStyle(valueStyle);
                        xpValueStyle.alignment = TextAnchor.MiddleRight;
                        EditorGUILayout.LabelField(experienceRequirements[i].ToString("N0"), xpValueStyle, GUILayout.Width(80));

                        // Percentage increase from previous level
                        if (i > 0)
                        {
                            float increase = ((float)experienceRequirements[i] / experienceRequirements[i - 1] - 1) * 100;
                            string increaseText = increase.ToString("0.0") + "%";

                            // Color code the increase percentage
                            GUIStyle increaseStyle = new GUIStyle(valueStyle);
                            increaseStyle.alignment = TextAnchor.MiddleRight; // Right-align the increase percentage
                            if (increase < 5)
                                increaseStyle.normal.textColor = new Color(0.0f, 0.8f, 0.0f); // Green for small increases
                            else if (increase > 15)
                                increaseStyle.normal.textColor = new Color(0.8f, 0.0f, 0.0f); // Red for large increases

                            EditorGUILayout.LabelField(increaseText, increaseStyle, GUILayout.Width(60));
                        }
                        else
                        {
                            EditorGUILayout.LabelField("-", GUILayout.Width(60));
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }

                // Add a section for rewards
                EditorGUILayout.Space(10);

                // Create a header rect for the rewards section
                Rect rewardsHeaderRect = EditorGUILayout.GetControlRect(false, 30);
                EditorGUI.DrawRect(rewardsHeaderRect, new Color(0.2f, 0.4f, 0.2f));

                // Display the rewards header with scaling indicator
                string rewardsHeaderText = "Rewards (Algorithm-Scaled)";
                EditorGUI.LabelField(rewardsHeaderRect, rewardsHeaderText, headerTextStyle);

                EditorGUILayout.Space(5);

                // Calculate total experience needed for this level range
                int totalExpForRange = 0;
                int rangeStartIdx = Mathf.Max(0, startLevel - StartingLevel);
                int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + rangeSize - 1);

                for (int i = rangeStartIdx; i <= rangeEndIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        totalExpForRange += experienceRequirements[i];
                    }
                }

                // Display total experience needed for this level range
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Total XP Needed:", subHeaderStyle);
                EditorGUILayout.LabelField(totalExpForRange.ToString("N0") + " XP", valueStyle);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                // Table header with fixed widths to ensure proper layout
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));

                // Create right-aligned header style for XP Reward and % of Level
                GUIStyle rightAlignedHeaderStyle = new GUIStyle(subHeaderStyle)
                {
                    alignment = TextAnchor.MiddleRight
                };

                EditorGUILayout.LabelField("XP", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.LabelField("% Level", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.LabelField("Need", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect rewardsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(rewardsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Sort categories by reward amount (descending)
                var sortedCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();

                foreach (var category in sortedCategories)
                {
                    // Get the average level for this range
                    int avgLevel = startLevel + (rangeSize / 2);

                    // Calculate the scaled reward based on the level
                    int rewardAmount = CalculateExperienceReward(category.Value, averageExp, avgLevel);

                    // Calculate the effective percentage (may be different from base percentage due to scaling)
                    float effectiveMultiplier = CalculateScaledRewardMultiplier(avgLevel, category.Value);
                    float percentOfLevel = effectiveMultiplier * 100;

                    // Calculate how many of this reward type are needed to level up
                    int numNeeded = rewardAmount > 0 ? Mathf.CeilToInt(averageExp / (float)rewardAmount) : 0;

                    EditorGUILayout.BeginHorizontal();

                    // Activity type
                    EditorGUILayout.LabelField(category.Key, labelStyle, GUILayout.Width(100));

                    // XP reward - ensure it's right-aligned
                    GUIStyle xpRewardStyle = new GUIStyle(valueStyle)
                    {
                        alignment = TextAnchor.MiddleRight
                    };
                    EditorGUILayout.LabelField(rewardAmount.ToString("N0"), xpRewardStyle, GUILayout.Width(60));

                    // Percentage of level
                    GUIStyle percentStyle = new GUIStyle(valueStyle)
                    {
                        alignment = TextAnchor.MiddleRight // Right-align the percentage
                    };
                    if (percentOfLevel < 10)
                        percentStyle.normal.textColor = new Color(0.0f, 0.7f, 0.0f); // Green for small rewards
                    else if (percentOfLevel > 50)
                        percentStyle.normal.textColor = new Color(0.7f, 0.0f, 0.0f); // Red for large rewards

                    // Format percentage with appropriate precision based on its value
                    string percentFormat;
                    if (percentOfLevel < 0.01f)
                        percentFormat = "0.000"; // Show 3 decimal places for very small values (below 0.01%)
                    else if (percentOfLevel < 0.1f)
                        percentFormat = "0.00"; // Show 2 decimal places for small values (below 0.1%)
                    else
                        percentFormat = "0.0"; // Show 1 decimal place for normal values

                    EditorGUILayout.LabelField(percentOfLevel.ToString(percentFormat) + "%", percentStyle, GUILayout.Width(60));

                    // Number needed to level up
                    GUIStyle numNeededStyle = new GUIStyle(valueStyle)
                    {
                        alignment = TextAnchor.MiddleRight
                    };
                    EditorGUILayout.LabelField(numNeeded.ToString(), numNeededStyle, GUILayout.Width(60));

                    EditorGUILayout.EndHorizontal();
                }

                // Add a section for total rewards needed for the entire level range
                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Total Needed", subHeaderStyle);
                EditorGUILayout.Space(2);

                // Table header for range totals
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));
                EditorGUILayout.LabelField("Total", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect rangeTotalsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(rangeTotalsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                foreach (var category in sortedCategories)
                {
                    // Get the average level for this range
                    int avgLevel = startLevel + (rangeSize / 2);

                    // Calculate the scaled reward based on the level
                    int rewardAmount = CalculateExperienceReward(category.Value, averageExp, avgLevel);

                    // Calculate total needed for the entire range
                    int totalNeeded = rewardAmount > 0 ? Mathf.CeilToInt(totalExpForRange / (float)rewardAmount) : 0;

                    EditorGUILayout.BeginHorizontal();

                    // Activity type
                    EditorGUILayout.LabelField(category.Key, labelStyle, GUILayout.Width(100));

                    // Total needed for range
                    GUIStyle totalNeededStyle = new GUIStyle(valueStyle)
                    {
                        alignment = TextAnchor.MiddleRight,
                        fontSize = 12,
                        fontStyle = FontStyle.Bold
                    };
                    EditorGUILayout.LabelField(totalNeeded.ToString(), totalNeededStyle, GUILayout.Width(60));

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }
        #endregion

        #region Calculation Methods
        /// <summary>
        /// Calculates the scaled reward multiplier based on the player's level and the algorithm's curve
        /// </summary>
        private float CalculateScaledRewardMultiplier(int level, float baseMultiplier)
        {
            // If no algorithm is selected or no experience requirements calculated, return the base multiplier
            if (selectedAlgorithm == null || experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return baseMultiplier;
            }

            // Get the level index in our experience requirements list
            int levelIndex = level - StartingLevel;
            if (levelIndex < 0 || levelIndex >= experienceRequirements.Count)
            {
                return baseMultiplier; // Out of range, return base multiplier
            }

            // Calculate the relative increase from the starting level to this level
            float startingXP = experienceRequirements[0];
            float currentLevelXP = experienceRequirements[levelIndex];

            // Avoid division by zero
            if (startingXP <= 0)
            {
                return baseMultiplier;
            }

            // Calculate the relative XP increase from starting level
            float relativeIncrease = currentLevelXP / startingXP;

            // Scale the reward inversely to the XP increase
            // As XP requirements grow, rewards become a smaller percentage
            return baseMultiplier / Mathf.Max(1.0f, Mathf.Sqrt(relativeIncrease));
        }

        /// <summary>
        /// Calculates the experience reward for a given category and level
        /// </summary>
        private int CalculateExperienceReward(float categoryPercentage, int expRequired, int level)
        {
            // Get the scaled multiplier based on the player's level
            float scaledMultiplier = CalculateScaledRewardMultiplier(level, categoryPercentage);

            // Calculate the raw reward
            float rawReward = expRequired * scaledMultiplier;

            // Ensure the reward is at least 1, even for very small percentages
            return Mathf.Max(1, Mathf.RoundToInt(rawReward));
        }

        private void CalculateExperienceRequirements()
        {
            // Initialize the list
            experienceRequirements = new List<int>();

            // Set the starting experience
            int baseRequirement = StartingExperience;
            experienceRequirements.Add(baseRequirement);

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // If we have a selected algorithm, use it
            if (selectedAlgorithm != null)
            {
                // Calculate experience requirements for each level using the algorithm
                int currentRequirement = baseRequirement;

                for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                {
                    try
                    {
                        // Calculate the next requirement using the algorithm
                        int nextRequirement = selectedAlgorithm.CalculateNextRequirement(
                            currentRequirement,
                            level - 1, // Current level (before level up)
                            LevelUpMultiplier,
                            StartingLevel,
                            MaxLevel);

                        // Ensure we have a valid value (greater than previous)
                        if (nextRequirement <= currentRequirement)
                        {
                            // If the formula produced an invalid value, use a fallback
                            nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                        }

                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                    catch (Exception e)
                    {
                        // If there's an error, use a fallback formula
                        string algorithmName = selectedAlgorithm != null ? selectedAlgorithm.Name : "Unknown";
                        Debug.LogWarning($"Error calculating curve with algorithm {algorithmName}: {e.Message}. Using fallback.");
                        float fallbackMultiplier = LevelUpMultiplier > 0.01f ? LevelUpMultiplier : 1.05f;
                        int nextRequirement = Mathf.RoundToInt(currentRequirement * fallbackMultiplier);
                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                }
            }
            else
            {
                // If no algorithm is selected, use a simple fallback
                Debug.LogWarning("No algorithm selected. Using simple linear progression.");

                // Calculate a simple linear progression
                int currentRequirement = baseRequirement;
                for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                {
                    float multiplier = 1.1f * LevelUpMultiplier;
                    if (multiplier < 1.01f) multiplier = 1.01f; // Ensure at least 1% increase

                    int nextRequirement = Mathf.RoundToInt(currentRequirement * multiplier);
                    experienceRequirements.Add(nextRequirement);
                    currentRequirement = nextRequirement;
                }
            }

            // Ensure all values are at least increasing
            for (int i = 1; i < experienceRequirements.Count; i++)
            {
                if (experienceRequirements[i] <= experienceRequirements[i - 1])
                {
                    experienceRequirements[i] = experienceRequirements[i - 1] + 1;
                }
            }

            // Run simulation with the updated experience requirements
            RunSimulation();
        }

        private void RunSimulation()
        {
            // Initialize the simulation results list
            simulationResults = new List<SimulationResult>();

            // If no experience requirements, calculate them first
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                CalculateExperienceRequirements();
            }

            // If still no experience requirements, return
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return;
            }

            // Run the simulation with current settings
            simulationResults = RunSimulationForPlayerType();
        }

        private List<SimulationResult> RunSimulationForPlayerType()
        {
            // Create a new list for the results
            List<SimulationResult> results = new List<SimulationResult>();

            // If no experience requirements, return empty list
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return results;
            }

            // Calculate actions per day
            float actionsPerDay = playTimePerDay * 60 * actionsPerMinute;

            // Calculate average XP per action based on reward categories
            float totalXpPerDay = 0;

            // Use the average of the first few levels for simulation
            int levelRangeToUse = Mathf.Min(5, experienceRequirements.Count);
            int totalXpForRange = 0;

            for (int i = 0; i < levelRangeToUse && i < experienceRequirements.Count; i++)
            {
                totalXpForRange += experienceRequirements[i];
            }

            int averageXpPerLevel = totalXpForRange / levelRangeToUse;

            // Calculate average XP per action based on reward categories
            foreach (var category in rewardCategories)
            {
                // Calculate the reward for this category at the starting level
                int rewardAmount = CalculateExperienceReward(category.Value, averageXpPerLevel, StartingLevel);

                // Assume equal distribution of actions across categories
                float actionsPerDayForCategory = actionsPerDay / rewardCategories.Count;

                // Calculate XP per day for this category
                totalXpPerDay += rewardAmount * actionsPerDayForCategory;
            }

            // If no XP per day, use a default value
            if (totalXpPerDay <= 0)
            {
                totalXpPerDay = 100; // Default value
            }

            // Calculate time to reach each level
            float totalDays = 0;
            float totalHours = 0;
            DateTime currentDate = DateTime.Now;

            // Add starting level
            results.Add(new SimulationResult
            {
                Level = StartingLevel,
                ExperienceRequired = StartingExperience,
                HoursToLevel = 0,
                DaysToLevel = 0,
                TotalDays = 0,
                TotalHours = 0,
                EstimatedDate = currentDate
            });

            // Calculate for each level
            for (int i = 0; i < experienceRequirements.Count && (StartingLevel + i + 1) <= MaxLevel; i++)
            {
                int level = StartingLevel + i + 1;
                int xpRequired = experienceRequirements[i];

                // Calculate time to level
                float daysToLevel = xpRequired / totalXpPerDay;
                float hoursToLevel = daysToLevel * 24;

                // Update totals
                totalDays += daysToLevel;
                totalHours += hoursToLevel;

                // Calculate estimated date
                DateTime estimatedDate = currentDate.AddDays(daysToLevel);
                currentDate = estimatedDate;

                // Add to results
                results.Add(new SimulationResult
                {
                    Level = level,
                    ExperienceRequired = xpRequired,
                    HoursToLevel = hoursToLevel,
                    DaysToLevel = daysToLevel,
                    TotalDays = totalDays,
                    TotalHours = totalHours,
                    EstimatedDate = estimatedDate
                });
            }

            return results;
        }
        #endregion

        #region UI Drawing Methods
        private void DrawAlgorithmSettings()
        {
            // Algorithm & Settings Section
            EditorGUILayout.LabelField("Algorithm & Settings", headerStyle);
            EditorGUILayout.Space(5);

            // Algorithm selection
            EditorGUI.BeginChangeCheck();

            // Load all available algorithms
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Create a list of algorithm names for the popup
                List<string> algorithmNames = new List<string>();
                List<LevelingAlgorithmBase> algorithms = new List<LevelingAlgorithmBase>();

                // Add all algorithms from the database
                foreach (var algorithm in algorithmDB.algorithms)
                {
                    algorithmNames.Add(algorithm.Name);
                    algorithms.Add(algorithm);
                }

                // Find the index of the currently selected algorithm
                int selectedIndex = 0;
                if (selectedAlgorithm != null)
                {
                    for (int i = 0; i < algorithms.Count; i++)
                    {
                        if (algorithms[i] != null && algorithms[i].uniqueID == selectedAlgorithm.uniqueID)
                        {
                            selectedIndex = i;
                            break;
                        }
                    }
                }

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    selectedIndex,
                    "Leveling Algorithm",
                    algorithmNames.ToArray(),
                    (newIndex) => {
                        selectedAlgorithm = algorithms[newIndex];
                        CalculateExperienceRequirements();
                        RunSimulation();
                    }
                );
            }
            else
            {
                // Show a warning if no algorithms are available
                EditorGUILayout.HelpBox("No leveling algorithms found. Please create at least one algorithm in the Algorithm Database.", MessageType.Warning);

                // Add a button to create a default algorithm
                if (GUILayout.Button("Create Default Algorithm Database", GUILayout.Height(30)))
                {
                    // Create a default algorithm database
                    AlgorithmDatabaseManager.CreateDefaultAlgorithmDatabase();

                    // Refresh the asset database
                    AssetDatabase.Refresh();
                }
            }

            // Draw the algorithm description if available
            if (selectedAlgorithm != null)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField(selectedAlgorithm.Name, subHeaderStyle);
                EditorGUILayout.LabelField(selectedAlgorithm.Description, EditorStyles.wordWrappedLabel);
                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.Space(10);

            // Level Up Multiplier (allow 0 to turn off the multiplier in equations)
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", LevelUpMultiplier, 0f, 5.0f);
            if (newLevelUpMultiplier != LevelUpMultiplier)
            {
                window.UpdateSharedSettings(levelUpMultiplier: newLevelUpMultiplier);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Show a tooltip about the multiplier
            EditorGUILayout.HelpBox("The Level Up Multiplier affects how quickly experience requirements increase between levels. Higher values create steeper curves.", MessageType.Info);

            // Starting Experience
            int newStartingExperience = EditorGUILayout.IntField("Starting Experience", StartingExperience);
            if (newStartingExperience < 1) newStartingExperience = 1;
            if (newStartingExperience != StartingExperience)
            {
                window.UpdateSharedSettings(startingExperience: newStartingExperience);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Starting Level
            int newStartingLevel = EditorGUILayout.IntField("Starting Level", StartingLevel);
            if (newStartingLevel < 1) newStartingLevel = 1;
            if (newStartingLevel != StartingLevel)
            {
                window.UpdateSharedSettings(startingLevel: newStartingLevel);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Max Level
            int newMaxLevel = EditorGUILayout.IntField("Max Level", MaxLevel);
            if (newMaxLevel < StartingLevel + 5) newMaxLevel = StartingLevel + 5;
            if (newMaxLevel != MaxLevel)
            {
                window.UpdateSharedSettings(maxLevel: newMaxLevel);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Level range size
            int newLevelRangeSize = EditorGUILayout.IntSlider("Level Range Size", levelRangeSize, 1, 10);
            if (newLevelRangeSize != levelRangeSize)
            {
                levelRangeSize = newLevelRangeSize;
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Show detailed breakdown
            bool newShowDetailedBreakdown = EditorGUILayout.Toggle("Show Detailed Breakdown", showDetailedBreakdown);
            if (newShowDetailedBreakdown != showDetailedBreakdown)
            {
                showDetailedBreakdown = newShowDetailedBreakdown;
                // No need to recalculate, just update the UI
                window.Repaint();
            }

            EditorGUILayout.Space(10);

            // Draw the leveling curve graph
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Draw the graph with a fixed height and expanded width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Add a title for the graph
                EditorGUILayout.LabelField("Leveling Curve Preview", subHeaderStyle);
                EditorGUILayout.Space(5);

                // Use the algorithm if available, otherwise fall back to the legacy difficulty
                if (selectedAlgorithm != null)
                {
                    // Calculate the available width based on the left panel width to ensure the graph fits properly
                    // Use the leftPanelWidth directly (which is already calculated as 60% of available width minus padding)
                    float availableWidth = leftPanelWidth - 20; // Subtract 20 pixels for padding
                    curvePreview.DrawCurvePreview(selectedAlgorithm, LevelUpMultiplier, StartingExperience, StartingLevel, MaxLevel, availableWidth);
                }
                else
                {
                    // If no algorithm is selected, show a message
                    EditorGUILayout.HelpBox("Please select a leveling algorithm to preview the curve.", MessageType.Info);
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.Space(10);

            // Player Type Settings
            EditorGUILayout.LabelField("Player Type Settings", headerStyle);
            EditorGUILayout.Space(5);

            // Player type selection
            EditorGUILayout.BeginHorizontal();

            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                // Create a style for the button
                GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);

                // Highlight the selected player type
                if (type == selectedPlayerType)
                {
                    buttonStyle.normal.background = EditorGUIUtility.whiteTexture;
                    buttonStyle.normal.textColor = Color.black;
                    buttonStyle.fontStyle = FontStyle.Bold;
                }

                // Add a button to select this player type
                if (GUILayout.Button(type.ToString(), buttonStyle, GUILayout.Height(25)))
                {
                    // Save current settings to the current player type
                    if (playerTypeSettings.ContainsKey(selectedPlayerType))
                    {
                        playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                            playTimePerDay, actionsPerMinute);
                    }

                    // Switch to new player type
                    selectedPlayerType = type;

                    // Load settings for the new player type
                    UpdatePlayerTypeSettings();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Player type settings
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Play time per day
            float newPlayTimePerDay = EditorGUILayout.Slider("Play Time (hours/day)", playTimePerDay, 0.5f, 12f);
            if (newPlayTimePerDay != playTimePerDay)
            {
                playTimePerDay = newPlayTimePerDay;

                // Update the player type settings
                if (playerTypeSettings.ContainsKey(selectedPlayerType))
                {
                    playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                        playTimePerDay, actionsPerMinute);
                }

                // Run simulation with new settings
                RunSimulation();
            }

            // Actions per minute
            float newActionsPerMinute = EditorGUILayout.Slider("Actions per Minute", actionsPerMinute, 1f, 30f);
            if (newActionsPerMinute != actionsPerMinute)
            {
                actionsPerMinute = newActionsPerMinute;

                // Update the player type settings
                if (playerTypeSettings.ContainsKey(selectedPlayerType))
                {
                    playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                        playTimePerDay, actionsPerMinute);
                }

                // Run simulation with new settings
                RunSimulation();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Save button
            if (GUILayout.Button("Save Settings", GUILayout.Height(30)))
            {
                SaveData();
                Debug.Log("Settings saved successfully.");
            }
        }

        private void DrawRewardCategories()
        {
            // Reward Categories Section
            EditorGUILayout.LabelField("Reward Categories", headerStyle);
            EditorGUILayout.Space(5);

            // Reward Scaling Information
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Reward Scaling", categoryHeaderStyle);
            EditorGUILayout.HelpBox("Rewards automatically scale based on the selected algorithm and level up multiplier. As XP requirements increase with level, rewards become a smaller relative percentage.", MessageType.Info);

            if (selectedAlgorithm != null)
            {
                EditorGUILayout.LabelField($"Using algorithm: {selectedAlgorithm.Name}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Level Up Multiplier: {LevelUpMultiplier:F2}x", EditorStyles.boldLabel);
            }
            else
            {
                EditorGUILayout.HelpBox("No algorithm selected. Please select an algorithm to see reward calculations.", MessageType.Warning);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Display each reward category with a slider
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Reward Categories", categoryHeaderStyle);

            // Sort categories by multiplier (descending)
            var sortedCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();

            foreach (var category in sortedCategories)
            {
                EditorGUILayout.BeginHorizontal();

                // Allow renaming the category
                string newCategory = EditorGUILayout.TextField(category.Key, GUILayout.Width(120));

                // If the category name changed, update the dictionary
                if (newCategory != category.Key && !rewardCategories.ContainsKey(newCategory))
                {
                    float value = rewardCategories[category.Key];
                    rewardCategories.Remove(category.Key);
                    rewardCategories.Add(newCategory, value);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }
                else
                {
                    // Update the multiplier value with finer control (0.001 to 1.0)
                    float multiplier = rewardCategories[category.Key];
                    float percentValue = multiplier * 100f; // Convert to percentage for display

                    EditorGUI.BeginChangeCheck();
                    // Create a slider for the full range (0.001% - 100%)
                    float newPercentValue = EditorGUILayout.Slider(percentValue, 0.001f, 100f, GUILayout.ExpandWidth(true));
                    if (EditorGUI.EndChangeCheck())
                    {
                        percentValue = newPercentValue;
                        multiplier = percentValue / 100f;
                        rewardCategories[category.Key] = multiplier;

                        // Update the ScriptableObject
                        UpdateRewardCategoriesInScriptableObject();

                        // Run simulation with new settings
                        RunSimulation();
                    }
                }

                // Display the % symbol
                GUILayout.Label("%", GUILayout.Width(15));

                // Delete button
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    rewardCategories.Remove(category.Key);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add new category button
            EditorGUILayout.Space(5);
            if (GUILayout.Button("Add New Category", GUILayout.Height(25)))
            {
                string newCategoryName = "New Category";
                int suffix = 0;

                // Ensure unique name
                while (rewardCategories.ContainsKey(newCategoryName + (suffix == 0 ? "" : suffix.ToString())))
                {
                    suffix++;
                }

                rewardCategories.Add(newCategoryName + (suffix == 0 ? "" : suffix.ToString()), 0.25f);

                // Update the ScriptableObject
                UpdateRewardCategoriesInScriptableObject();

                // Run simulation with new settings
                RunSimulation();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Preview of rewards at different levels
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Reward Preview", categoryHeaderStyle);

                // Calculate average experience for a few key levels
                int[] previewLevels = { StartingLevel, StartingLevel + 5, StartingLevel + 10, StartingLevel + 25, MaxLevel };

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Category", GUILayout.Width(120));

                foreach (int level in previewLevels)
                {
                    if (level <= MaxLevel)
                    {
                        EditorGUILayout.LabelField("Level " + level, GUILayout.Width(70));
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Display each category with rewards at different levels
                foreach (var category in sortedCategories)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Category name
                    EditorGUILayout.LabelField(category.Key, GUILayout.Width(120));

                    // Reward at each preview level
                    foreach (int level in previewLevels)
                    {
                        if (level <= MaxLevel)
                        {
                            // Get the level index
                            int levelIndex = level - StartingLevel;

                            // Ensure valid index
                            if (levelIndex >= 0 && levelIndex < experienceRequirements.Count)
                            {
                                // Calculate the reward at this level
                                int expRequired = experienceRequirements[levelIndex];
                                int reward = CalculateExperienceReward(category.Value, expRequired, level);

                                // Display the reward
                                EditorGUILayout.LabelField(reward.ToString(), GUILayout.Width(70));
                            }
                            else
                            {
                                EditorGUILayout.LabelField("-", GUILayout.Width(70));
                            }
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }

        /// <summary>
        /// Collapses all level range foldouts
        /// </summary>
        private void CollapseAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = false;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        /// <summary>
        /// Expands all level range foldouts
        /// </summary>
        private void ExpandAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = true;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        private void DrawRightPanel()
        {
            // Create styles for the right panel
            GUIStyle darkBackgroundStyle = new GUIStyle();
            darkBackgroundStyle.normal.background = CreateColorTexture(new Color(0.2f, 0.2f, 0.2f));

            // Create a style for section headers in the right panel
            GUIStyle rightPanelHeaderStyle = new GUIStyle(EditorStyles.boldLabel);
            rightPanelHeaderStyle.alignment = TextAnchor.MiddleCenter;
            rightPanelHeaderStyle.fontSize = 12;
            rightPanelHeaderStyle.normal.textColor = Color.white;
            rightPanelHeaderStyle.margin = new RectOffset(0, 0, 6, 6);
            rightPanelHeaderStyle.padding = new RectOffset(4, 4, 4, 4);

            // Create a style for the dark header background
            GUIStyle darkHeaderBackgroundStyle = new GUIStyle();
            darkHeaderBackgroundStyle.normal.background = CreateColorTexture(new Color(0.15f, 0.15f, 0.3f));

            // Create a style for the green section header
            GUIStyle greenHeaderStyle = new GUIStyle(rightPanelHeaderStyle);
            greenHeaderStyle.normal.background = CreateColorTexture(new Color(0.2f, 0.4f, 0.2f));

            // Begin scrollview for the entire right panel with dark background
            EditorGUILayout.BeginVertical(darkBackgroundStyle, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
            rightScrollPosition = EditorGUILayout.BeginScrollView(
                rightScrollPosition,
                false, // No horizontal scrollbar
                true,  // Vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));

            // Add a heading for the level progression section
            Rect levelProgressionHeaderRect = EditorGUILayout.GetControlRect(false, 30);
            EditorGUI.DrawRect(levelProgressionHeaderRect, new Color(0.3f, 0.3f, 0.4f));

            // Create a style for the level progression header
            GUIStyle levelProgressionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 13,
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = Color.white },
                stretchWidth = true
            };

            // Display the level progression heading
            EditorGUI.LabelField(levelProgressionHeaderRect, "Level Progression", levelProgressionHeaderStyle);

            EditorGUILayout.Space(10);

            // Add Collapse All / Expand All buttons
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Center buttons

            // Create a compact button style with blue background and white text
            GUIStyle compactButtonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 10,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(3, 3, 2, 2),
                normal = { textColor = Color.white, background = CreateColorTexture(new Color(0.2f, 0.4f, 0.8f)) }
            };

            // Collapse All button
            if (GUILayout.Button("Collapse All", compactButtonStyle, GUILayout.Width(80), GUILayout.Height(20)))
            {
                CollapseAllLevelRanges();
            }

            GUILayout.Space(10);

            // Expand All button
            if (GUILayout.Button("Expand All", compactButtonStyle, GUILayout.Width(80), GUILayout.Height(20)))
            {
                ExpandAllLevelRanges();
            }

            GUILayout.FlexibleSpace(); // Center buttons
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Add a separator line
            Rect mainSeparatorRect = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(mainSeparatorRect, new Color(0.3f, 0.3f, 0.3f, 0.8f));

            EditorGUILayout.Space(10);

            // Check if we have experience requirements calculated
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Define level ranges for display
                int rangeSize = 5; // 5 levels per range

                // Calculate how many ranges we need to display all levels up to MaxLevel
                int totalRangesNeeded = Mathf.CeilToInt((float)(MaxLevel - StartingLevel + 1) / rangeSize);

                // Calculate average experience for the first level range
                int totalExp = 0;
                int count = 0;
                int startIdx = Mathf.Max(0, StartingLevel - 1);
                int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + rangeSize - 1);

                for (int i = startIdx; i <= endIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        totalExp += experienceRequirements[i];
                        count++;
                    }
                }

                int averageExp = (count > 0) ? totalExp / count : 0;

                // Display first level range
                DisplayLevelRangeWithRewards(StartingLevel, rangeSize, averageExp, rightPanelHeaderStyle);

                // Display additional level ranges
                for (int rangeIndex = 1; rangeIndex < totalRangesNeeded; rangeIndex++)
                {
                    int rangeStartLevel = StartingLevel + (rangeIndex * rangeSize);

                    // Calculate average experience for this level range
                    int rangeTotalExp = 0;
                    int rangeCount = 0;
                    int rangeStartIdx = Mathf.Max(0, rangeStartLevel - 1);
                    int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + rangeSize - 1);

                    for (int i = rangeStartIdx; i <= rangeEndIdx; i++)
                    {
                        if (i < experienceRequirements.Count)
                        {
                            rangeTotalExp += experienceRequirements[i];
                            rangeCount++;
                        }
                    }

                    int rangeAverageExp = (rangeCount > 0) ? rangeTotalExp / rangeCount : 0;

                    // Add some space between ranges
                    EditorGUILayout.Space(15);

                    // Display this level range
                    DisplayLevelRangeWithRewards(rangeStartLevel, rangeSize, rangeAverageExp, rightPanelHeaderStyle);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No experience requirements calculated. Please check your settings.", MessageType.Warning);
            }

            // No additional content needed here as all the information is now displayed in the level range sections

            EditorGUILayout.Space(10);

            // Display simulation results
            if (simulationResults != null && simulationResults.Count > 0)
            {
                // Total needed section
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Total Needed", EditorStyles.boldLabel);

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", GUILayout.Width(50));
                EditorGUILayout.LabelField("XP Required", GUILayout.Width(80));
                EditorGUILayout.LabelField("Time to Level", GUILayout.Width(100));
                EditorGUILayout.LabelField("Total Time", GUILayout.Width(100));
                EditorGUILayout.LabelField("Est. Date", GUILayout.Width(100));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Create a list of levels to display (key milestones)
                List<SimulationResult> milestonesToShow = new List<SimulationResult>();

                // Always add starting level
                milestonesToShow.Add(simulationResults[0]);

                // Determine milestone frequency based on max level
                int milestoneFrequency = 5;
                if (MaxLevel > 100)
                    milestoneFrequency = 20;
                else if (MaxLevel > 50)
                    milestoneFrequency = 10;

                // Add milestone levels and max level
                for (int i = 1; i < simulationResults.Count; i++)
                {
                    var result = simulationResults[i];

                    // Skip levels beyond maxLevel
                    if (result.Level > MaxLevel)
                        break;

                    // Only add milestones and the max level
                    if (result.Level % milestoneFrequency == 0 || result.Level == MaxLevel)
                    {
                        milestonesToShow.Add(result);
                    }
                }

                // Display each milestone
                foreach (var result in milestonesToShow)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Level
                    EditorGUILayout.LabelField(result.Level.ToString(), GUILayout.Width(50));

                    // XP Required
                    EditorGUILayout.LabelField(result.ExperienceRequired.ToString("N0"), GUILayout.Width(80));

                    // Time to Level
                    string timeToLevel;
                    if (result.Level == StartingLevel)
                        timeToLevel = "-";
                    else if (result.DaysToLevel < 1)
                        timeToLevel = $"{result.HoursToLevel:F1} hours";
                    else
                        timeToLevel = $"{result.DaysToLevel:F1} days";

                    EditorGUILayout.LabelField(timeToLevel, GUILayout.Width(100));

                    // Total Time
                    string totalTime;
                    if (result.Level == StartingLevel)
                        totalTime = "-";
                    else if (result.TotalDays < 1)
                        totalTime = $"{result.TotalHours:F1} hours";
                    else
                        totalTime = $"{result.TotalDays:F1} days";

                    EditorGUILayout.LabelField(totalTime, GUILayout.Width(100));

                    // Estimated Date
                    string estimatedDate = result.Level == StartingLevel ? "Now" : result.EstimatedDate.ToString("MMM d, yyyy");
                    EditorGUILayout.LabelField(estimatedDate, GUILayout.Width(100));

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();

                EditorGUILayout.Space(10);

                // Display player activity summary
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Player Activity Summary", categoryHeaderStyle);

                // Display player type and settings
                EditorGUILayout.LabelField($"Player Type: {selectedPlayerType}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Play Time: {playTimePerDay:F1} hours/day", EditorStyles.label);
                EditorGUILayout.LabelField($"Actions: {actionsPerMinute:F1} per minute", EditorStyles.label);

                // Calculate total actions per day
                float actionsPerDay = playTimePerDay * 60 * actionsPerMinute;
                EditorGUILayout.LabelField($"Total Actions: {actionsPerDay:F0} per day", EditorStyles.boldLabel);

                // Add Player Type Comparison Timeline
                EditorGUILayout.Space(10);
                EditorGUILayout.LabelField("Player Type Comparison", categoryHeaderStyle);

                // Calculate time to max level for each player type
                Dictionary<PlayerType, float> playerTypeDays = new Dictionary<PlayerType, float>();

                // Store current settings
                PlayerType currentType = selectedPlayerType;
                float currentPlayTime = playTimePerDay;
                float currentActions = actionsPerMinute;

                // Calculate for each player type
                foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                {
                    // Get settings for this player type
                    if (playerTypeSettings.TryGetValue(type, out PlayerTypeSettings settings))
                    {
                        // Temporarily switch to this player type's settings
                        playTimePerDay = settings.playTimePerDay;
                        actionsPerMinute = settings.actionsPerMinute;

                        // Run a simulation for this player type
                        List<SimulationResult> typeResults = RunSimulationForPlayerType();

                        // Get the max level result
                        if (typeResults.Count > 0 && typeResults[typeResults.Count - 1].Level == MaxLevel)
                        {
                            playerTypeDays[type] = typeResults[typeResults.Count - 1].TotalDays;
                        }
                        else
                        {
                            playerTypeDays[type] = 0;
                        }
                    }
                }

                // Restore original settings
                selectedPlayerType = currentType;
                playTimePerDay = currentPlayTime;
                actionsPerMinute = currentActions;

                // Draw the timeline bars
                float maxDays = 0;
                foreach (var days in playerTypeDays.Values)
                {
                    if (days > maxDays) maxDays = days;
                }

                if (maxDays > 0)
                {
                    // Draw each player type bar
                    foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                    {
                        if (playerTypeDays.TryGetValue(type, out float days))
                        {
                            // Skip if no data
                            if (days <= 0) continue;

                            EditorGUILayout.BeginHorizontal();

                            // Player type label (fixed width)
                            EditorGUILayout.LabelField(type.ToString(), GUILayout.Width(70));

                            // Bar background
                            Rect barRect = EditorGUILayout.GetControlRect(false, 20, GUILayout.ExpandWidth(true));

                            // Calculate bar width as percentage of max days
                            float barWidth = (days / maxDays) * barRect.width;

                            // Draw the bar with color based on player type
                            Color barColor;
                            switch (type)
                            {
                                case PlayerType.Casual:
                                    barColor = new Color(0.2f, 0.6f, 1.0f); // Blue
                                    break;
                                case PlayerType.Regular:
                                    barColor = new Color(0.2f, 0.8f, 0.2f); // Green
                                    break;
                                case PlayerType.Hardcore:
                                    barColor = new Color(1.0f, 0.6f, 0.2f); // Orange
                                    break;
                                default:
                                    barColor = Color.gray;
                                    break;
                            }

                            // Draw the bar
                            Rect filledBarRect = new Rect(barRect.x, barRect.y, barWidth, barRect.height);
                            EditorGUI.DrawRect(filledBarRect, barColor);

                            // Draw the time text on the bar
                            string timeText;
                            if (days < 1)
                                timeText = $"{days * 24:F1} hours";
                            else if (days < 30)
                                timeText = $"{days:F1} days";
                            else if (days < 365)
                                timeText = $"{days / 30:F1} months";
                            else
                                timeText = $"{days / 365:F1} years";

                            // Create a style for the text
                            GUIStyle barTextStyle = new GUIStyle(EditorStyles.boldLabel);
                            barTextStyle.normal.textColor = Color.white;
                            barTextStyle.alignment = TextAnchor.MiddleRight;

                            // Draw the text
                            Rect textRect = new Rect(filledBarRect.x, filledBarRect.y, filledBarRect.width - 5, filledBarRect.height);
                            EditorGUI.LabelField(textRect, timeText, barTextStyle);

                            EditorGUILayout.EndHorizontal();
                        }
                    }

                    // Add a legend
                    EditorGUILayout.Space(5);
                    EditorGUILayout.BeginHorizontal();
                    GUILayout.FlexibleSpace(); // Center the legend

                    foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                    {
                        // Skip if no data
                        if (!playerTypeDays.ContainsKey(type) || playerTypeDays[type] <= 0) continue;

                        // Get color for this player type
                        Color legendColor;
                        switch (type)
                        {
                            case PlayerType.Casual:
                                legendColor = new Color(0.2f, 0.6f, 1.0f); // Blue
                                break;
                            case PlayerType.Regular:
                                legendColor = new Color(0.2f, 0.8f, 0.2f); // Green
                                break;
                            case PlayerType.Hardcore:
                                legendColor = new Color(1.0f, 0.6f, 0.2f); // Orange
                                break;
                            default:
                                legendColor = Color.gray;
                                break;
                        }

                        // Draw color box
                        Rect colorRect = EditorGUILayout.GetControlRect(false, 16, GUILayout.Width(16));
                        EditorGUI.DrawRect(colorRect, legendColor);

                        // Draw type name
                        EditorGUILayout.LabelField(type.ToString(), GUILayout.Width(70));

                        GUILayout.Space(10);
                    }

                    GUILayout.FlexibleSpace(); // Center the legend
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.Space(5);

                // Display time to reach max level
                if (simulationResults.Count > 0 && simulationResults[simulationResults.Count - 1].Level == MaxLevel)
                {
                    var maxLevelResult = simulationResults[simulationResults.Count - 1];

                    EditorGUILayout.LabelField($"Time to Max Level ({MaxLevel}):", EditorStyles.boldLabel);

                    if (maxLevelResult.TotalDays < 1)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalHours:F1} hours", EditorStyles.boldLabel);
                    else if (maxLevelResult.TotalDays < 30)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays:F1} days", EditorStyles.boldLabel);
                    else if (maxLevelResult.TotalDays < 365)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays / 30:F1} months", EditorStyles.boldLabel);
                    else
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays / 365:F1} years", EditorStyles.boldLabel);

                    EditorGUILayout.LabelField($"Estimated Completion: {maxLevelResult.EstimatedDate.ToString("MMMM d, yyyy")}", EditorStyles.boldLabel);
                }

                EditorGUILayout.EndVertical();
            }
            else
            {
                EditorGUILayout.HelpBox("No simulation results available. Please check your settings and run the simulation.", MessageType.Warning);
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical(); // End the dark background vertical
        }
        #endregion
    }
}
