using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class SettingsTabManager : ITabManager
    {
        private AdvancedLevelingSystemEditorWindow window;
        private GUIStyle headerStyle;
        private GUIStyle sectionHeaderStyle;
        private Vector2 scrollPosition;

        // Tab properties
        public string TabName => "Settings";

        // Settings
        private bool autoSaveChanges = true;
        private bool showDebugInfo = false;
        private bool enableTooltips = true;

        public SettingsTabManager(AdvancedLevelingSystemEditorWindow window)
        {
            this.window = window;
        }

        public void OnEnable()
        {
            LoadSettings();
        }

        public void OnDisable()
        {
            SaveSettings();
        }

        public void OnDestroy()
        {
            SaveSettings();
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (sectionHeaderStyle == null)
            {
                sectionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 10, 5)
                };
            }
        }

        private void LoadSettings()
        {
            autoSaveChanges = EditorPrefs.GetBool("RealSoftGames.AdvancedLevelingSystem.AutoSaveChanges", true);
            showDebugInfo = EditorPrefs.GetBool("RealSoftGames.AdvancedLevelingSystem.ShowDebugInfo", false);
            enableTooltips = EditorPrefs.GetBool("RealSoftGames.AdvancedLevelingSystem.EnableTooltips", true);
        }

        private void SaveSettings()
        {
            EditorPrefs.SetBool("RealSoftGames.AdvancedLevelingSystem.AutoSaveChanges", autoSaveChanges);
            EditorPrefs.SetBool("RealSoftGames.AdvancedLevelingSystem.ShowDebugInfo", showDebugInfo);
            EditorPrefs.SetBool("RealSoftGames.AdvancedLevelingSystem.EnableTooltips", enableTooltips);
        }

        public void OnGUI()
        {
            InitializeStyles();

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.LabelField("Advanced Leveling System Settings", headerStyle);
            EditorGUILayout.Space(10);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Editor Settings
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Editor Settings", sectionHeaderStyle);
            EditorGUILayout.Space(5);

            EditorGUI.BeginChangeCheck();

            autoSaveChanges = EditorGUILayout.Toggle(new GUIContent("Auto Save Changes", "Automatically save changes when values are modified"), autoSaveChanges);
            showDebugInfo = EditorGUILayout.Toggle(new GUIContent("Show Debug Info", "Display additional debug information in the editor"), showDebugInfo);
            enableTooltips = EditorGUILayout.Toggle(new GUIContent("Enable Tooltips", "Show tooltips when hovering over UI elements"), enableTooltips);

            if (EditorGUI.EndChangeCheck())
            {
                SaveSettings();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Data Management
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Data Management", sectionHeaderStyle);

            // Add explanatory text
            EditorGUILayout.HelpBox("These options allow you to reset or manage your leveling system data.", MessageType.Info);
            EditorGUILayout.Space(5);

            // Reset buttons with explanations
            DrawButtonWithDescription(
                "Reset Difficulty Ratings to Default",
                "Resets all difficulty ratings (Very Easy, Easy, Medium, etc.) to their default values and colors.",
                () => {
                    if (EditorUtility.DisplayDialog("Reset Difficulty Ratings",
                        "Are you sure you want to reset all difficulty ratings to their default values? This cannot be undone.",
                        "Yes, Reset", "Cancel"))
                    {
                        ResetDifficultyDescriptions();
                    }
                }
            );

            DrawButtonWithDescription(
                "Reset Experience Rewards to Default",
                "Resets all experience reward categories and their multipliers to default values.",
                () => {
                    if (EditorUtility.DisplayDialog("Reset Experience Rewards",
                        "Are you sure you want to reset all experience reward settings to their default values? This cannot be undone.",
                        "Yes, Reset", "Cancel"))
                    {
                        ResetExperienceRewards();
                    }
                }
            );

            EditorGUILayout.Space(5);

            // Import/Export with explanations
            DrawButtonWithDescription(
                "Export Settings",
                "Exports your current editor settings to a JSON file that can be imported later.",
                () => {
                    ExportSettings();
                }
            );

            DrawButtonWithDescription(
                "Import Settings",
                "Imports editor settings from a previously exported JSON file.",
                () => {
                    ImportSettings();
                }
            );

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Component Management
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Component Management", sectionHeaderStyle);

            // Add explanatory text
            EditorGUILayout.HelpBox("Use this to add the leveling system component to a GameObject in your scene.", MessageType.Info);
            EditorGUILayout.Space(5);

            DrawButtonWithDescription(
                "Find or Create Leveling System Component",
                "Locates an existing LevelingSystem component in your scene or creates a new one if none exists.",
                () => {
                    AdvancedLevelingSystemEditor.FindOrCreateComponent();
                }
            );

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Database Management
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Algorithm Management", sectionHeaderStyle);

            // Add explanatory text
            EditorGUILayout.HelpBox("These options help you create and manage leveling algorithms.", MessageType.Info);
            EditorGUILayout.Space(5);

            // Get all available algorithms for the dropdown
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.LabelField("Default Algorithm", EditorStyles.boldLabel);

                // Get the current default algorithm
                LevelingSystemData levelingSystemData = Resources.Load<LevelingSystemData>("LevelingSystemData");
                int currentIndex = 0;

                if (levelingSystemData != null && levelingSystemData.levelingAlgorithm != null)
                {
                    currentIndex = algorithmDB.algorithms.IndexOf(levelingSystemData.levelingAlgorithm);
                    if (currentIndex < 0) currentIndex = 0;
                }

                // Create a searchable dropdown for algorithm selection
                string[] algorithmNames = algorithmDB.algorithms.ConvertAll(a => a.Name).ToArray();

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    currentIndex,
                    "Default Algorithm",
                    algorithmNames,
                    (newIndex) => {
                        if (levelingSystemData != null)
                        {
                            levelingSystemData.levelingAlgorithm = algorithmDB.algorithms[newIndex];
                            EditorUtility.SetDirty(levelingSystemData);
                            AssetDatabase.SaveAssets();
                        }
                    }
                );

                EditorGUILayout.Space(5);
            }

            DrawButtonWithDescription(
                "Create Common Algorithms",
                "Creates a complete set of pre-defined algorithms (Linear, Exponential, Sinusoidal, Crescendo, Cosine, Asymptotic, Burst, etc.) and adds them to your database.",
                () => {
                    if (EditorUtility.DisplayDialog("Create Common Algorithms",
                        "This will create a complete set of algorithms (including Crescendo, Cosine, Asymptotic, and Burst) and add them to the database. Continue?",
                        "Yes, Create", "Cancel"))
                    {
                        CreateCommonAlgorithms();
                    }
                }
            );

            DrawButtonWithDescription(
                "Fix Missing Difficulty Ratings",
                "Assigns a default difficulty rating to all algorithms that don't have one.",
                () => {
                    if (EditorUtility.DisplayDialog("Fix Missing Difficulty Ratings",
                        "This will assign a default difficulty rating to all algorithms that don't have one. Continue?",
                        "Yes, Fix", "Cancel"))
                    {
                        FixMissingDifficultyRatings();
                    }
                }
            );

            DrawButtonWithDescription(
                "Convert Algorithms to ScriptableObjects",
                "Converts any code-based algorithms to ScriptableObjects for easier management and editing.",
                () => {
                    if (EditorUtility.DisplayDialog("Convert Algorithms",
                        "This will convert all code-based algorithms to ScriptableObjects. Continue?",
                        "Yes, Convert", "Cancel"))
                    {
                        AlgorithmDatabaseManager.ConvertAlgorithms();
                    }
                }
            );

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Advanced Options
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Advanced Options", sectionHeaderStyle);

            // Add explanatory text
            EditorGUILayout.HelpBox("These options are for advanced users or troubleshooting. Use with caution.", MessageType.Warning);
            EditorGUILayout.Space(5);

            DrawButtonWithDescription(
                "Initialize Default Data",
                "Creates or resets ALL data files to their default values. Use this if your system is broken or for a fresh start.",
                () => {
                    if (EditorUtility.DisplayDialog("Initialize Default Data",
                        "This will create or reset all data files to their default values. Any customizations will be lost. Continue?",
                        "Yes, Initialize", "Cancel"))
                    {
                        InitializeDefaultData();
                    }
                }
            );

            DrawButtonWithDescription(
                "Open Resources Folder",
                "Opens the Resources folder where all leveling system data is stored for direct access.",
                () => {
                    OpenResourcesFolder();
                }
            );

            GUI.backgroundColor = Color.white; // Reset color

            EditorGUILayout.EndVertical();

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Draws a button with a description below it
        /// </summary>
        private void DrawButtonWithDescription(string buttonText, string description, System.Action onClick)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Button
            if (GUILayout.Button(buttonText, GUILayout.Height(30)))
            {
                onClick?.Invoke();
            }

            // Description
            EditorGUILayout.LabelField(description, EditorStyles.wordWrappedMiniLabel);

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(5);
        }

        private void ResetDifficultyDescriptions()
        {
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset";

            // Delete the existing asset
            if (File.Exists(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }

            // Create a new database using the manager
            DifficultyRatingDatabaseManager.CreateDatabase();

            Debug.Log("Difficulty ratings have been reset to default values.");
        }

        private void ResetExperienceRewards()
        {
            string assetPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources/ExperienceRewardsData.asset";

            // Delete the existing asset
            if (File.Exists(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }

            // Create a new asset
            ExperienceRewardsData asset = ScriptableObject.CreateInstance<ExperienceRewardsData>();

            // Set default values
            // Find a SineWave algorithm or use the first available algorithm
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Try to find a SineWave algorithm or use the first one
                LevelingAlgorithmBase sineWaveAlgorithm = algorithmDB.algorithms.Find(a => a.Name.Contains("Sine"));
                asset.selectedAlgorithm = sineWaveAlgorithm != null ? sineWaveAlgorithm : algorithmDB.algorithms[0];
            }
            asset.levelUpMultiplier = 1.1f;
            asset.startingExperience = 250;
            asset.startingLevel = 1;
            asset.maxLevel = 50;
            asset.levelRangeSize = 5;
            asset.showDetailedBreakdown = true;

            // Add default reward categories in the desired order
            // Quest categories
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Epic Quest", multiplier = 0.12f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Hard Quest", multiplier = 0.08f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Medium Quest", multiplier = 0.05f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Easy Quest", multiplier = 0.03f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Daily Activity", multiplier = 0.02f });

            // Boss and special enemies
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "World Boss", multiplier = 0.10f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Boss Enemy", multiplier = 0.05f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Elite Enemy", multiplier = 0.015f });

            // Regular enemies
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Hard Enemy", multiplier = 0.003f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Medium Enemy", multiplier = 0.002f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Easy Enemy", multiplier = 0.001f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Very Easy Enemy", multiplier = 0.0005f });

            // Crafting and gathering
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Crafting Item", multiplier = 0.0001f });
            asset.rewardCategories.Add(new ExperienceRewardsData.RewardCategory { name = "Gathering Resource", multiplier = 0.0001f });

            AssetDatabase.CreateAsset(asset, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log("Experience rewards have been reset to default values.");
        }

        private void ExportSettings()
        {
            string path = EditorUtility.SaveFilePanel("Export Settings", "", "LevelingSystemSettings", "json");
            if (string.IsNullOrEmpty(path))
                return;

            // Create a settings object to serialize
            var settings = new
            {
                EditorSettings = new
                {
                    AutoSaveChanges = autoSaveChanges,
                    ShowDebugInfo = showDebugInfo,
                    EnableTooltips = enableTooltips
                }
            };

            // Serialize to JSON
            string json = JsonUtility.ToJson(settings, true);
            File.WriteAllText(path, json);

            Debug.Log("Settings exported successfully to: " + path);
        }

        private void ImportSettings()
        {
            string path = EditorUtility.OpenFilePanel("Import Settings", "", "json");
            if (string.IsNullOrEmpty(path))
                return;

            try
            {
                string json = File.ReadAllText(path);
                // Deserialize from JSON
                // Note: This is a simplified version, in a real implementation you'd need to parse the JSON properly
                JsonUtility.FromJsonOverwrite(json, this);
                SaveSettings();

                Debug.Log("Settings imported successfully from: " + path);
            }
            catch (System.Exception e)
            {
                Debug.LogError("Error importing settings: " + e.Message);
                EditorUtility.DisplayDialog("Import Error", "Failed to import settings: " + e.Message, "OK");
            }
        }

        private void OpenResourcesFolder()
        {
            string path = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";

            // Create the directory if it doesn't exist
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
                AssetDatabase.Refresh();
            }

            // Select the folder in the Project window
            UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
            Selection.activeObject = obj;
            EditorGUIUtility.PingObject(obj);
        }

        private void InitializeDefaultData()
        {
            string resourcesFolder = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";
            string experienceRewardsDataPath = resourcesFolder + "/ExperienceRewardsData.asset";
            string levelingSystemDataPath = resourcesFolder + "/LevelingSystemData.asset";

            // Ensure the Resources folder exists
            if (!Directory.Exists(resourcesFolder))
            {
                Directory.CreateDirectory(resourcesFolder);
            }

            // Create and initialize the Experience Rewards Data
            CreateExperienceRewardsData(experienceRewardsDataPath);

            // Create and initialize the Leveling System Data
            CreateLevelingSystemData(levelingSystemDataPath);

            // Refresh the AssetDatabase to show the new assets
            AssetDatabase.Refresh();

            Debug.Log("Default data initialized successfully.");
        }

        private void CreateExperienceRewardsData(string assetPath)
        {
            // Check if the asset already exists
            ExperienceRewardsData rewardsData = AssetDatabase.LoadAssetAtPath<ExperienceRewardsData>(assetPath);

            if (rewardsData == null)
            {
                // Create a new instance if it doesn't exist
                rewardsData = ScriptableObject.CreateInstance<ExperienceRewardsData>();
                AssetDatabase.CreateAsset(rewardsData, assetPath);
            }

            // Clear existing categories
            rewardsData.rewardCategories.Clear();

            // Add default reward categories with their multipliers in the desired order
            // Quest categories
            AddRewardCategory(rewardsData, "Epic Quest", 0.12f);
            AddRewardCategory(rewardsData, "Hard Quest", 0.08f);
            AddRewardCategory(rewardsData, "Medium Quest", 0.05f);
            AddRewardCategory(rewardsData, "Easy Quest", 0.03f);
            AddRewardCategory(rewardsData, "Daily Activity", 0.02f);

            // Boss and special enemies
            AddRewardCategory(rewardsData, "World Boss", 0.10f);
            AddRewardCategory(rewardsData, "Boss Enemy", 0.05f);
            AddRewardCategory(rewardsData, "Elite Enemy", 0.015f);

            // Regular enemies
            AddRewardCategory(rewardsData, "Hard Enemy", 0.003f);
            AddRewardCategory(rewardsData, "Medium Enemy", 0.002f);
            AddRewardCategory(rewardsData, "Easy Enemy", 0.001f);
            AddRewardCategory(rewardsData, "Very Easy Enemy", 0.0005f);

            // Crafting and gathering
            AddRewardCategory(rewardsData, "Crafting Item", 0.0001f);
            AddRewardCategory(rewardsData, "Gathering Resource", 0.0001f);

            // Set default values
            // Find a SineWave algorithm or use the first available algorithm
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Try to find a SineWave algorithm or use the first one
                LevelingAlgorithmBase sineWaveAlgorithm = algorithmDB.algorithms.Find(a => a.Name.Contains("Sine"));
                rewardsData.selectedAlgorithm = sineWaveAlgorithm != null ? sineWaveAlgorithm : algorithmDB.algorithms[0];
            }
            rewardsData.levelUpMultiplier = 1.1f;
            rewardsData.startingExperience = 250;
            rewardsData.startingLevel = 1;
            rewardsData.maxLevel = 50;
            rewardsData.levelRangeSize = 5;
            rewardsData.showDetailedBreakdown = true;

            // Save the changes
            EditorUtility.SetDirty(rewardsData);
            AssetDatabase.SaveAssets();

            Debug.Log("Experience Rewards Data initialized at: " + assetPath);
        }

        private void AddRewardCategory(ExperienceRewardsData data, string name, float multiplier)
        {
            data.rewardCategories.Add(new ExperienceRewardsData.RewardCategory
            {
                name = name,
                multiplier = multiplier
            });
        }

        private void CreateLevelingSystemData(string assetPath)
        {
            // Check if the asset already exists
            LevelingSystemData levelingSystemData = AssetDatabase.LoadAssetAtPath<LevelingSystemData>(assetPath);

            if (levelingSystemData == null)
            {
                // Create a new instance if it doesn't exist
                levelingSystemData = ScriptableObject.CreateInstance<LevelingSystemData>();
                AssetDatabase.CreateAsset(levelingSystemData, assetPath);
            }

            // Set default values
            levelingSystemData.maxLevel = 30;
            levelingSystemData.initialRequiredExperience = 250;
            levelingSystemData.levelUpMultiplier = 1.1f;

            // Set default algorithm
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Try to find a linear algorithm or use the first one
                LevelingAlgorithmBase linearAlgorithm = algorithmDB.algorithms.Find(a => a.Name.Contains("Linear"));
                levelingSystemData.levelingAlgorithm = linearAlgorithm != null ? linearAlgorithm : algorithmDB.algorithms[0];
            }


            // Save the changes
            EditorUtility.SetDirty(levelingSystemData);
            AssetDatabase.SaveAssets();

            Debug.Log("Leveling System Data initialized at: " + assetPath);
        }

        /// <summary>
        /// Creates common algorithms and adds them to the database
        /// </summary>
        private void CreateCommonAlgorithms()
        {
            // Call the CreateDefaultAlgorithms method from CreateDefaultAlgorithmsMenu
            // This will create all algorithms, including our newly added ones (Crescendo, Cosine, Asymptotic, and Burst)
            CreateDefaultAlgorithmsMenu.CreateDefaultAlgorithms();

            // Force refresh the algorithm registry to ensure all algorithms are available
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

            Debug.Log("Algorithm database populated with all default algorithms.");
        }

        /// <summary>
        /// Converts coded algorithms to scriptable objects
        /// </summary>
        private void ConvertCodedAlgorithmsToScriptableObjects()
        {
            // Call the existing conversion method
            AlgorithmDatabaseManager.ConvertAlgorithms();
        }

        /// <summary>
        /// Fixes all algorithms with missing difficulty ratings in the project
        /// </summary>
        private void FixMissingDifficultyRatings()
        {
            // Find all LevelingAlgorithmBase assets in the project
            string[] guids = AssetDatabase.FindAssets("t:LevelingAlgorithmBase");

            if (guids.Length == 0)
            {
                Debug.LogWarning("No algorithm assets found in the project.");
                return;
            }

            int fixedCount = 0;

            // Process each found asset
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                if (algorithm != null && algorithm.difficultyRating == null)
                {
                    FixMissingDifficultyRating(algorithm);
                    EditorUtility.SetDirty(algorithm);
                    fixedCount++;
                }
            }

            // Save all changes
            AssetDatabase.SaveAssets();

            // Show a message with the results
            if (fixedCount > 0)
            {
                Debug.Log($"Fixed {fixedCount} algorithm(s) with missing difficulty ratings.");
                EditorUtility.DisplayDialog("Fix Missing Difficulty Ratings", $"Fixed {fixedCount} algorithm(s) with missing difficulty ratings.", "OK");
            }
            else
            {
                Debug.Log("No algorithms with missing difficulty ratings found.");
                EditorUtility.DisplayDialog("Fix Missing Difficulty Ratings", "No algorithms with missing difficulty ratings found.", "OK");
            }

            // Force refresh the algorithm registry to ensure all algorithms are available
            ScriptableAlgorithmRegistry.ForceLoadFromDatabase();
        }

        /// <summary>
        /// Fixes algorithms with missing difficulty ratings
        /// </summary>
        private void FixMissingDifficultyRating(LevelingAlgorithmBase algorithm)
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");

            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Cannot fix missing difficulty ratings.");
                return;
            }

            // Find the "Medium" difficulty rating (or use the first one if "Medium" doesn't exist)
            DifficultyRating mediumRating = null;

            foreach (var rating in ratingDB.difficultyRatings)
            {
                if (rating.ratingName == "Medium")
                {
                    mediumRating = rating;
                    break;
                }
            }

            // If "Medium" wasn't found, use the first rating
            if (mediumRating == null && ratingDB.difficultyRatings.Count > 0)
            {
                mediumRating = ratingDB.difficultyRatings[0];
            }

            // Assign the difficulty rating
            if (mediumRating != null)
            {
                Debug.Log($"Fixing missing difficulty rating for algorithm '{algorithm.algorithmName}'. Assigning '{mediumRating.ratingName}' difficulty.");
                algorithm.difficultyRating = mediumRating;
            }
        }
    }
}
