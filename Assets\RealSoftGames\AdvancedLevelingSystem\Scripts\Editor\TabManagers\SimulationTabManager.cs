using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System;
using System.Linq;
using RealSoftGames;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Tab manager for simulating player progression and visualizing level-up times
    /// </summary>
    public class SimulationTabManager : ITabManager
    {
        #region Fields and Properties
        // Reference to the main editor window
        private readonly AdvancedLevelingSystemEditorWindow window;

        // UI Styles
        private GUIStyle headerStyle;
        private GUIStyle subHeaderStyle;
        private GUIStyle sectionBoxStyle;
        private Vector2 scrollPosition;

        // Tab properties
        public string TabName => "Simulation";

        #region Algorithm Settings
        // Simulation settings
        private ILevelingAlgorithm selectedAlgorithm;
        private float levelUpMultiplier = 1.1f;
        private int startingExperience = 250;
        private int startingLevel = 1;
        private int maxLevel = 50;

        // Algorithm visualization
        private LevelingCurvePreview curvePreview;
        #endregion

        #region Player Activity Settings
        // Player activity settings
        private enum PlayerType { Casual, Regular, Hardcore }
        private PlayerType selectedPlayerType = PlayerType.Regular;

        // Game genre options
        private enum GameGenre {
            RPG,
            MMORPG,
            ActionAdventure,
            FPS,
            MOBA,
            Strategy,
            Puzzle,
            Casual
        }
        private GameGenre selectedGenre = GameGenre.RPG;

        // Dictionary of game genre descriptions
        private Dictionary<GameGenre, string> genreDescriptions = new Dictionary<GameGenre, string>
        {
            { GameGenre.RPG, "Single-player RPGs with quest-based progression" },
            { GameGenre.MMORPG, "Online RPGs with grinding and raid-based progression" },
            { GameGenre.ActionAdventure, "Story-driven games with combat and exploration" },
            { GameGenre.FPS, "First-person shooters with match-based progression" },
            { GameGenre.MOBA, "Competitive team-based games with match progression" },
            { GameGenre.Strategy, "Turn-based or real-time strategy games" },
            { GameGenre.Puzzle, "Puzzle games with level-based progression" },
            { GameGenre.Casual, "Mobile or casual games with quick sessions" }
        };

        // Dictionary of typical actions per genre
        private Dictionary<GameGenre, string[]> genreActions = new Dictionary<GameGenre, string[]>
        {
            { GameGenre.RPG, new[] { "Complete Quest", "Defeat Enemy", "Discover Location", "Craft Item", "Complete Dungeon" } },
            { GameGenre.MMORPG, new[] { "Kill Monster", "Complete Quest", "Raid Boss", "Dungeon Run", "Craft Item", "PvP Match" } },
            { GameGenre.ActionAdventure, new[] { "Complete Mission", "Defeat Enemy", "Discover Area", "Collect Item", "Boss Fight" } },
            { GameGenre.FPS, new[] { "Kill Enemy", "Win Match", "Complete Objective", "Headshot", "Win Tournament" } },
            { GameGenre.MOBA, new[] { "Win Match", "Kill Enemy Hero", "Destroy Tower", "Complete Objective", "MVP Award" } },
            { GameGenre.Strategy, new[] { "Win Battle", "Complete Mission", "Research Technology", "Build Structure", "Win Campaign" } },
            { GameGenre.Puzzle, new[] { "Complete Level", "Perfect Score", "Time Challenge", "Collect Star", "Complete World" } },
            { GameGenre.Casual, new[] { "Complete Level", "Daily Reward", "Watch Ad", "Use Power-up", "Daily Challenge" } }
        };

        // Settings for each player type
        private Dictionary<PlayerType, PlayerTypeSettings> playerTypeSettings = new Dictionary<PlayerType, PlayerTypeSettings>();

        // Current settings (based on selected player type)
        private float playTimePerDay = 2.0f; // hours
        private float actionsPerMinute = 10.0f;
        private float averageXpPerAction = 5.0f;
        #endregion

        // Class to store settings for each player type
        [Serializable]
        private class PlayerTypeSettings
        {
            public float playTimePerDay;
            public float actionsPerMinute;
            public float averageXpPerAction;
            public Dictionary<string, float> actionFrequencies = new Dictionary<string, float>();
            public Dictionary<string, float> actionXpValues = new Dictionary<string, float>();

            public PlayerTypeSettings(float playTime, float actions, float xpPerAction)
            {
                playTimePerDay = playTime;
                actionsPerMinute = actions;
                averageXpPerAction = xpPerAction;
            }
        }

        // Reward categories and their multipliers
        private Dictionary<string, float> rewardCategories = new Dictionary<string, float>();

        // Cached data
        private List<int> experienceRequirements;
        private List<SimulationResult> simulationResults;
        #endregion

        // Simulation result class
        private class SimulationResult
        {
            public int Level { get; set; }
            public int ExperienceRequired { get; set; }
            public float HoursToLevel { get; set; }
            public float DaysToLevel { get; set; }
            public float TotalDays { get; set; }
            public float TotalHours { get; set; }
            public DateTime EstimatedDate { get; set; }
        }

        public SimulationTabManager(AdvancedLevelingSystemEditorWindow window)
        {
            this.window = window;

            // Load default reward categories
            InitializeDefaultRewardCategories();
        }

        public void OnEnable()
        {
            // Initialize styles
            InitializeStyles();

            // Initialize player type settings if not already done
            if (playerTypeSettings.Count == 0)
            {
                InitializePlayerTypeSettings();
            }

            // Calculate experience requirements
            CalculateExperienceRequirements();

            // Run simulation
            RunSimulation();
        }

        private void InitializePlayerTypeSettings()
        {
            // Clear existing settings
            playerTypeSettings.Clear();

            // Add default settings for each player type
            playerTypeSettings[PlayerType.Casual] = new PlayerTypeSettings(2.0f, 5.0f, 5.0f);
            playerTypeSettings[PlayerType.Regular] = new PlayerTypeSettings(4.0f, 10.0f, 5.0f);
            playerTypeSettings[PlayerType.Hardcore] = new PlayerTypeSettings(6.0f, 15.0f, 5.0f);

            // Set initial values based on selected player type
            UpdatePlayerTypeSettings();
        }

        private void UpdatePlayerTypeSettings()
        {
            if (playerTypeSettings.TryGetValue(selectedPlayerType, out PlayerTypeSettings settings))
            {
                playTimePerDay = settings.playTimePerDay;
                actionsPerMinute = settings.actionsPerMinute;
                averageXpPerAction = settings.averageXpPerAction;
            }
        }

        private void UpdateGenreSpecificSettings()
        {
            // Get actions for the selected genre
            if (genreActions.TryGetValue(selectedGenre, out string[] actions))
            {
                // Update each player type's settings for this genre
                foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                {
                    if (playerTypeSettings.TryGetValue(type, out PlayerTypeSettings settings))
                    {
                        // Initialize action frequencies and XP values if they don't exist
                        foreach (string action in actions)
                        {
                            if (!settings.actionFrequencies.ContainsKey(action))
                            {
                                // Set default values based on player type and action type
                                float defaultFrequency;
                                float defaultXp;

                                // Assign XP values based on action type
                                if (action.Contains("Boss") || action.Contains("Raid") || action.Contains("Tournament") || action.Contains("Campaign"))
                                {
                                    // Boss/Raid activities are rare but high value
                                    defaultFrequency = 5f;
                                    defaultXp = type == PlayerType.Casual ? 30f : (type == PlayerType.Regular ? 45f : 60f);
                                }
                                else if (action.Contains("Quest") || action.Contains("Mission") || action.Contains("Dungeon") || action.Contains("Match"))
                                {
                                    // Quests/Missions are common and medium value
                                    defaultFrequency = 30f;
                                    defaultXp = type == PlayerType.Casual ? 15f : (type == PlayerType.Regular ? 20f : 30f);
                                }
                                else if (action.Contains("Complete"))
                                {
                                    // Completion activities are medium frequency
                                    defaultFrequency = 20f;
                                    defaultXp = type == PlayerType.Casual ? 10f : (type == PlayerType.Regular ? 15f : 25f);
                                }
                                else
                                {
                                    // Regular activities are most common but lower value
                                    defaultFrequency = 45f / (actions.Length - 1); // Distribute remaining percentage among other activities
                                    defaultXp = type == PlayerType.Casual ? 5f : (type == PlayerType.Regular ? 10f : 15f);
                                }

                                settings.actionFrequencies[action] = defaultFrequency;
                                settings.actionXpValues[action] = defaultXp;
                            }
                        }

                        // Calculate the weighted average XP per action
                        float totalFrequency = 0;
                        float totalXpPerAction = 0;

                        foreach (string action in actions)
                        {
                            if (settings.actionFrequencies.ContainsKey(action) && settings.actionXpValues.ContainsKey(action))
                            {
                                totalFrequency += settings.actionFrequencies[action];
                                totalXpPerAction += settings.actionFrequencies[action] * settings.actionXpValues[action] / 100f;
                            }
                        }

                        // Update the average XP per action if we have valid data
                        if (totalFrequency > 0)
                        {
                            settings.averageXpPerAction = totalXpPerAction;

                            // If this is the selected player type, update the current value
                            if (type == selectedPlayerType)
                            {
                                averageXpPerAction = totalXpPerAction;
                            }
                        }
                    }
                }

                // Run simulation with updated values
                RunSimulation();
            }
        }

        public void OnDisable()
        {
            // No cleanup needed
        }

        public void OnDestroy()
        {
            // Clean up resources if needed
        }

        public void Update()
        {
            // No continuous updates needed
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (subHeaderStyle == null)
            {
                subHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (sectionBoxStyle == null)
            {
                sectionBoxStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(10, 10, 10, 10),
                    margin = new RectOffset(0, 0, 5, 5)
                };
            }
        }

        private void InitializeDefaultRewardCategories()
        {
            // Clear existing categories
            rewardCategories.Clear();

            // Add default reward categories with their multipliers
            rewardCategories.Add("Epic Quest", 0.12f);
            rewardCategories.Add("Hard Quest", 0.08f);
            rewardCategories.Add("Medium Quest", 0.05f);
            rewardCategories.Add("Easy Quest", 0.03f);
            rewardCategories.Add("Daily Activity", 0.02f);
            rewardCategories.Add("World Boss", 0.10f);
            rewardCategories.Add("Boss Enemy", 0.05f);
            rewardCategories.Add("Elite Enemy", 0.015f);
            rewardCategories.Add("Hard Enemy", 0.003f);
            rewardCategories.Add("Medium Enemy", 0.002f);
            rewardCategories.Add("Easy Enemy", 0.001f);
        }

        private void CalculateExperienceRequirements()
        {
            if (selectedAlgorithm == null)
            {
                // Try to load a default algorithm
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a linear algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a.Name.Contains("Linear"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }
                }
                else
                {
                    // No algorithms found, create an empty list
                    experienceRequirements = new List<int>();
                    return;
                }
            }

            // Log the algorithm we're using for debugging
            // Debug.Log($"SimulationTabManager: Calculating experience requirements using algorithm: {selectedAlgorithm.Name} with multiplier: {levelUpMultiplier}");

            // Force recalculation by clearing any cached values
            if (selectedAlgorithm is LevelingAlgorithmBase scriptableAlgorithm)
            {
                scriptableAlgorithm.ClearCachedValues();
            }

            // Calculate experience requirements using the selected algorithm's CalculateRequirementCurve method
            experienceRequirements = selectedAlgorithm.CalculateRequirementCurve(
                startingExperience,
                startingLevel,
                maxLevel,
                levelUpMultiplier);

            // Log the first few values for debugging
            // if (experienceRequirements.Count > 0)
            // {
            //     string debugValues = string.Join(", ", experienceRequirements.Take(5));
            //     Debug.Log($"SimulationTabManager: First 5 experience requirements: {debugValues}");
            // }
        }

        private void RunSimulation()
        {
            // Ensure we have valid experience requirements
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                // Try to recalculate experience requirements
                CalculateExperienceRequirements();

                // If still empty, return
                if (experienceRequirements == null || experienceRequirements.Count == 0)
                {
                    // Debug.LogWarning("SimulationTabManager: Cannot run simulation - no experience requirements calculated.");
                    return;
                }
            }

            // Log that we're running the simulation
            // Debug.Log($"SimulationTabManager: Running simulation with algorithm: {selectedAlgorithm.Name}, levels: {startingLevel} to {maxLevel}, multiplier: {levelUpMultiplier}");

            // Create a new list for simulation results
            simulationResults = new List<SimulationResult>();

            // Calculate XP per hour based on player type
            float xpPerHour = CalculateXpPerHour();
            // Debug.Log($"SimulationTabManager: XP per hour: {xpPerHour} (actions per minute: {actionsPerMinute}, XP per action: {averageXpPerAction})");

            // Track cumulative time
            float totalHours = 0;
            float totalDays = 0;
            DateTime currentDate = DateTime.Now;

            // Add entry for starting level
            simulationResults.Add(new SimulationResult
            {
                Level = startingLevel,
                ExperienceRequired = startingExperience,
                HoursToLevel = 0,
                DaysToLevel = 0,
                TotalHours = 0,
                TotalDays = 0,
                EstimatedDate = currentDate
            });

            // Calculate time to reach each level
            for (int i = 0; i < experienceRequirements.Count; i++)
            {
                int level = startingLevel + i + 1;

                // Skip levels beyond maxLevel
                if (level > maxLevel)
                    break;

                int expRequired = experienceRequirements[i];

                // Calculate time needed
                float hoursToLevel = expRequired / xpPerHour;
                float daysToLevel = hoursToLevel / playTimePerDay;

                // Update totals
                totalHours += hoursToLevel;
                totalDays += daysToLevel;

                // Prevent DateTime overflow errors by capping extremely large day values
                // DateTime can only handle about 10,000 years worth of days
                const float MAX_SAFE_DAYS = 3650000f; // ~10,000 years
                float safeDaysToAdd = Mathf.Min(daysToLevel, MAX_SAFE_DAYS);

                try
                {
                    currentDate = currentDate.AddDays(safeDaysToAdd);
                }
                catch (ArgumentOutOfRangeException)
                {
                    // If we still get an overflow, use the maximum date value
                    Debug.LogWarning($"Date calculation reached maximum limit at level {level}. Using maximum date.");
                    currentDate = new DateTime(9999, 12, 31); // Maximum valid DateTime
                }

                // Add to results
                simulationResults.Add(new SimulationResult
                {
                    Level = level,
                    ExperienceRequired = expRequired,
                    HoursToLevel = hoursToLevel,
                    DaysToLevel = daysToLevel,
                    TotalHours = totalHours,
                    TotalDays = totalDays,
                    EstimatedDate = currentDate
                });
            }
        }

        private float CalculateXpPerHour()
        {
            // Update settings from the selected player type
            UpdatePlayerTypeSettings();

            // Calculate XP per hour
            return actionsPerMinute * 60 * averageXpPerAction;
        }

        private void NormalizeActionFrequencies()
        {
            // Get the current player type settings
            if (playerTypeSettings.TryGetValue(selectedPlayerType, out PlayerTypeSettings currentSettings))
            {
                // Get actions for the selected genre
                if (genreActions.TryGetValue(selectedGenre, out string[] actions))
                {
                    // Calculate total frequency
                    float totalFrequency = 0;
                    foreach (string action in actions)
                    {
                        if (currentSettings.actionFrequencies.ContainsKey(action))
                        {
                            totalFrequency += currentSettings.actionFrequencies[action];
                        }
                    }

                    // Only normalize if total is not already 100%
                    if (Mathf.Abs(totalFrequency - 100f) > 0.01f && totalFrequency > 0)
                    {
                        // Normalize all frequencies to sum to 100%
                        float factor = 100f / totalFrequency;
                        foreach (string action in actions)
                        {
                            if (currentSettings.actionFrequencies.ContainsKey(action))
                            {
                                currentSettings.actionFrequencies[action] *= factor;
                            }
                        }
                    }
                }
            }
        }

        #region GUI Methods
        public void OnGUI()
        {
            InitializeStyles();

            EditorGUILayout.BeginVertical();
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            EditorGUILayout.LabelField("Player Progression Simulation", headerStyle);
            EditorGUILayout.Space(10);

            #region Algorithm and Player Settings

            // Algorithm settings
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Algorithm Settings", subHeaderStyle);
            EditorGUILayout.Space(5);

            EditorGUI.BeginChangeCheck();

            // Algorithm selection
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Create a searchable popup for algorithm selection
                string[] algorithmNames = algorithmDB.algorithms.ConvertAll(a => a.Name).ToArray();
                int currentIndex = 0;
                if (selectedAlgorithm != null && selectedAlgorithm is LevelingAlgorithmBase levelingAlgorithm)
                {
                    currentIndex = algorithmDB.algorithms.IndexOf(levelingAlgorithm);
                }

                // Ensure valid index
                if (currentIndex < 0) currentIndex = 0;

                // Use the searchable popup from Utilities
                int newIndex = Utilities.SearchablePopup(
                    currentIndex,
                    "Algorithm",
                    algorithmNames,
                    (index) => {
                        // Log the algorithm change
                        Debug.Log($"SimulationTabManager: Changing algorithm from {(selectedAlgorithm != null ? selectedAlgorithm.Name : "null")} to {algorithmDB.algorithms[index].Name}");

                        // Set the new algorithm
                        selectedAlgorithm = algorithmDB.algorithms[index];

                        // Force recalculation
                        if (selectedAlgorithm is LevelingAlgorithmBase scriptableAlgorithm)
                        {
                            scriptableAlgorithm.ClearCachedValues();
                        }

                        // Recalculate experience requirements and run simulation
                        CalculateExperienceRequirements();
                        RunSimulation();

                        // Force repaint to update the UI
                        window.Repaint();
                    }
                );

                if (newIndex != currentIndex)
                {
                    // Log the algorithm change
                    Debug.Log($"SimulationTabManager: Changing algorithm from {(selectedAlgorithm != null ? selectedAlgorithm.Name : "null")} to {algorithmDB.algorithms[newIndex].Name}");

                    // Set the new algorithm
                    selectedAlgorithm = algorithmDB.algorithms[newIndex];

                    // Force recalculation
                    if (selectedAlgorithm is LevelingAlgorithmBase scriptableAlgorithm)
                    {
                        scriptableAlgorithm.ClearCachedValues();
                    }
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No algorithms found in the database.", MessageType.Warning);
            }

            // Level up multiplier (minimum 1.0 to avoid issues with values below 1.0)
            float newMultiplier = EditorGUILayout.Slider("Level Up Multiplier", levelUpMultiplier, 1.0f, 2.0f);
            if (newMultiplier != levelUpMultiplier)
            {
                Debug.Log($"SimulationTabManager: Changing level up multiplier from {levelUpMultiplier} to {newMultiplier}");
                levelUpMultiplier = newMultiplier;

                // Force recalculation when multiplier changes
                if (selectedAlgorithm is LevelingAlgorithmBase scriptableAlgorithm)
                {
                    scriptableAlgorithm.ClearCachedValues();
                }
            }

            // Starting experience
            int newStartingExp = EditorGUILayout.IntField("Starting Experience", startingExperience);
            if (newStartingExp != startingExperience && newStartingExp > 0)
            {
                startingExperience = newStartingExp;
            }

            // Starting level
            int newStartingLevel = EditorGUILayout.IntSlider("Starting Level", startingLevel, 1, 10);
            if (newStartingLevel != startingLevel)
            {
                startingLevel = newStartingLevel;
            }

            // Max level
            int newMaxLevel = EditorGUILayout.IntSlider("Max Level", maxLevel, 10, 100);
            if (newMaxLevel != maxLevel && newMaxLevel > startingLevel)
            {
                maxLevel = newMaxLevel;
            }

            if (EditorGUI.EndChangeCheck())
            {
                CalculateExperienceRequirements();
                RunSimulation();
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Player activity settings
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Player Activity Settings", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Game Genre Selection
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Game Genre", EditorStyles.boldLabel);

            EditorGUI.BeginChangeCheck();
            GameGenre newGenre = (GameGenre)EditorGUILayout.EnumPopup("Select Game Genre", selectedGenre);
            if (EditorGUI.EndChangeCheck() && newGenre != selectedGenre)
            {
                selectedGenre = newGenre;
                // Update action frequencies and XP values based on the new genre
                UpdateGenreSpecificSettings();
                // Normalize action frequencies for the new genre
                NormalizeActionFrequencies();
            }

            // Display genre description
            if (genreDescriptions.TryGetValue(selectedGenre, out string description))
            {
                EditorGUILayout.HelpBox(description, MessageType.Info);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(5);

            // Display the currently selected player type in a disabled dropdown for reference
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Active Player Type:", GUILayout.Width(120));
            GUIStyle activeTypeStyle = new GUIStyle(EditorStyles.boldLabel);
            activeTypeStyle.normal.textColor = GetPlayerTypeColor(selectedPlayerType);
            EditorGUILayout.LabelField(selectedPlayerType.ToString(), activeTypeStyle);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Create a horizontal layout for all player types
            EditorGUILayout.BeginHorizontal();

            // For each player type, create a vertical column with settings
            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                // Get settings for this player type
                if (!playerTypeSettings.TryGetValue(type, out PlayerTypeSettings settings))
                {
                    // If settings don't exist, create default ones
                    settings = new PlayerTypeSettings(
                        type == PlayerType.Casual ? 2.0f : (type == PlayerType.Regular ? 4.0f : 6.0f),
                        type == PlayerType.Casual ? 5.0f : (type == PlayerType.Regular ? 10.0f : 15.0f),
                        5.0f);
                    playerTypeSettings[type] = settings;
                }

                // Create a vertical column for this player type
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Player type header with color
                GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
                headerStyle.normal.textColor = GetPlayerTypeColor(type);
                EditorGUILayout.LabelField(type.ToString() + " Player", headerStyle);

                // Add a button to select this player type
                if (GUILayout.Button("Select " + type.ToString(), GUILayout.Height(25)))
                {
                    // Save current settings to the current player type
                    if (playerTypeSettings.ContainsKey(selectedPlayerType))
                    {
                        playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                            playTimePerDay, actionsPerMinute, averageXpPerAction);
                    }

                    // Switch to new player type
                    selectedPlayerType = type;

                    // Load settings for the new player type
                    UpdatePlayerTypeSettings();

                    // Normalize action frequencies to 100% for the new player type
                    NormalizeActionFrequencies();

                    RunSimulation();
                }

                EditorGUILayout.Space(5);

                EditorGUI.BeginChangeCheck();

                // Play time per day
                float newPlayTime = EditorGUILayout.FloatField("Play Time (hours/day)", settings.playTimePerDay);
                if (newPlayTime != settings.playTimePerDay && newPlayTime > 0)
                {
                    settings.playTimePerDay = newPlayTime;

                    // If this is the selected player type, update the current values
                    if (type == selectedPlayerType)
                    {
                        playTimePerDay = newPlayTime;
                    }
                }

                // Calculate and display XP per hour for this player type
                float playerXpPerHour = settings.actionsPerMinute * 60 * settings.averageXpPerAction;
                EditorGUILayout.LabelField($"XP Per Hour: {playerXpPerHour:N0}");

                // End the vertical column for this player type
                EditorGUILayout.EndVertical();
            }

            // End the horizontal layout
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            // Game-specific actions section
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Use genre-specific title for the actions section
            string actionsTitle = selectedGenre switch
            {
                GameGenre.RPG => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.MMORPG => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.ActionAdventure => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.FPS => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.MOBA => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.Strategy => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.Puzzle => $"Player Activities & Rewards ({selectedGenre})",
                GameGenre.Casual => $"Player Activities & Rewards ({selectedGenre})",
                _ => $"Game Actions for {selectedGenre}"
            };

            EditorGUILayout.LabelField(actionsTitle, EditorStyles.boldLabel);
            EditorGUILayout.Space(5);

            // Get the current player type settings
            if (playerTypeSettings.TryGetValue(selectedPlayerType, out PlayerTypeSettings currentSettings))
            {
                // Get actions for the selected genre
                if (genreActions.TryGetValue(selectedGenre, out string[] actions))
                {
                    // Display a table of actions with their frequency and XP values
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Action", EditorStyles.boldLabel, GUILayout.Width(150));
                    EditorGUILayout.LabelField("Frequency (%)", EditorStyles.boldLabel, GUILayout.Width(100));

                    // Use genre-specific label for XP values
                    string xpLabel = selectedGenre switch
                    {
                        GameGenre.RPG => "XP Reward",
                        GameGenre.MMORPG => "XP Reward",
                        GameGenre.ActionAdventure => "XP Reward",
                        GameGenre.FPS => "XP Reward",
                        GameGenre.MOBA => "XP Reward",
                        GameGenre.Strategy => "XP Reward",
                        GameGenre.Puzzle => "XP Reward",
                        GameGenre.Casual => "XP Reward",
                        _ => "XP Value"
                    };

                    EditorGUILayout.LabelField(xpLabel, EditorStyles.boldLabel, GUILayout.Width(100));
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.Space(2);

                    // Draw a separator line
                    Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                    EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                    EditorGUILayout.Space(2);

                    float totalFrequency = 0;
                    float totalXpPerAction = 0;

                    // For each action, display its settings
                    foreach (string action in actions)
                    {
                        // Ensure the action exists in the dictionaries
                        if (!currentSettings.actionFrequencies.ContainsKey(action))
                        {
                            // Set default values based on player type and action type
                            float defaultFrequency;
                            float defaultXp;

                            // Assign XP values based on action type
                            if (action.Contains("Boss") || action.Contains("Raid") || action.Contains("Tournament") || action.Contains("Campaign"))
                            {
                                // Boss/Raid activities are rare but high value
                                defaultFrequency = 5f;
                                defaultXp = selectedPlayerType == PlayerType.Casual ? 30f : (selectedPlayerType == PlayerType.Regular ? 45f : 60f);
                            }
                            else if (action.Contains("Quest") || action.Contains("Mission") || action.Contains("Dungeon") || action.Contains("Match"))
                            {
                                // Quests/Missions are common and medium value
                                defaultFrequency = 30f;
                                defaultXp = selectedPlayerType == PlayerType.Casual ? 15f : (selectedPlayerType == PlayerType.Regular ? 20f : 30f);
                            }
                            else if (action.Contains("Complete"))
                            {
                                // Completion activities are medium frequency
                                defaultFrequency = 20f;
                                defaultXp = selectedPlayerType == PlayerType.Casual ? 10f : (selectedPlayerType == PlayerType.Regular ? 15f : 25f);
                            }
                            else
                            {
                                // Regular activities are most common but lower value
                                defaultFrequency = 45f / (actions.Length - 1); // Distribute remaining percentage among other activities
                                defaultXp = selectedPlayerType == PlayerType.Casual ? 5f : (selectedPlayerType == PlayerType.Regular ? 10f : 15f);
                            }

                            currentSettings.actionFrequencies[action] = defaultFrequency;
                            currentSettings.actionXpValues[action] = defaultXp;
                        }

                        EditorGUILayout.BeginHorizontal();

                        // Action name
                        EditorGUILayout.LabelField(action, GUILayout.Width(150));

                        // Frequency slider
                        float newFrequency = EditorGUILayout.Slider(currentSettings.actionFrequencies[action], 0, 100, GUILayout.Width(100));
                        if (newFrequency != currentSettings.actionFrequencies[action])
                        {
                            currentSettings.actionFrequencies[action] = newFrequency;
                        }

                        // XP value field
                        float newXpValue = EditorGUILayout.FloatField(currentSettings.actionXpValues[action], GUILayout.Width(100));
                        if (newXpValue != currentSettings.actionXpValues[action] && newXpValue >= 0)
                        {
                            currentSettings.actionXpValues[action] = newXpValue;
                        }

                        EditorGUILayout.EndHorizontal();

                        totalFrequency += currentSettings.actionFrequencies[action];
                        totalXpPerAction += currentSettings.actionFrequencies[action] * currentSettings.actionXpValues[action] / 100f;
                    }

                    // Draw a separator line
                    EditorGUILayout.Space(2);
                    separatorRect = EditorGUILayout.GetControlRect(false, 1);
                    EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
                    EditorGUILayout.Space(2);

                    // Display totals
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Total", EditorStyles.boldLabel, GUILayout.Width(150));

                    // Show warning if total frequency is not 100%
                    if (Mathf.Abs(totalFrequency - 100f) > 0.01f)
                    {
                        GUIStyle warningStyle = new GUIStyle(EditorStyles.boldLabel);
                        warningStyle.normal.textColor = Color.yellow;
                        EditorGUILayout.LabelField($"{totalFrequency:F1}%", warningStyle, GUILayout.Width(100));
                    }
                    else
                    {
                        EditorGUILayout.LabelField($"{totalFrequency:F1}%", EditorStyles.boldLabel, GUILayout.Width(100));
                    }

                    EditorGUILayout.LabelField($"{totalXpPerAction:F1}", EditorStyles.boldLabel, GUILayout.Width(100));
                    EditorGUILayout.EndHorizontal();

                    // Update the average XP per action based on the weighted values
                    if (totalFrequency > 0)
                    {
                        // Only update if the value has changed significantly
                        if (Mathf.Abs(totalXpPerAction - currentSettings.averageXpPerAction) > 0.01f)
                        {
                            currentSettings.averageXpPerAction = totalXpPerAction;

                            // Always update the current value since we're working with the current player type
                            averageXpPerAction = totalXpPerAction;
                            RunSimulation();
                        }
                    }

                    // Display a note about frequency distribution
                    if (Mathf.Abs(totalFrequency - 100f) > 0.01f)
                    {
                        EditorGUILayout.HelpBox($"Action frequencies should sum to 100%. Current total: {totalFrequency:F1}%", MessageType.Warning);
                    }

                    // Display a button to normalize frequencies
                    if (Mathf.Abs(totalFrequency - 100f) > 0.01f && GUILayout.Button("Normalize Frequencies to 100%"))
                    {
                        // Normalize all frequencies to sum to 100%
                        float factor = 100f / totalFrequency;
                        foreach (string action in actions)
                        {
                            if (currentSettings.actionFrequencies.ContainsKey(action))
                            {
                                currentSettings.actionFrequencies[action] *= factor;
                            }
                        }
                    }

                    // Display the activity rate in a more genre-appropriate way
                    EditorGUILayout.Space(5);
                    EditorGUILayout.BeginHorizontal();

                    // Use genre-specific label for the activity rate
                    string activityLabel = selectedGenre switch
                    {
                        GameGenre.RPG => "Quests/Activities Per Hour:",
                        GameGenre.MMORPG => "Activities Per Hour:",
                        GameGenre.ActionAdventure => "Missions/Activities Per Hour:",
                        GameGenre.FPS => "Matches/Activities Per Hour:",
                        GameGenre.MOBA => "Matches Per Hour:",
                        GameGenre.Strategy => "Battles/Missions Per Hour:",
                        GameGenre.Puzzle => "Levels Completed Per Hour:",
                        GameGenre.Casual => "Levels/Activities Per Hour:",
                        _ => "Activities Per Hour:"
                    };

                    EditorGUILayout.LabelField(activityLabel, GUILayout.Width(180));

                    // Convert actions per minute to actions per hour for more intuitive values
                    float actionsPerHour = currentSettings.actionsPerMinute * 60;
                    float newActionsPerHour = EditorGUILayout.FloatField(actionsPerHour, GUILayout.Width(100));

                    if (Mathf.Abs(newActionsPerHour - actionsPerHour) > 0.01f && newActionsPerHour > 0)
                    {
                        // Convert back to actions per minute for internal calculations
                        float newActionsPerMinute = newActionsPerHour / 60f;
                        currentSettings.actionsPerMinute = newActionsPerMinute;

                        // Always update the current value since we're working with the current player type
                        actionsPerMinute = newActionsPerMinute;
                        RunSimulation();
                    }

                    EditorGUILayout.EndHorizontal();
                }
                else
                {
                    EditorGUILayout.HelpBox($"No actions defined for {selectedGenre} genre.", MessageType.Warning);
                }
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Simulation Controls in its own section
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Simulation Controls", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Display active player type info
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Active Player Type:", GUILayout.Width(120));
            GUIStyle simulationTypeStyle = new GUIStyle(EditorStyles.boldLabel);
            simulationTypeStyle.normal.textColor = GetPlayerTypeColor(selectedPlayerType);
            EditorGUILayout.LabelField(selectedPlayerType.ToString(), simulationTypeStyle);
            EditorGUILayout.EndHorizontal();

            // Display calculated XP per hour for active player type
            float xpPerHour = actionsPerMinute * 60 * averageXpPerAction;
            EditorGUILayout.LabelField($"Active XP Per Hour: {xpPerHour:N0}");
            EditorGUILayout.Space(10);

            // Run simulation button
            if (GUILayout.Button("Run Simulation", GUILayout.Height(30)))
            {
                CalculateExperienceRequirements();
                RunSimulation();
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            #endregion Algorithm and Player Settings

            #region Visualization and Results



            // Algorithm Graphs
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Algorithm Graphs", subHeaderStyle);
            EditorGUILayout.Space(5);

            if (selectedAlgorithm != null && experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Draw algorithm graphs
                DrawAlgorithmGraphs();
            }
            else
            {
                EditorGUILayout.HelpBox("No algorithm selected or no experience requirements calculated.", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Player Journey Timeline
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Player Journey Timeline", subHeaderStyle);
            EditorGUILayout.Space(5);

            if (simulationResults != null && simulationResults.Count > 0)
            {
                // Add horizontal padding with EditorGUILayout.BeginHorizontal and flexible space
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace(); // Add flexible space on the left

                // Calculate available width for the timeline
                float availableWidth = EditorGUIUtility.currentViewWidth - 80; // Subtract some padding

                // Create a vertical layout for the timeline
                EditorGUILayout.BeginVertical(GUILayout.Width(availableWidth));

                // Draw timeline visualization
                DrawTimelineVisualization();

                EditorGUILayout.EndVertical();

                GUILayout.FlexibleSpace(); // Add flexible space on the right
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(10);

                // Draw table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", EditorStyles.boldLabel, GUILayout.Width(50));
                EditorGUILayout.LabelField("XP Required", EditorStyles.boldLabel, GUILayout.Width(100));
                EditorGUILayout.LabelField("Hours", EditorStyles.boldLabel, GUILayout.Width(70));
                EditorGUILayout.LabelField("Days", EditorStyles.boldLabel, GUILayout.Width(70));
                EditorGUILayout.LabelField("Total Days", EditorStyles.boldLabel, GUILayout.Width(80));
                EditorGUILayout.LabelField("Est. Date", EditorStyles.boldLabel, GUILayout.Width(100));
                EditorGUILayout.EndHorizontal();

                // Draw table rows
                for (int i = 0; i < simulationResults.Count; i++)
                {
                    var result = simulationResults[i];

                    // Skip levels beyond maxLevel
                    if (result.Level > maxLevel)
                        continue;

                    // For starting level, show the initial experience but no time
                    if (i == 0)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField(result.Level.ToString(), EditorStyles.boldLabel, GUILayout.Width(50));
                        EditorGUILayout.LabelField(result.ExperienceRequired.ToString("N0"), EditorStyles.boldLabel, GUILayout.Width(100));
                        EditorGUILayout.LabelField("-", EditorStyles.boldLabel, GUILayout.Width(70));
                        EditorGUILayout.LabelField("-", EditorStyles.boldLabel, GUILayout.Width(70));
                        EditorGUILayout.LabelField("-", EditorStyles.boldLabel, GUILayout.Width(80));
                        EditorGUILayout.LabelField("-", EditorStyles.boldLabel, GUILayout.Width(100));
                        EditorGUILayout.EndHorizontal();

                        // Add separator after starting level
                        EditorGUILayout.Space(5);
                        Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                        EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
                        EditorGUILayout.Space(5);

                        continue;
                    }

                    // Highlight milestone levels (every 5 levels)
                    bool isMilestone = result.Level % 5 == 0;
                    bool isMaxLevel = result.Level == maxLevel;
                    GUIStyle rowStyle = (isMilestone || isMaxLevel) ? EditorStyles.boldLabel : EditorStyles.label;

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(result.Level.ToString(), rowStyle, GUILayout.Width(50));
                    EditorGUILayout.LabelField(result.ExperienceRequired.ToString("N0"), rowStyle, GUILayout.Width(100));
                    EditorGUILayout.LabelField(result.HoursToLevel.ToString("F1"), rowStyle, GUILayout.Width(70));
                    EditorGUILayout.LabelField(result.DaysToLevel.ToString("F1"), rowStyle, GUILayout.Width(70));
                    EditorGUILayout.LabelField(result.TotalDays.ToString("F1"), rowStyle, GUILayout.Width(80));
                    // Format the estimated date
                    string dateText = result.Level == startingLevel ? "Now" :
                                     (result.EstimatedDate.Year >= 9999 ? "Far Future" :
                                     result.EstimatedDate.ToString("MM/dd/yyyy"));
                    EditorGUILayout.LabelField(dateText, rowStyle, GUILayout.Width(100));
                    EditorGUILayout.EndHorizontal();

                    // Add separator after milestone levels or max level
                    if (isMilestone || isMaxLevel)
                    {
                        EditorGUILayout.Space(5);
                        Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                        EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
                        EditorGUILayout.Space(5);
                    }
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No simulation results available. Please run the simulation.", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(5);

            // Experience Rewards Preview
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Experience Rewards Preview", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Draw experience rewards preview
            DrawExperienceRewardsPreview();

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(5);

            // Player Type Comparison
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Player Type Comparison", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Draw player type comparison
            DrawPlayerTypeComparison();

            EditorGUILayout.EndVertical();

            // Add extra padding at the bottom to ensure values don't go off screen
            EditorGUILayout.Space(20);

            #endregion Visualization and Results

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// Calculates the experience reward for a given category and level requirement
        /// </summary>
        /// <param name="categoryPercentage">The percentage of the level requirement</param>
        /// <param name="expRequired">The experience required for the level</param>
        /// <returns>The calculated experience reward (minimum 1)</returns>
        private int CalculateExperienceReward(float categoryPercentage, int expRequired)
        {
            // Calculate the raw reward
            float rawReward = expRequired * categoryPercentage;

            // Ensure the reward is at least 1, even for very small percentages
            // This ensures that even 0.001% of a small experience requirement still gives at least 1 XP
            return Mathf.Max(1, Mathf.RoundToInt(rawReward));
        }

        private void DrawTimelineVisualization()
        {
            if (simulationResults == null || simulationResults.Count <= 1)
            {
                EditorGUILayout.HelpBox("Not enough data to display player journey. Run the simulation first.", MessageType.Info);
                return;
            }

            // Find the last valid result (within maxLevel)
            SimulationResult lastValidResult = simulationResults.LastOrDefault(r => r.Level <= maxLevel);
            if (lastValidResult == null)
            {
                EditorGUILayout.HelpBox("No valid simulation results found within the max level range.", MessageType.Warning);
                return;
            }

            // Calculate total days for the full journey
            float totalDays = lastValidResult.TotalDays;

            // Ensure we have a reasonable number of days (at least 1)
            if (totalDays <= 0)
            {
                totalDays = 1;
                EditorGUILayout.HelpBox("Journey time is very short or zero. Check your XP settings.", MessageType.Info);
            }
            else if (totalDays > 3650) // More than 10 years
            {
                EditorGUILayout.HelpBox("Journey time is extremely long (" + FormatTimeSpan(totalDays) + "). Consider adjusting your settings for a more reasonable progression.", MessageType.Warning);
            }

            // Set up the timeline rect - make it taller for better visibility
            Rect timelineRect = GUILayoutUtility.GetRect(0, 90, GUILayout.ExpandWidth(true));
            EditorGUI.DrawRect(timelineRect, new Color(0.2f, 0.2f, 0.2f, 1.0f));

            // Draw timeline axis
            Handles.color = Color.white;
            Handles.DrawLine(
                new Vector3(timelineRect.x, timelineRect.y + timelineRect.height - 20),
                new Vector3(timelineRect.x + timelineRect.width, timelineRect.y + timelineRect.height - 20)
            );

            // Calculate a reasonable number of time markers based on total days
            int numMarkers = Mathf.Min(10, Mathf.Max(4, Mathf.FloorToInt(totalDays / 10) + 1));

            // Draw time markers
            for (int i = 0; i <= numMarkers; i++)
            {
                float x = timelineRect.x + (timelineRect.width * i / numMarkers);
                float days = totalDays * i / numMarkers;

                // Draw marker line
                Handles.DrawLine(
                    new Vector3(x, timelineRect.y + timelineRect.height - 20),
                    new Vector3(x, timelineRect.y + timelineRect.height - 15)
                );

                // Format day label based on magnitude
                string dayLabel = FormatTimeSpan(days);

                // Draw day label
                GUI.Label(
                    new Rect(x - 20, timelineRect.y + timelineRect.height - 15, 40, 15),
                    dayLabel,
                    new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter }
                );
            }

            // Create a list of levels to display (key milestones)
            List<SimulationResult> milestonesToShow = new List<SimulationResult>();

            // Always add starting level
            milestonesToShow.Add(simulationResults[0]);

            // Determine milestone frequency based on max level
            int milestoneFrequency = 5;
            if (maxLevel > 100)
                milestoneFrequency = 20;
            else if (maxLevel > 50)
                milestoneFrequency = 10;

            // Add milestone levels and max level
            for (int i = 1; i < simulationResults.Count; i++)
            {
                var result = simulationResults[i];

                // Skip levels beyond maxLevel
                if (result.Level > maxLevel)
                    break;

                // Only add milestones and the max level
                if (result.Level % milestoneFrequency == 0 || result.Level == maxLevel)
                {
                    milestonesToShow.Add(result);
                }
            }

            // Make sure we always include the max level if it's not already included
            SimulationResult maxLevelResult = simulationResults.LastOrDefault(r => r.Level <= maxLevel);
            if (maxLevelResult != null && !milestonesToShow.Contains(maxLevelResult))
            {
                milestonesToShow.Add(maxLevelResult);
            }

            // If we have too many milestones, reduce them intelligently
            if (milestonesToShow.Count > 8)
            {
                // Create a new list with smart selection
                List<SimulationResult> reducedMilestones = new List<SimulationResult>();

                // Always include starting level
                reducedMilestones.Add(milestonesToShow[0]);

                // Always include max level
                SimulationResult finalLevel = milestonesToShow[milestonesToShow.Count - 1];

                // Select milestones that are evenly distributed by time, not just by index
                // This ensures better visual distribution on the timeline
                int numMilestonesToShow = 6; // We want about 6 milestones between start and end

                for (int i = 1; i <= numMilestonesToShow; i++)
                {
                    // Calculate the ideal time position for this milestone
                    float targetDays = (finalLevel.TotalDays * i) / (numMilestonesToShow + 1);

                    // Find the milestone closest to this time position
                    SimulationResult closestMilestone = null;
                    float closestDistance = float.MaxValue;

                    foreach (var milestone in milestonesToShow)
                    {
                        // Skip start and end levels
                        if (milestone == milestonesToShow[0] || milestone == finalLevel)
                            continue;

                        float distance = Mathf.Abs(milestone.TotalDays - targetDays);
                        if (distance < closestDistance)
                        {
                            closestDistance = distance;
                            closestMilestone = milestone;
                        }
                    }

                    // Add the closest milestone if we found one
                    if (closestMilestone != null && !reducedMilestones.Contains(closestMilestone))
                    {
                        reducedMilestones.Add(closestMilestone);
                    }
                }

                // Add the final level
                if (!reducedMilestones.Contains(finalLevel))
                {
                    reducedMilestones.Add(finalLevel);
                }

                // Sort by level to ensure proper order
                milestonesToShow = reducedMilestones.OrderBy(r => r.Level).ToList();
            }

            // Ensure we have at least 3 milestones for a meaningful timeline
            if (milestonesToShow.Count < 3 && simulationResults.Count > 2)
            {
                // Add more levels if we don't have enough milestones
                int step = Mathf.Max(1, simulationResults.Count / 4);
                for (int i = step; i < simulationResults.Count && milestonesToShow.Count < 4; i += step)
                {
                    var result = simulationResults[i];

                    // Skip levels beyond maxLevel
                    if (result.Level > maxLevel)
                        continue;

                    if (!milestonesToShow.Contains(result))
                    {
                        milestonesToShow.Add(result);
                    }
                }

                // Sort by level
                milestonesToShow = milestonesToShow.OrderBy(r => r.Level).ToList();
            }

            // Check for overlapping milestones and adjust if needed
            List<float> milestonePositions = new List<float>();
            Dictionary<int, float> levelPositions = new Dictionary<int, float>();

            // First pass: calculate all positions
            foreach (var result in milestonesToShow)
            {
                float ratio = totalDays > 0 ? result.TotalDays / totalDays : 0;
                ratio = Mathf.Clamp01(ratio);
                float x = timelineRect.x + (timelineRect.width * ratio);
                milestonePositions.Add(x);
                levelPositions[result.Level] = x;
            }

            // Second pass: detect and resolve overlaps
            const float MIN_SPACING = 35f; // Minimum pixels between milestone labels
            bool hasOverlaps = true;
            int maxIterations = 5; // Prevent infinite loops
            int iteration = 0;

            while (hasOverlaps && iteration < maxIterations)
            {
                hasOverlaps = false;
                iteration++;

                // Check each pair of adjacent milestones
                for (int i = 0; i < milestonesToShow.Count - 1; i++)
                {
                    float currentPos = levelPositions[milestonesToShow[i].Level];
                    float nextPos = levelPositions[milestonesToShow[i + 1].Level];

                    if (nextPos - currentPos < MIN_SPACING)
                    {
                        hasOverlaps = true;

                        // If we have too many milestones and they're overlapping, remove some
                        if (milestonesToShow.Count > 3 && iteration > 1)
                        {
                            // Remove the middle milestone to reduce density
                            int indexToRemove = i + 1;
                            if (indexToRemove < milestonesToShow.Count - 1) // Don't remove the last milestone
                            {
                                milestonesToShow.RemoveAt(indexToRemove);
                                break; // Restart the overlap detection
                            }
                        }
                    }
                }
            }

            // Draw level milestones
            foreach (var result in milestonesToShow)
            {
                // Skip the starting level if it has 0 days
                if (result.Level == startingLevel && result.TotalDays <= 0)
                    continue;

                // Calculate position (ensure it's within the timeline bounds)
                float ratio = totalDays > 0 ? result.TotalDays / totalDays : 0;
                ratio = Mathf.Clamp01(ratio); // Ensure ratio is between 0 and 1
                float x = timelineRect.x + (timelineRect.width * ratio);

                // Draw milestone marker
                Color markerColor = new Color(0.2f, 0.6f, 1.0f);
                EditorGUI.DrawRect(new Rect(x - 2, timelineRect.y + 10, 4, timelineRect.height - 30), markerColor);

                // Draw level label
                GUI.Label(
                    new Rect(x - 15, timelineRect.y, 30, 20),
                    "L" + result.Level,
                    new GUIStyle(EditorStyles.boldLabel) { alignment = TextAnchor.MiddleCenter }
                );

                // Draw time label below the marker
                string timeLabel = FormatTimeSpan(result.TotalDays);
                GUI.Label(
                    new Rect(x - 25, timelineRect.y + 25, 50, 15),
                    timeLabel,
                    new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter }
                );
            }

            // Add a legend explaining the timeline
            EditorGUILayout.Space(5);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Timeline shows estimated time to reach each level milestone based on current settings.",
                new GUIStyle(EditorStyles.miniLabel) { wordWrap = true });
            EditorGUILayout.EndHorizontal();
        }

        private string FormatTimeSpan(float days)
        {
            if (days >= 365)
            {
                float years = days / 365f;
                return years.ToString("F1") + "y";
            }
            else if (days >= 30)
            {
                float months = days / 30f;
                return months.ToString("F1") + "m";
            }
            else if (days >= 1)
            {
                return days.ToString("F0") + "d";
            }
            else
            {
                float hours = days * 24f;
                if (hours >= 1)
                {
                    return hours.ToString("F0") + "h";
                }
                else
                {
                    float minutes = hours * 60f;
                    return minutes.ToString("F0") + "m";
                }
            }
        }

        private void DrawAlgorithmGraphs()
        {
            if (selectedAlgorithm == null || experienceRequirements == null || experienceRequirements.Count == 0)
                return;

            try
            {
                // Create a LevelingCurvePreview instance if it doesn't exist
                if (curvePreview == null)
                {
                    curvePreview = new LevelingCurvePreview();

                    // Configure the graph settings
                    curvePreview.GraphSize = new Vector2(0.9f, 0.42f); // Increased by 20% as per user preference (from 0.35f to 0.42f)
                    curvePreview.GraphPadding = 40; // Increase padding inside the graph
                }

                // Force the algorithm to recalculate its values for the graph
                if (selectedAlgorithm is LevelingAlgorithmBase scriptableAlgorithm)
                {
                    // Clear cached values to ensure fresh calculation
                    scriptableAlgorithm.ClearCachedValues();

                    // Pre-calculate points with current settings
                    scriptableAlgorithm.PreCalculatePoints(
                        startingExperience,
                        startingLevel,
                        maxLevel,
                        levelUpMultiplier
                    );

                    // Log the algorithm name and first few raw formula values for debugging
                    var rawValues = scriptableAlgorithm.CalculateRawFormulaCurve(startingLevel, maxLevel, levelUpMultiplier);
                    if (rawValues.Count > 0)
                    {
                        string debugValues = string.Join(", ", rawValues.Take(5));
                        Debug.Log($"SimulationTabManager: Algorithm {scriptableAlgorithm.Name} - First 5 raw formula values: {debugValues}");
                    }
                }

                // Add horizontal padding with EditorGUILayout.BeginHorizontal and flexible space
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace(); // Add flexible space on the left

                // Calculate available width for the graph
                float availableWidth = EditorGUIUtility.currentViewWidth - 80; // Subtract some padding

                // Create a vertical layout for the graphs
                EditorGUILayout.BeginVertical(GUILayout.Width(availableWidth));

                // Draw the algorithm graphs
                curvePreview.DrawCurvePreview(
                    selectedAlgorithm,
                    levelUpMultiplier,
                    null, // No comparison algorithm
                    startingExperience,
                    startingLevel,
                    maxLevel,
                    availableWidth // Pass the available width to the graph
                );

                EditorGUILayout.EndVertical();

                GUILayout.FlexibleSpace(); // Add flexible space on the right
                EditorGUILayout.EndHorizontal();
            }
            catch (System.Exception ex)
            {
                EditorGUILayout.HelpBox($"Error drawing algorithm graphs: {ex.Message}", MessageType.Error);
            }
        }

        private void DrawPlayerTypeComparison()
        {
            // Store current player type settings
            PlayerType originalType = selectedPlayerType;
            float originalPlayTime = playTimePerDay;
            float originalActions = actionsPerMinute;
            float originalXpPerAction = averageXpPerAction;

            // Calculate for each player type
            Dictionary<PlayerType, float> daysToMaxLevel = new Dictionary<PlayerType, float>();

            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                // Get settings for this player type
                if (playerTypeSettings.TryGetValue(type, out PlayerTypeSettings settings))
                {
                    // Calculate days to max level based on player type settings
                    float playerXpPerHour = settings.actionsPerMinute * 60 * settings.averageXpPerAction;

                    // Calculate total XP needed to reach max level
                    int totalXpNeeded = 0;
                    for (int i = 0; i < experienceRequirements.Count; i++)
                    {
                        int level = startingLevel + i + 1;
                        if (level > maxLevel)
                            break;

                        totalXpNeeded += experienceRequirements[i];
                    }

                    // Calculate total hours and days needed
                    float totalHoursNeeded = playerXpPerHour > 0 ? totalXpNeeded / playerXpPerHour : 0;
                    float playerDaysToMaxLevel = settings.playTimePerDay > 0 ? totalHoursNeeded / settings.playTimePerDay : 0;

                    // Sanity check - cap at reasonable maximum (10 years)
                    const float MAX_REASONABLE_DAYS = 365 * 10;
                    if (playerDaysToMaxLevel > MAX_REASONABLE_DAYS)
                    {
                        playerDaysToMaxLevel = MAX_REASONABLE_DAYS;
                    }

                    // Store the result for this player type
                    daysToMaxLevel[type] = playerDaysToMaxLevel;
                }
            }

            // Restore original settings
            selectedPlayerType = originalType;
            playTimePerDay = originalPlayTime;
            actionsPerMinute = originalActions;
            averageXpPerAction = originalXpPerAction;
            RunSimulation();

            // Check if we have data to display
            if (daysToMaxLevel.Count == 0)
            {
                EditorGUILayout.HelpBox("No player type data available. Please run the simulation.", MessageType.Info);
                return;
            }

            // Add horizontal padding with EditorGUILayout.BeginHorizontal and flexible space
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Add flexible space on the left

            // Calculate available width for the chart
            float availableWidth = EditorGUIUtility.currentViewWidth - 80; // Subtract some padding

            // Create a vertical layout for the chart
            EditorGUILayout.BeginVertical(GUILayout.Width(availableWidth));

            // Draw comparison chart
            Rect chartRect = GUILayoutUtility.GetRect(0, 100, GUILayout.Width(availableWidth));
            EditorGUI.DrawRect(chartRect, new Color(0.2f, 0.2f, 0.2f, 1.0f));

            // Find max days for scaling
            float maxDays = 0;
            foreach (var days in daysToMaxLevel.Values)
            {
                maxDays = Mathf.Max(maxDays, days);
            }

            // Ensure we have a non-zero max for scaling
            if (maxDays <= 0)
            {
                maxDays = 1;
            }

            // Draw bars - adjust height and spacing for the side panel
            float barHeight = 20;
            float spacing = 10;
            float y = chartRect.y + spacing;

            // Check if all player types have the same value
            bool allSameValue = daysToMaxLevel.Values.Distinct().Count() == 1;

            foreach (var kvp in daysToMaxLevel)
            {
                // Draw label
                EditorGUI.LabelField(
                    new Rect(chartRect.x, y, 80, barHeight),
                    kvp.Key.ToString(),
                    EditorStyles.boldLabel
                );

                // Draw bar - always use actual values for bar width
                float barWidth;
                float maxBarWidth = chartRect.width - 180; // Leave room for text

                // Calculate bar width based on actual days to max level
                barWidth = maxBarWidth * kvp.Value / maxDays;
                // Ensure bar doesn't get too long
                barWidth = Mathf.Min(barWidth, maxBarWidth);

                Color barColor = GetPlayerTypeColor(kvp.Key);
                EditorGUI.DrawRect(
                    new Rect(chartRect.x + 80, y, barWidth, barHeight),
                    barColor
                );

                // Calculate position for days text, ensuring it stays within the chart area
                float textX = chartRect.x + 85 + barWidth;
                float maxTextX = chartRect.x + chartRect.width - 100; // Leave room for text
                textX = Mathf.Min(textX, maxTextX);

                // Draw days text
                EditorGUI.LabelField(
                    new Rect(textX, y, 100, barHeight),
                    kvp.Value.ToString("F1") + " days",
                    EditorStyles.miniLabel
                );

                y += barHeight + spacing;
            }

            // Draw compact legend
            EditorGUILayout.Space(5);
            EditorGUILayout.BeginHorizontal(GUILayout.Width(availableWidth));
            GUILayout.FlexibleSpace(); // Center the legend

            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                EditorGUILayout.BeginHorizontal(GUILayout.Width(90));

                // Draw color box
                Rect colorRect = GUILayoutUtility.GetRect(12, 12, GUILayout.Width(12), GUILayout.Height(12));
                EditorGUI.DrawRect(colorRect, GetPlayerTypeColor(type));

                // Draw label with smaller font
                GUIStyle smallLabel = new GUIStyle(EditorStyles.miniLabel);
                EditorGUILayout.LabelField(type.ToString(), smallLabel);

                EditorGUILayout.EndHorizontal();
            }

            GUILayout.FlexibleSpace(); // Center the legend
            EditorGUILayout.EndHorizontal();

            // Add a note if all values are the same
            if (allSameValue)
            {
                EditorGUILayout.HelpBox("All player types reach max level in the same time. Adjust player type settings for more realistic comparisons.", MessageType.Info);
            }

            // Add a warning if any values are extremely high
            bool hasExtremeValues = false;
            foreach (var days in daysToMaxLevel.Values)
            {
                if (days > 365) // More than a year
                {
                    hasExtremeValues = true;
                    break;
                }
            }

            if (hasExtremeValues)
            {
                EditorGUILayout.HelpBox("Some player types take an extremely long time to reach max level. Consider adjusting your algorithm settings, XP rates, or max level.", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
            GUILayout.FlexibleSpace(); // Add flexible space on the right
            EditorGUILayout.EndHorizontal();
        }

        private void DrawExperienceRewardsPreview()
        {
            if (experienceRequirements == null || experienceRequirements.Count == 0)
                return;

            // Define level ranges for the preview
            int[][] levelRanges = new int[][]
            {
                new int[] { 1, 5 },
                new int[] { 6, 10 },
                new int[] { 11, 15 },
                new int[] { 16, 20 },
                new int[] { 21, 25 },
                new int[] { 26, 30 }
            };

            // Draw table header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Reward Category", EditorStyles.boldLabel, GUILayout.Width(150));

            // Draw level range headers
            foreach (var range in levelRanges)
            {
                if (range[1] <= maxLevel)
                {
                    EditorGUILayout.LabelField($"Levels {range[0]}-{range[1]}", EditorStyles.boldLabel, GUILayout.Width(100));
                }
            }

            EditorGUILayout.EndHorizontal();

            // Draw a separator line
            Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
            EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
            EditorGUILayout.Space(5);

            // Draw each reward category
            foreach (var category in rewardCategories)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(category.Key, GUILayout.Width(150));

                // Calculate and display rewards for each level range
                foreach (var range in levelRanges)
                {
                    if (range[1] <= maxLevel)
                    {
                        // Calculate average experience requirement for this range
                        int avgExpRequired = 0;
                        int count = 0;

                        for (int level = range[0]; level <= range[1]; level++)
                        {
                            // For level 1, use the starting experience
                            if (level == 1)
                            {
                                avgExpRequired += startingExperience;
                                count++;
                                continue;
                            }

                            // For other levels, use the experience requirements
                            int index = level - startingLevel - 1; // Adjust index since experienceRequirements starts at level 2
                            if (index >= 0 && index < experienceRequirements.Count)
                            {
                                avgExpRequired += experienceRequirements[index];
                                count++;
                            }
                        }

                        if (count > 0)
                        {
                            avgExpRequired /= count;
                            // Calculate reward with minimum of 1
                            int reward = CalculateExperienceReward(category.Value, avgExpRequired);
                            EditorGUILayout.LabelField(reward.ToString(), GUILayout.Width(100));
                        }
                        else
                        {
                            EditorGUILayout.LabelField("-", GUILayout.Width(100));
                        }
                    }
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add a compact note about how rewards are calculated
            EditorGUILayout.Space(5);
            EditorGUILayout.HelpBox("Rewards are calculated as a percentage of the level XP requirement. Customize in Experience Rewards tab.", MessageType.Info);
        }

        private Color GetPlayerTypeColor(PlayerType type) => type switch
        {
            PlayerType.Casual => new Color(0.2f, 0.6f, 1.0f), // Blue
            PlayerType.Regular => new Color(0.2f, 0.8f, 0.2f), // Green
            PlayerType.Hardcore => new Color(1.0f, 0.6f, 0.2f), // Orange
            _ => Color.gray
        };
        #endregion GUI Methods
    }
}
