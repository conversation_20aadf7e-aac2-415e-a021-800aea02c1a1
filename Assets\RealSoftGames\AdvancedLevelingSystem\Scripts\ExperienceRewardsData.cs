using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    // No enum needed for simplified linear-only scaling

    [CreateAssetMenu(fileName = "ExperienceRewardsData", menuName = "RealSoftGames/Leveling System/Experience Rewards Data")]
    public class ExperienceRewardsData : ScriptableObject
    {
        [System.Serializable]
        public class RewardCategory
        {
            public string name;
            public float multiplier;
        }

        public List<RewardCategory> rewardCategories = new List<RewardCategory>();

        // Algorithm to use for experience calculations
        public LevelingAlgorithmBase selectedAlgorithm;

        // Legacy property for backward compatibility - removed in favor of ScriptableObject-based algorithms

        public float levelUpMultiplier = 1.1f;
        public int startingExperience = 250;
        public int startingLevel = 1;
        public int maxLevel = 50;
        public int levelRangeSize = 5;
        public bool showDetailedBreakdown = true;

        // Note: Reward scaling is now directly based on the algorithm and levelUpMultiplier
        // No separate scaling settings needed
    }
}
