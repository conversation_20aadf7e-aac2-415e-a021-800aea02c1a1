using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public static class Extensions
    {
        public static void DisplayFloatingText(this Transform target, int exp, Vector3? expTextOffset = null)
        {
            FloatingText expTextInstance = LevelingSystemObjectPool.Instance.ExpTextPool.Find(text => !text.gameObject.activeInHierarchy);

            if (expTextInstance == null)
                expTextInstance = LevelingSystemObjectPool.Instance.CreateNewExpTextInPool();

            Vector3 offset = expTextOffset ?? LevelingSystemObjectPool.Instance.DefaultExpTextOffset;

            expTextInstance.gameObject.SetActive(true);
            expTextInstance.transform.position = target.transform.position + expTextOffset ?? target.transform.position + LevelingSystemObjectPool.Instance.DefaultExpTextOffset;
            expTextInstance.DisplayFloatingText(target, $"{LevelingSystemObjectPool.Instance.Prefix}{exp}{LevelingSystemObjectPool.Instance.Suffix}", offset);
        }

        /// <summary>
        /// Add Experience to the leveling system
        /// </summary>
        /// <param name="experienceToAdd"></param>
        /// <returns></returns>
        public static int AddExperience(this Transform target, int experienceToAdd, bool DisplayFloatingText = true, Vector3? expTextOffset = null)
        {
            if (AdvancedLevelingSystem.Instance.CurrentLevel == AdvancedLevelingSystem.Instance.MaxLevel)
                return experienceToAdd; // Return remaining experience

            if (DisplayFloatingText)
                target.DisplayFloatingText(experienceToAdd, expTextOffset);

            AdvancedLevelingSystem.Instance.CurrentExperience += experienceToAdd;

            // Invoke OnExpModified event with current experience and required experience to level up
            AdvancedLevelingSystem.OnExpModified?.Invoke(AdvancedLevelingSystem.Instance.CurrentExperience, AdvancedLevelingSystem.Instance.RequiredExperience);

            // Return remaining experience if level-up is detected
            if (AdvancedLevelingSystem.Instance.CurrentExperience >= AdvancedLevelingSystem.Instance.RequiredExperience)
                return AdvancedLevelingSystem.Instance.CurrentExperience - AdvancedLevelingSystem.Instance.RequiredExperience;

            return 0; // No remaining experience
        }
        
        /// <summary>
        /// Calculates experience requirements for a given algorithm
        /// </summary>
        /// <param name="algorithm">The algorithm to use</param>
        /// <param name="levelUpMultiplier">Level up multiplier</param>
        /// <param name="startingExperience">Starting experience requirement</param>
        /// <param name="startingLevel">Starting level</param>
        /// <param name="maxLevel">Maximum level</param>
        /// <returns>List of experience requirements for each level</returns>
        public static List<int> CalculateExperienceRequirements(
            this ILevelingAlgorithm algorithm,
            float levelUpMultiplier,
            int startingExperience = 250,
            int startingLevel = 1,
            int maxLevel = 30)
        {
            // Use the algorithm's CalculateRequirementCurve method
            return algorithm.CalculateRequirementCurve(startingExperience, startingLevel, maxLevel, levelUpMultiplier);
        }
    }
}