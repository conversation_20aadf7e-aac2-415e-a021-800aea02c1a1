using UnityEngine;
using T<PERSON>ro;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class FloatingText : MonoBehaviour
    {
        [SerializeField] private TextMeshPro tmpText;
        private Transform followTarget;
        private Vector3 offset;

        private Camera cam;
        private float elapsedTime;
        private bool isActive = false;
        private Vector3 velocity = Vector3.zero;
        [SerializeField] private float smoothTime = 0.3f;
        [SerializeField] private float upwardsMovement = 0.5f;

        /// <summary>
        /// Time taken for the text to fade out
        /// </summary>
        [SerializeField] private float fadeDuration = 1.5f; // Adjust the duration as needed

        /// <summary>
        /// How fast the text floats up
        /// </summary>
        [SerializeField] private float scrollSpeed = 1f; // Adjust the movement speed as needed

        private void Awake()
        {
            cam = Camera.main;
        }

        private void FixedUpdate()
        {
            if (!isActive)
                return;

            if (elapsedTime < fadeDuration)
            {
                FadeOutText();
                FollowAndFloatUpwards();

                elapsedTime += Time.fixedDeltaTime;
            }
            else
            {
                offset = Vector3.zero;
                isActive = false;
                gameObject.SetActive(false);
            }
        }

        private void LateUpdate()
        {
            transform.LookAt(transform.position + cam.transform.rotation * Vector3.forward, cam.transform.rotation * Vector3.up);
        }

        private void FadeOutText()
        {
            tmpText.alpha = Mathf.Lerp(tmpText.alpha, 0f, elapsedTime / fadeDuration);
        }

        private void FollowAndFloatUpwards()
        {
            if (followTarget != null)
            {
                // Determine the target position for smooth following, keeping the original Y position
                float originalY = transform.position.y;
                Vector3 targetPositionWithOffset = followTarget.position + offset;
                Vector3 targetPosition = new Vector3(targetPositionWithOffset.x, originalY, targetPositionWithOffset.z);

                // Smoothly interpolate towards the target's position on the X and Z axes while maintaining the Y position
                Vector3 newPosition = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);

                // Now, separately handle the upwards movement by adding the upwardsMovement to the Y component
                newPosition.y += upwardsMovement * scrollSpeed * Time.deltaTime;

                // Apply the combined position
                transform.position = newPosition;
            }
            else
            {
                // If there's no target, just float upwards
                transform.position += new Vector3(0, upwardsMovement, 0) * scrollSpeed * Time.deltaTime;
            }
        }

        /// <summary>
        /// Activate the floating Text
        /// </summary>
        /// <param name="text"></param>
        public void DisplayFloatingText(Transform target, string text, Vector3? offset = null)
        {
            followTarget = target;
            tmpText.text = text;
            this.offset = offset ?? Vector3.zero;
            // Reset the timer
            elapsedTime = 0f;
            tmpText.alpha = 1.0f;
            isActive = true;
        }

        private void OnDisable()
        {
            followTarget = null;
        }
    }
}