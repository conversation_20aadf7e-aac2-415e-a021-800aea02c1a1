using UnityEngine;


namespace RealSoftGames.AdvancedLevelingSystem
{
    [CreateAssetMenu(fileName = "LevelingSystemData", menuName = "RealSoftGames/Leveling System/Leveling System Data")]
    public class LevelingSystemData : ScriptableObject
    {
        [Tooltip("The max level the player can reach")]
        public int maxLevel = 30;

        [Toolt<PERSON>("The required experience for the first level")]
        public int initialRequiredExperience = 250;

        [Tooltip("The multiplier applied to the required experience for each level")]
        [Range(0f, 5f)]
        public float levelUpMultiplier = 1.1f;

        [Tooltip("Select the leveling algorithm to use")]
        public LevelingAlgorithmBase levelingAlgorithm;

        [Tooltip("Audio clip to play when leveling up (leave empty to disable)")]
        public AudioClip levelUpClip;
    }
}
