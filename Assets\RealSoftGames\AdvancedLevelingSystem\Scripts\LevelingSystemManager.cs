﻿using System;
using TMPro;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class LevelingSystemManager : MonoBehaviour
    {
        /// <summary>
        /// Display the Leveling System Text Style as (Numeric)124/500 or (Percent)30%
        /// </summary>
        public enum DisplayType
        {
            None,
            Numeric,
            Percent
        }

        /// <summary>
        /// The Exp bar image to display the Exp Bar
        /// </summary>
        [SerializeField] private SlicedFilledImage fillImage;

        [SerializeField] private GameObject expBar;

        /// <summary>
        /// The Exp Text Component for displaying the Exp Text
        /// </summary>
        [SerializeField] private TMP_Text expText;

        /// <summary>
        /// The additional Text to display with the level for example - Level:
        /// </summary>
        [SerializeField] private string lvlText = "Level: ";

        /// <summary>
        /// The EXP Text for displaying the current players level
        /// </summary>
        [SerializeField] private TMP_Text levelText;

        /// <summary>
        /// The Display Type for the expText component
        /// </summary>
        [SerializeField] private DisplayType displayType;

        private void Awake()
        {
            AdvancedLevelingSystem.OnEXPUIUpdate += UpdateUI;
            AdvancedLevelingSystem.OnLevelUp += OnLevelUp;

            AdvancedLevelingSystem.OnMaxLevel = () =>
            {
                expText.gameObject.SetActive(false);
                expBar.SetActive(false);
            };
        }

        private void OnDestroy()
        {
            AdvancedLevelingSystem.OnEXPUIUpdate -= UpdateUI;
            AdvancedLevelingSystem.OnLevelUp -= OnLevelUp;
        }

        /// <summary>
        /// Example Code for how to update the player UI for the leveling system
        /// </summary>
        /// <param name="curEXP"></param>
        /// <param name="reqEXP"></param>
        private void UpdateUI(int curEXP, int reqEXP)
        {
            //Debug.Log($"CurEXP:{curEXP} ReqEXP:{reqEXP});
            float fillAmount = Mathf.Clamp01((float)curEXP / (float)reqEXP);
            fillImage.fillAmount = fillAmount;

            switch (displayType)
            {
                case DisplayType.None:
                    expText.text = "";
                    break;

                case DisplayType.Numeric:
                    expText.text = $"{curEXP}/{reqEXP}";
                    break;

                case DisplayType.Percent:
                    float percent = fillAmount * 100f;
                    expText.text = $"{Math.Round(percent, 2)}%";
                    break;
            }
        }

        /// <summary>
        /// Example code for updating the player ui for when the player levels up
        /// Include here custom logic for other factors for when the player levels up, e.g increase stats and gain new skills
        /// </summary>
        /// <param name="level"></param>
        private void OnLevelUp(int level)
        {
            //Debug.Log($"Level:{level}");
            levelText.text = $"{lvlText}{level}";
            fillImage.fillAmount = 0f;
        }
    }
}