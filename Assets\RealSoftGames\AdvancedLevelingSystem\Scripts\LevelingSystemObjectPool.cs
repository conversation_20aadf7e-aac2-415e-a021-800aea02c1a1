using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class LevelingSystemObjectPool : MonoBehaviour
    {
        public static LevelingSystemObjectPool Instance;
        private List<FloatingText> expTextPool = new List<FloatingText>();

        [SerializeField]
        private FloatingText expTextPrefab;

        public Vector3 DefaultExpTextOffset = new Vector3(0, 0.5f, 0); // default Offset from the object's position

        [SerializeField] private string prefix = "";
        [SerializeField] private string suffix = " EXP";

        public string Prefix { get => prefix; }
        public string Suffix { get => suffix; }
        public List<FloatingText> ExpTextPool { get => expTextPool; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject); // Optional: Keep the Instance alive across scenes
            }
            else if (Instance != this)
                Destroy(gameObject); // Ensure there's only one Instance in the scene

            InitializePool();
        }

        // Initializes the pool with a predefined number of objects
        private void InitializePool(int initialPoolSize = 5)
        {
            for (int i = 0; i < initialPoolSize; i++)
                CreateNewExpTextInPool();
        }

        // Creates a new TextMeshPro object and adds it to the pool
        public FloatingText CreateNewExpTextInPool()
        {
            var expTextInstance = Instantiate(expTextPrefab, transform);
            expTextInstance.gameObject.SetActive(false); // Start inactive
            expTextPool.Add(expTextInstance);
            return expTextInstance;
        }

        // Gets an inactive TextMeshPro object from the pool or creates a new one if all are active
        public void DisplayFloatingText(Transform target, int exp, Vector3? expTextOffset = null)
        {
            FloatingText expTextInstance = expTextPool.Find(text => !text.gameObject.activeInHierarchy);

            if (expTextInstance == null)
                expTextInstance = CreateNewExpTextInPool();

            expTextInstance.gameObject.SetActive(true);

            expTextInstance.DisplayFloatingText(target, $"{prefix}{exp}{suffix}");

            expTextInstance.transform.localPosition = expTextOffset ?? DefaultExpTextOffset;
        }
    }
}