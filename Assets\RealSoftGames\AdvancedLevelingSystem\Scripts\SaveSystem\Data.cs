using System;
using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    [Serializable]
    public class Data
    {
        [SerializeField] private List<KeyValue> keyValuePairs = new List<KeyValue>();

        public void Save<T>(string key, T value)
        {
            // Update or add the value for the specified key
            var item = keyValuePairs.Find(i => key == i.Key);

            if (item != null)
            {
                item.ValueType = typeof(T).FullName;
                item.Value = value.ToString();
            }
            else
            {
                keyValuePairs.Add(new KeyValue
                {
                    Key = key,
                    ValueType = typeof(T).FullName,
                    Value = value.ToString()
                });
            }
        }

        public T Get<T>(string key)
        {
            // Retrieve the value for the specified key
            KeyValue keyValue = keyValuePairs.Find(i => i.Key == key);

            if (keyValue.Key != null)
            {
                // Directly convert stored string to the type T
                return (T)Convert.ChangeType(keyValue.Value, typeof(T));
            }

            // If the key doesn't exist, return the default value for the specified type
            return default(T);
        }

        public bool ContainsKey(string key)
        {
            return keyValuePairs.Exists(kv => kv.Key == key);
        }
    }

    [Serializable]
    public class KeyValue
    {
        public string Key;
        public string ValueType; // Store the type information as a string
        public string Value; // Store the string representation of the value
    }
}