using System.IO;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public class FileSystem
    {
        private static string savePath = "Saves";

        private static string SavePath(string fileName)
        {
            string directoryPath = Application.dataPath + "/" + savePath;

            // Check if the directory exists, create it if not
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            return Path.Combine(directoryPath, fileName);
        }

        public static void SaveData<T>(string key, T data, string fileName)
        {
            // Load existing data
            Data existingData = LoadData(fileName);

            // Update or add the new data
            if (existingData == null)
            {
                existingData = new Data();
            }

            existingData.Save(key, data);

            // Convert the updated data to JSON format
            string json = JsonUtility.ToJson(existingData);

            // Save the JSON string to a file
            File.WriteAllText(SavePath(fileName), json);
        }

        public static Data LoadData(string fileName)
        {
            // Load the JSON string from a file
            string filePath = SavePath(fileName);
            if (File.Exists(filePath))
            {
                string json = File.ReadAllText(filePath);

                // If there is saved data, deserialize it into the specified type
                return JsonUtility.FromJson<Data>(json);
            }

            // If no saved data is found, return null
            return null;
        }
    }
}