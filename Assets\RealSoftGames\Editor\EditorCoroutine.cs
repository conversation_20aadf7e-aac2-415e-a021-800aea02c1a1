using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace RealSoftGames
{
    /// <summary>
    /// Simple implementation of coroutines for Unity Editor
    /// </summary>
    public class EditorCoroutine
    {
        private static List<EditorCoroutine> activeCoroutines = new List<EditorCoroutine>();
        private IEnumerator routine;
        private bool finished = false;

        private EditorCoroutine(IEnumerator routine)
        {
            this.routine = routine;
        }

        /// <summary>
        /// Starts a new editor coroutine
        /// </summary>
        /// <param name="routine">The coroutine to start</param>
        /// <returns>A reference to the coroutine</returns>
        public static EditorCoroutine Start(IEnumerator routine)
        {
            EditorCoroutine coroutine = new EditorCoroutine(routine);
            activeCoroutines.Add(coroutine);

            // Register for update if this is the first coroutine
            if (activeCoroutines.Count == 1)
            {
                EditorApplication.update += UpdateCoroutines;
            }

            return coroutine;
        }

        /// <summary>
        /// Stops all active editor coroutines
        /// </summary>
        public static void StopAll()
        {
            activeCoroutines.Clear();
            EditorApplication.update -= UpdateCoroutines;
        }

        /// <summary>
        /// Stops a specific editor coroutine
        /// </summary>
        /// <param name="coroutine">The coroutine to stop</param>
        public static void Stop(EditorCoroutine coroutine)
        {
            if (coroutine != null && activeCoroutines.Contains(coroutine))
            {
                coroutine.finished = true;
                activeCoroutines.Remove(coroutine);

                // Unregister from update if no more coroutines
                if (activeCoroutines.Count == 0)
                {
                    EditorApplication.update -= UpdateCoroutines;
                }
            }
        }

        private static void UpdateCoroutines()
        {
            // Process coroutines in reverse order to safely remove finished ones
            for (int i = activeCoroutines.Count - 1; i >= 0; i--)
            {
                EditorCoroutine coroutine = activeCoroutines[i];
                if (coroutine.finished)
                {
                    activeCoroutines.RemoveAt(i);
                    continue;
                }

                // Process the next step in the coroutine
                if (coroutine.routine.MoveNext())
                {
                    // Handle yield return values
                    object current = coroutine.routine.Current;
                    
                    // Handle WaitForSeconds
                    if (current is WaitForSeconds)
                    {
                        // Not supported in editor, just continue next frame
                    }
                    // Handle nested coroutines
                    else if (current is IEnumerator)
                    {
                        // Start a new coroutine with the nested routine
                        EditorCoroutine nested = Start((IEnumerator)current);
                        // Wait until the nested coroutine is finished
                        coroutine.routine = CombineCoroutines(nested, coroutine.routine);
                    }
                }
                else
                {
                    // Coroutine has finished
                    coroutine.finished = true;
                    activeCoroutines.RemoveAt(i);
                }
            }

            // Unregister from update if no more coroutines
            if (activeCoroutines.Count == 0)
            {
                EditorApplication.update -= UpdateCoroutines;
            }
        }

        private static IEnumerator CombineCoroutines(EditorCoroutine waitFor, IEnumerator next)
        {
            while (!waitFor.finished)
                yield return null;

            // Continue with the original coroutine
            while (next.MoveNext())
                yield return next.Current;
        }
    }
}
