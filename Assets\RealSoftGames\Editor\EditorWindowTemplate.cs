using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;

namespace RealSoftGames
{
    public class EditorWindowTemplate : EditorWindow
    {
        protected TaskManager taskManager;
        protected Vector2 NavigationScrollPosition, MainBodyScrollPosition;
        protected float padding = 5f;
        protected float NavigationPanelWidth = 200f;
        private static Rect navRect;
        private static Rect mainBodyRect;

        // Tab management
        protected List<ITabManager> tabManagers = new List<ITabManager>();
        protected int currentTabIndex = 0;
        protected Color selectedTabColor = new Color(0.3f, 0.5f, 0.85f);
        protected Color deselectedTabColor = Color.white;

        // Toolbar buttons
        protected float ToolbarHeight = 40f;
        protected List<ToolbarButton> toolbarButtons;

        public static Rect NavRect => navRect;
        public static Rect MainBodyRect => mainBodyRect;

        public TaskManager TaskManager => taskManager;

        protected virtual GUILayoutOption[] NavBarWidthOptions
        {
            get => new GUILayoutOption[]
            {
                GUILayout.Width(NavigationPanelWidth - (padding * 10)),
                GUILayout.Height(30f)
            };
        }

        //[MenuItem("Tools/RealSoftGames/EditorWindowTemplate")]
        public static void ShowWindow()
        {
            GetWindow<EditorWindowTemplate>("RealSoft Games");
        }

        protected virtual void OnEnable()
        {
            if (taskManager == null) taskManager = new TaskManager();
            EditorApplication.update = null;
            EditorApplication.update += Update;

            // Initialize all tab managers
            foreach (var manager in tabManagers)
                manager.OnEnable();
        }

        protected virtual void Update()
        {
            Repaint();
            taskManager.ProcessTaskQueue();

            // Update all tab managers
            foreach (var manager in tabManagers)
                manager.Update();
        }

        protected virtual void OnGUI()
        {
            // Draw toolbar if we have buttons
            if (toolbarButtons != null && toolbarButtons.Count > 0)
            {
                GUILayout.BeginHorizontal(EditorStyles.toolbar, GUILayout.Height(ToolbarHeight));
                DrawToolbar(toolbarButtons);
                GUILayout.EndHorizontal();
            }

            // Start the horizontal layout that will contain the navigation and the main content.
            GUILayout.BeginHorizontal();
            DrawNavigationPanel();
            DrawMainBody();
            GUILayout.EndHorizontal();
        }

        protected virtual void OnDisable()
        {
            EditorApplication.update -= Update;

            // Disable all tab managers
            foreach (var manager in tabManagers)
                manager.OnDisable();
        }

        protected virtual void OnDestroy()
        {
            // Cleanup resources when the window is closed

            // Destroy all tab managers
            foreach (var manager in tabManagers)
                manager.OnDestroy();
        }

        #region Navigation Panel

        private void DrawNavigationPanel()
        {
            navRect = Utilities.EditorGUILayoutBeginContentZone(
                padding,
                padding,
                NavigationPanelWidth,
                position.height - (padding * 2),
                padding,
                ref NavigationScrollPosition);

            // Draw the navigation panel content (to be implemented by derived classes)
            EditorGUILayoutLeftPanel();

            // End the navigation panel area
            Utilities.EditorGUILayoutEndContentZone();
        }

        /// <summary>
        /// Navigation Panel Content - Override this in derived classes to provide custom navigation content
        /// </summary>
        /// <remarks>
        /// This method is called after the tab buttons are drawn. You can add additional navigation content here.
        /// </remarks>
        protected virtual void EditorGUILayoutLeftPanel()
        {
            // Draw tab buttons if we have tab managers
            if (tabManagers.Count > 0)
            {
                var buttonStyle = new GUIStyle(GUI.skin.button)
                {
                    fontStyle = FontStyle.Bold,
                    fontSize = 12,
                    fixedHeight = 35,
                    alignment = TextAnchor.MiddleCenter
                };

                // Draw buttons for each tab manager in the order they were registered
                for (int i = 0; i < tabManagers.Count; i++)
                {
                    var manager = tabManagers[i];
                    bool isSelected = currentTabIndex == i;

                    GUI.backgroundColor = isSelected ? selectedTabColor : deselectedTabColor;

                    if (GUILayout.Button(manager.TabName, buttonStyle, GUILayout.Width(NavigationPanelWidth - 20)))
                    {
                        // Set the current tab index directly
                        currentTabIndex = i;
                    }
                }

                GUI.backgroundColor = deselectedTabColor;

                // Add some space after the tabs
                GUILayout.Space(10);
            }
        }

        #endregion

        #region Main Body
        private void DrawMainBody()
        {
            // Calculate the main body rect and begin content zone

            mainBodyRect = Utilities.EditorGUILayoutBeginContentZone(
                NavigationPanelWidth + padding,
                padding + (toolbarButtons != null && toolbarButtons.Count > 0 ? ToolbarHeight : 0),
                position.width - (NavigationPanelWidth + padding) - padding,
                position.height - (padding * 2) - (toolbarButtons != null && toolbarButtons.Count > 0 ? ToolbarHeight : 0),
                padding,
                ref MainBodyScrollPosition);

            // If using tab managers, draw the current tab
            if (tabManagers.Count > 0 && currentTabIndex >= 0 && currentTabIndex < tabManagers.Count)
            {
                EditorGUI.BeginChangeCheck();
                tabManagers[currentTabIndex].OnGUI();
                if (EditorGUI.EndChangeCheck())
                {
                    // Force repaint to update the GUI
                    Repaint();
                }
            }
            else
            {
                // Draw the main body content (to be implemented by derived classes)
                EditorGUILayoutMainBody();
            }

            // End the content zone
            Utilities.EditorGUILayoutEndContentZone();
        }

        /// <summary>
        /// Main Body Content - Override this in derived classes to provide custom main body content
        /// </summary>
        protected virtual void EditorGUILayoutMainBody()
        {
            // Default implementation is empty
            // Derived classes should override this to provide custom content
        }

        #endregion

        #region Toolbar
        /// <summary>
        /// Represents a toolbar button with its label and action
        /// </summary>
        public class ToolbarButton
        {
            public string Label { get; set; }
            public Action OnClick { get; set; }
            public float Width { get; set; } = 100f;
            public float Height { get; set; } = 0f; // 0 means use default height
            public bool IsFlexible { get; set; } = false; // If true, button will expand to fill available space

            public ToolbarButton(string label, Action onClick, float width = 100f)
            {
                Label = label;
                OnClick = onClick;
                Width = width;
            }
        }

        /// <summary>
        /// Draws a toolbar with the specified buttons
        /// </summary>
        /// <param name="buttons">The buttons to display in the toolbar</param>
        protected void DrawToolbar(List<ToolbarButton> buttons = null)
        {
            // If no buttons, don't draw anything
            if (buttons == null)
                return;

            GUILayout.BeginHorizontal();

            var buttonStyle = new GUIStyle(EditorStyles.miniButton)
            {
                padding = new RectOffset(8, 8, 2, 2),
                margin = new RectOffset(2, 2, 0, 0),
                fixedHeight = 24
            };

            bool foundFlexibleButton = false;

            // Draw all buttons
            foreach (var button in buttons)
            {
                // If we encounter a flexible button, add the flexible space before the button
                if (button.IsFlexible && !foundFlexibleButton)
                {
                    GUILayout.FlexibleSpace();
                    foundFlexibleButton = true;
                }

                // Create layout options based on button properties
                var options = new List<GUILayoutOption>();

                if (!button.IsFlexible)
                    options.Add(GUILayout.Width(button.Width));
                else
                    options.Add(GUILayout.ExpandWidth(true));

                if (button.Height > 0)
                    options.Add(GUILayout.Height(button.Height));

                // Draw the button
                if (GUILayout.Button(button.Label, buttonStyle, options.ToArray()))
                {
                    button.OnClick?.Invoke();
                }
            }

            // If no flexible buttons were found, add flexible space at the end
            if (!foundFlexibleButton)
            {
                GUILayout.FlexibleSpace();
            }

            GUILayout.EndHorizontal();
        }

        #endregion

        #region Tab Management
        /// <summary>
        /// Registers a tab manager with the editor window
        /// </summary>
        /// <param name="manager">The tab manager to register</param>
        protected void RegisterTabManager(ITabManager manager)
        {
            if (!tabManagers.Contains(manager))
            {
                tabManagers.Add(manager);
                // Always call OnEnable when registering a tab manager
                // The tab manager will be properly initialized
                manager.OnEnable();
            }
        }

        /// <summary>
        /// Unregisters a tab manager from the editor window
        /// </summary>
        /// <param name="manager">The tab manager to unregister</param>
        protected void UnregisterTabManager(ITabManager manager)
        {
            if (tabManagers.Contains(manager))
            {
                // Always call OnDisable when unregistering a tab manager
                // This ensures proper cleanup
                manager.OnDisable();
                tabManagers.Remove(manager);
            }
        }

        /// <summary>
        /// Gets a tab manager of the specified type
        /// </summary>
        /// <typeparam name="T">The type of tab manager to get</typeparam>
        /// <returns>The tab manager of the specified type, or null if not found</returns>
        public T GetTabManager<T>() where T : class, ITabManager
        {
            foreach (var manager in tabManagers)
            {
                if (manager is T typedManager)
                {
                    return typedManager;
                }
            }
            return null;
        }
        #endregion
    }
}