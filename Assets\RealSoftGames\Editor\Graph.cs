using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using RealSoftGames.AdvancedLevelingSystem;

namespace RealSoftGames
{
    /// <summary>
    /// A generic graph drawing tool for visualizing data in the Unity Editor.
    /// Can be used to display any type of numerical data in various graph formats.
    /// </summary>
    public class Graph
    {
        #region Properties and Fields

        // Graph size and display parameters
        private Vector2 graphSize = new Vector2(1.0f, 0.3f); // Width and height as ratios of available space (0-1)
        private int graphPadding = 25; // Padding inside the graph in pixels (reduced from 30)
        private int bottomLabelSpace = 85; // Space for bottom labels in pixels (increased to accommodate X-axis label)
        private int leftLabelSpace = 50; // Space for left labels in pixels (reduced from 60)
        private int rightLabelSpace = 10; // Space for right margin in pixels (reduced from 20)
        private int horizontalMargin = 20; // Horizontal margin on each side of the graph (reduced from 40)
        private int topLabelSpace = 25; // Space for top labels in pixels (added for Y-axis label)
        private int maxPreviewPoints = 30; // Default value, can be changed

        /// <summary>
        /// If true, the graph will not be drawn (used to hide graphs)
        /// </summary>
        public bool SkipDrawing { get; set; } = false;

        /// <summary>
        /// Gets or sets the current algorithm being visualized
        /// </summary>
        public ILevelingAlgorithm CurrentAlgorithm { get; set; }

        // Grid and value tier settings
        private int horizontalGridLines = 5; // Number of horizontal grid lines
        private int verticalGridLines = 10; // Number of vertical grid lines

        /// <summary>
        /// Gets or sets the graph size as a ratio of available space (width, height)
        /// </summary>
        public Vector2 GraphSize
        {
            get { return graphSize; }
            set { graphSize = new Vector2(Mathf.Clamp01(value.x), Mathf.Clamp01(value.y)); }
        }

        /// <summary>
        /// Gets or sets the padding inside the graph in pixels
        /// </summary>
        public int GraphPadding
        {
            get { return graphPadding; }
            set { graphPadding = Mathf.Max(5, value); }
        }

        /// <summary>
        /// Gets or sets the space for bottom labels in pixels
        /// </summary>
        public int BottomLabelSpace
        {
            get { return bottomLabelSpace; }
            set { bottomLabelSpace = Mathf.Max(10, value); }
        }

        /// <summary>
        /// Gets or sets the space for left labels in pixels
        /// </summary>
        public int LeftLabelSpace
        {
            get { return leftLabelSpace; }
            set { leftLabelSpace = Mathf.Max(20, value); }
        }

        /// <summary>
        /// Gets or sets the horizontal margin on each side of the graph in pixels
        /// </summary>
        public int HorizontalMargin
        {
            get { return horizontalMargin; }
            set { horizontalMargin = Mathf.Max(0, value); }
        }

        /// <summary>
        /// Gets or sets the maximum number of points to preview in the graph
        /// By default, this will match the MaxLevel property
        /// </summary>
        public int MaxPreviewPoints
        {
            get { return maxPreviewPoints; }
            set { maxPreviewPoints = Mathf.Max(5, value); } // Ensure at least 5 points are shown
        }

        /// <summary>
        /// Gets or sets the number of horizontal grid lines
        /// </summary>
        public int HorizontalGridLines
        {
            get { return horizontalGridLines; }
            set { horizontalGridLines = Mathf.Clamp(value, 2, 20); } // Reasonable limits
        }

        /// <summary>
        /// Gets or sets the number of vertical grid lines
        /// </summary>
        public int VerticalGridLines
        {
            get { return verticalGridLines; }
            set { verticalGridLines = Mathf.Clamp(value, 2, 20); } // Reasonable limits
        }

        // Colors
        private Color graphBackgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f); // Darker background for better contrast
        private Color gridLineColor = new Color(0.35f, 0.35f, 0.35f, 1f); // Brighter grid lines
        private Color primaryCurveColor = new Color(0.0f, 0.9f, 1.0f, 1f); // Brighter cyan for main curve
        private Color secondaryCurveColor = new Color(1.0f, 0.4f, 0.4f, 1f); // Brighter red for comparison
        private Color labelColor = new Color(0.9f, 0.9f, 0.9f, 1f); // Brighter white for labels
        private Color legendBackgroundColor = new Color(0.1f, 0.1f, 0.1f, 0.9f); // Darker legend background

        /// <summary>
        /// Gets or sets the background color of the graph
        /// </summary>
        public Color GraphBackgroundColor
        {
            get { return graphBackgroundColor; }
            set { graphBackgroundColor = value; }
        }

        /// <summary>
        /// Gets or sets the color of the grid lines
        /// </summary>
        public Color GridLineColor
        {
            get { return gridLineColor; }
            set { gridLineColor = value; }
        }

        /// <summary>
        /// Gets or sets the color of the primary curve
        /// </summary>
        public Color PrimaryCurveColor
        {
            get { return primaryCurveColor; }
            set { primaryCurveColor = value; }
        }

        /// <summary>
        /// Gets or sets the color of the secondary curve
        /// </summary>
        public Color SecondaryCurveColor
        {
            get { return secondaryCurveColor; }
            set { secondaryCurveColor = value; }
        }

        /// <summary>
        /// Gets or sets the color of the labels
        /// </summary>
        public Color LabelColor
        {
            get { return labelColor; }
            set { labelColor = value; InitializeStyles(); }
        }

        private GUIStyle labelStyle;

        /// <summary>
        /// Gets whether graph zooming (logarithmic scaling) is enabled
        /// </summary>
        public bool EnableGraphZooming { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to show raw curve values instead of actual values
        /// </summary>
        public bool ShowRawCurveValues { get; set; } = false;

        /// <summary>
        /// Gets or sets the level up multiplier used in calculations
        /// If set to 0, the multiplier is turned off in the equations
        /// </summary>
        public float LevelUpMultiplier { get; set; } = 1.1f;

        /// <summary>
        /// Gets or sets the starting experience value
        /// </summary>
        public int StartingExperience { get; set; } = 250;

        /// <summary>
        /// Gets or sets the starting level
        /// </summary>
        public int StartingLevel { get; set; } = 1;

        /// <summary>
        /// Gets or sets the maximum level
        /// </summary>
        public int MaxLevel { get; set; } = 50;

        /// <summary>
        /// Gets or sets whether to show data point values on the graph
        /// </summary>
        public bool ShowDataPointValues { get; set; } = false;

        /// <summary>
        /// Gets or sets whether to show X-axis labels
        /// </summary>
        public bool ShowXAxisLabels { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to show Y-axis labels
        /// </summary>
        public bool ShowYAxisLabels { get; set; } = true;

        /// <summary>
        /// Gets or sets the custom X-axis label name
        /// </summary>
        public string CustomXAxisLabel { get; set; } = "X";

        /// <summary>
        /// Gets or sets the custom Y-axis label name
        /// </summary>
        public string CustomYAxisLabel { get; set; } = "Y";

        /// <summary>
        /// Gets or sets the title for the graph
        /// </summary>
        public string Title { get; set; } = "";

        #endregion

        #region Constructor and Initialization

        /// <summary>
        /// Creates a new instance of the Graph class
        /// </summary>
        public Graph()
        {
            InitializeStyles();

            // Set default values that match the Advanced Leveling System
            LevelUpMultiplier = 1.1f;
            StartingExperience = 250;
            StartingLevel = 1;
            MaxLevel = 50;
            MaxPreviewPoints = MaxLevel;
        }

        private void InitializeStyles()
        {
            labelStyle = new GUIStyle(EditorStyles.miniLabel);
            labelStyle.normal.textColor = labelColor;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Draws a line graph with the given data
        /// </summary>
        /// <param name="values">The values to display in the graph</param>
        /// <param name="title">The title of the graph</param>
        /// <param name="xAxisLabel">The label for the X axis</param>
        /// <param name="yAxisLabel">The label for the Y axis</param>
        /// <param name="xAxisValues">Optional custom values for the X axis labels</param>
        /// <param name="availableWidth">Optional custom width for the graph</param>
        /// <param name="fixedHeight">Optional fixed height for the graph (overrides the height ratio)</param>
        public void DrawLineGraph(List<float> values, string title = "Graph", string xAxisLabel = "X", string yAxisLabel = "Y", List<string> xAxisValues = null, float? availableWidth = null, float? fixedHeight = null)
        {
            if (values == null || values.Count < 2)
            {
                EditorGUILayout.HelpBox("Not enough data points to draw a graph.", MessageType.Warning);
                return;
            }

            // No logging needed here

            // Draw the graph
            DrawGraphInternal(values, null, title, xAxisLabel, yAxisLabel, xAxisValues, availableWidth, fixedHeight);
        }

        /// <summary>
        /// Draws a line graph with the given data and a comparison line
        /// </summary>
        /// <param name="primaryValues">The primary values to display in the graph</param>
        /// <param name="secondaryValues">The secondary values to display for comparison</param>
        /// <param name="title">The title of the graph</param>
        /// <param name="xAxisLabel">The label for the X axis</param>
        /// <param name="yAxisLabel">The label for the Y axis</param>
        /// <param name="primaryName">The name of the primary line for the legend</param>
        /// <param name="secondaryName">The name of the secondary line for the legend</param>
        /// <param name="xAxisValues">Optional custom values for the X axis labels</param>
        /// <param name="availableWidth">Optional custom width for the graph</param>
        /// <param name="fixedHeight">Optional fixed height for the graph (overrides the height ratio)</param>
        public void DrawComparisonLineGraph(List<float> primaryValues, List<float> secondaryValues,
            string title = "Comparison Graph", string xAxisLabel = "X", string yAxisLabel = "Y",
            string primaryName = "Primary", string secondaryName = "Secondary",
            List<string> xAxisValues = null, float? availableWidth = null, float? fixedHeight = null)
        {
            if (primaryValues == null || primaryValues.Count < 2)
            {
                EditorGUILayout.HelpBox("Not enough data points to draw the primary graph.", MessageType.Warning);
                return;
            }

            if (secondaryValues == null || secondaryValues.Count < 2)
            {
                EditorGUILayout.HelpBox("Not enough data points to draw the secondary graph.", MessageType.Warning);
                return;
            }

            // Draw the graphs
            DrawGraphInternal(primaryValues, secondaryValues, title, xAxisLabel, yAxisLabel, xAxisValues, availableWidth, fixedHeight, primaryName, secondaryName);
        }

        /// <summary>
        /// Draws a bar graph with the given data
        /// </summary>
        /// <param name="values">The values to display in the graph</param>
        /// <param name="title">The title of the graph</param>
        /// <param name="xAxisLabel">The label for the X axis</param>
        /// <param name="yAxisLabel">The label for the Y axis</param>
        /// <param name="xAxisValues">Optional custom values for the X axis labels</param>
        /// <param name="availableWidth">Optional custom width for the graph</param>
        /// <param name="fixedHeight">Optional fixed height for the graph (overrides the height ratio)</param>
        public void DrawBarGraph(List<float> values, string title = "Bar Graph", string xAxisLabel = "X", string yAxisLabel = "Y", List<string> xAxisValues = null, float? availableWidth = null, float? fixedHeight = null)
        {
            if (values == null || values.Count == 0)
            {
                EditorGUILayout.HelpBox("No data points to draw a bar graph.", MessageType.Warning);
                return;
            }

            // Draw the bar graph
            DrawBarGraphInternal(values, title, xAxisLabel, yAxisLabel, xAxisValues, availableWidth, fixedHeight);
        }

        /// <summary>
        /// Internal method to draw a bar graph
        /// </summary>
        protected virtual void DrawBarGraphInternal(List<float> values, string title, string xAxisLabel, string yAxisLabel, List<string> xAxisValues, float? customAvailableWidth, float? fixedHeight = null)
        {
            try
            {
                // Skip drawing if requested
                if (SkipDrawing)
                    return;
                // Calculate the available width with proper margins
                float availableWidth;
                if (customAvailableWidth.HasValue)
                {
                    availableWidth = customAvailableWidth.Value - (horizontalMargin * 2);
                }
                else
                {
                    availableWidth = EditorGUIUtility.currentViewWidth - (horizontalMargin * 2);
                }

                // Calculate the graph width based on the available width and the width ratio
                float graphWidth = availableWidth * graphSize.x;

                // Calculate the graph height based on the width and the height ratio, or use fixed height if provided
                float graphHeight = fixedHeight.HasValue ? fixedHeight.Value : graphWidth * graphSize.y;

                // Get a rect for the graph that scales with the window size, adding space for both top and bottom labels
                Rect graphRect = EditorGUILayout.GetControlRect(false, graphHeight + bottomLabelSpace + topLabelSpace);

                // Use the calculated width
                graphRect.width = graphWidth;

                // Center the graph horizontally
                float centeringWidth = customAvailableWidth.HasValue ? customAvailableWidth.Value : EditorGUIUtility.currentViewWidth;
                graphRect.x = (centeringWidth - graphRect.width) / 2;

                // Adjust Y position to leave space for top labels
                graphRect.y += topLabelSpace;

                // Add extra left padding for Y-axis labels
                graphRect.x += leftLabelSpace;
                graphRect.width -= (leftLabelSpace + rightLabelSpace);
                // Adjust the actual drawing area to leave space for bottom labels
                graphRect.height -= bottomLabelSpace;
                GUI.DrawTexture(graphRect, EditorGUIUtility.whiteTexture, ScaleMode.StretchToFill, true, 0, graphBackgroundColor, 0, 0);

                // Draw grid lines
                DrawGridLines(graphRect);

                // Find the maximum value for proper scaling
                float maxValue = values.Max();
                float minValue = 0; // Bar graphs typically start at 0

                // Add a title to indicate what's being shown
                GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };

                // Use class title if provided, otherwise use the default title
                string displayTitle = !string.IsNullOrEmpty(Title) ? Title : title;
                GUI.Label(new Rect(graphRect.x, graphRect.y - 20, graphRect.width, 20), displayTitle, titleStyle);

                // Draw the bars
                DrawBars(graphRect, values, primaryCurveColor, minValue, maxValue);

                // Draw labels
                DrawLabels(graphRect, values, minValue, maxValue, xAxisLabel, yAxisLabel, xAxisValues);

                EditorGUILayout.Space(10);
            }
            catch (System.Exception e)
            {
                // If there's an error in the graph drawing, log it and display a message
                Debug.LogError($"Error drawing bar graph: {e.Message}");
                EditorGUILayout.HelpBox($"Error drawing bar graph: {e.Message}", MessageType.Error);
            }
        }

        /// <summary>
        /// Draws bars for a bar graph
        /// </summary>
        protected virtual void DrawBars(Rect graphRect, List<float> values, Color color, float minValue, float maxValue)
        {
            if (values.Count == 0)
                return;

            // Calculate bar width based on the number of values
            float barWidth = (graphRect.width - 2 * graphPadding) / values.Count * 0.8f; // 80% of available space per bar
            float spacing = (graphRect.width - 2 * graphPadding) / values.Count * 0.2f; // 20% spacing

            // Draw each bar
            for (int i = 0; i < values.Count; i++)
            {
                float normalizedValue = Mathf.InverseLerp(minValue, maxValue, values[i]);
                float barHeight = normalizedValue * (graphRect.height - 2 * graphPadding);

                // Calculate bar position
                float x = graphRect.x + graphPadding + i * (barWidth + spacing);
                float y = graphRect.y + graphRect.height - graphPadding - barHeight;

                // Draw the bar
                Rect barRect = new Rect(x, y, barWidth, barHeight);
                EditorGUI.DrawRect(barRect, color);

                // Draw a darker outline
                Color outlineColor = new Color(color.r * 0.7f, color.g * 0.7f, color.b * 0.7f, color.a);
                Handles.color = outlineColor;
                Vector3[] outlinePoints = new Vector3[]
                {
                    new Vector3(barRect.x, barRect.y),
                    new Vector3(barRect.x + barRect.width, barRect.y),
                    new Vector3(barRect.x + barRect.width, barRect.y + barRect.height),
                    new Vector3(barRect.x, barRect.y + barRect.height),
                    new Vector3(barRect.x, barRect.y)
                };
                Handles.DrawPolyLine(outlinePoints);

                // Optionally draw the value on top of the bar
                GUIStyle valueStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };

                // Only draw the value if the bar is tall enough
                if (barHeight > 20)
                {
                    GUI.Label(new Rect(x, y - 15, barWidth, 15), FormatLargeNumber(values[i]), valueStyle);
                }
            }
        }

        #endregion

        #region Internal Methods

        /// <summary>
        /// Internal method to draw the graph with all its components
        /// </summary>
        /// <param name="primaryValues">The primary values to display</param>
        /// <param name="secondaryValues">Optional secondary values for comparison</param>
        /// <param name="title">The title of the graph</param>
        /// <param name="xAxisLabel">The label for the X axis</param>
        /// <param name="yAxisLabel">The label for the Y axis</param>
        /// <param name="xAxisValues">Optional custom values for the X axis labels</param>
        /// <param name="customAvailableWidth">Optional custom width for the graph</param>
        /// <param name="fixedHeight">Optional fixed height for the graph (overrides the height ratio)</param>
        /// <param name="primaryName">The name of the primary data series</param>
        /// <param name="secondaryName">The name of the secondary data series</param>
        protected virtual void DrawGraphInternal(List<float> primaryValues, List<float> secondaryValues = null,
            string title = "Graph", string xAxisLabel = "X", string yAxisLabel = "Y",
            List<string> xAxisValues = null, float? customAvailableWidth = null, float? fixedHeight = null,
            string primaryName = "Primary", string secondaryName = "Secondary")
        {
            try
            {
                // Skip drawing if requested
                if (SkipDrawing)
                    return;

                if (primaryValues.Count == 0)
                    return;

                // Limit the number of points to display if needed
                List<float> displayPrimaryValues = LimitDataPoints(primaryValues, MaxPreviewPoints);
                List<float> displaySecondaryValues = secondaryValues != null ?
                    LimitDataPoints(secondaryValues, MaxPreviewPoints) : null;

                // Calculate the available width with proper margins
                // If a custom width is provided, use that instead of EditorGUIUtility.currentViewWidth
                float availableWidth;
                if (customAvailableWidth.HasValue)
                {
                    availableWidth = customAvailableWidth.Value - (horizontalMargin * 2); // Apply horizontal margin on each side
                }
                else
                {
                    availableWidth = EditorGUIUtility.currentViewWidth - (horizontalMargin * 2); // Apply horizontal margin on each side
                }

                // Calculate the graph width based on the available width and the width ratio
                float graphWidth = availableWidth * graphSize.x;

                // Calculate the graph height based on the width and the height ratio, or use fixed height if provided
                float graphHeight = fixedHeight.HasValue ? fixedHeight.Value : graphWidth * graphSize.y;

                // Get a rect for the graph that scales with the window size, adding space for both top and bottom labels
                Rect graphRect = EditorGUILayout.GetControlRect(false, graphHeight + bottomLabelSpace + topLabelSpace);

                // Use the calculated width
                graphRect.width = graphWidth;

                // Adjust the graph positioning to fit better in the available space
                // If a custom width is provided, use that for positioning, otherwise use EditorGUIUtility.currentViewWidth
                float centeringWidth = customAvailableWidth.HasValue ? customAvailableWidth.Value : EditorGUIUtility.currentViewWidth;
                // Center the graph horizontally
                graphRect.x = (centeringWidth - graphRect.width) / 2;

                // Adjust Y position to leave space for top labels
                graphRect.y += topLabelSpace;

                // Add extra left padding for Y-axis labels
                graphRect.x += leftLabelSpace; // Move the graph right to make room for labels
                graphRect.width -= (leftLabelSpace + rightLabelSpace); // Reduce width to maintain margins
                // Adjust the actual drawing area to leave space for bottom labels
                graphRect.height -= bottomLabelSpace;
                GUI.DrawTexture(graphRect, EditorGUIUtility.whiteTexture, ScaleMode.StretchToFill, true, 0, graphBackgroundColor, 0, 0);

                // Draw grid lines
                DrawGridLines(graphRect);

                // Find the maximum value across both curves for proper scaling
                float maxValue = displayPrimaryValues.Max();
                float minValue = displayPrimaryValues.Min();

                if (displaySecondaryValues != null && displaySecondaryValues.Count > 0)
                {
                    maxValue = Mathf.Max(maxValue, displaySecondaryValues.Max());
                    minValue = Mathf.Min(minValue, displaySecondaryValues.Min());
                }

                // Add a title to indicate what's being shown
                GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };

                // Use class title if provided, otherwise use the default title
                string displayTitle = !string.IsNullOrEmpty(Title) ? Title : title;
                GUI.Label(new Rect(graphRect.x, graphRect.y - 20, graphRect.width, 20), displayTitle, titleStyle);

                // Draw the main curve
                DrawLine(graphRect, displayPrimaryValues, primaryCurveColor, minValue, maxValue);

                // Draw the comparison curve if available
                if (displaySecondaryValues != null && displaySecondaryValues.Count > 0)
                {
                    DrawLine(graphRect, displaySecondaryValues, secondaryCurveColor, minValue, maxValue);
                }

                // Draw labels
                DrawLabels(graphRect, displayPrimaryValues, minValue, maxValue, xAxisLabel, yAxisLabel, xAxisValues);

                // Draw legend if comparison is active
                if (displaySecondaryValues != null && displaySecondaryValues.Count > 0)
                {
                    DrawLegend(graphRect, primaryName, secondaryName);
                }

                EditorGUILayout.Space(10);
            }
            catch (System.Exception e)
            {
                // If there's an error in the graph drawing, log it and display a message
                Debug.LogError($"Error drawing graph: {e.Message}");
                EditorGUILayout.HelpBox($"Error drawing graph: {e.Message}", MessageType.Error);
            }
        }

        private List<float> LimitDataPoints(List<float> values, int maxPoints)
        {
            if (values.Count <= maxPoints)
                return values;

            List<float> result = new List<float>();
            float step = (float)(values.Count - 1) / (maxPoints - 1);

            for (int i = 0; i < maxPoints; i++)
            {
                float index = i * step;
                int lowerIndex = Mathf.FloorToInt(index);
                int upperIndex = Mathf.CeilToInt(index);

                if (lowerIndex == upperIndex || upperIndex >= values.Count)
                {
                    result.Add(values[lowerIndex]);
                }
                else
                {
                    float t = index - lowerIndex;
                    result.Add(Mathf.Lerp(values[lowerIndex], values[upperIndex], t));
                }
            }

            return result;
        }

        /// <summary>
        /// Draws the grid lines for the graph
        /// </summary>
        /// <param name="graphRect">The rectangle representing the graph area</param>
        protected virtual void DrawGridLines(Rect graphRect)
        {
            // Draw horizontal grid lines
            for (int i = 0; i <= horizontalGridLines; i++)
            {
                float y = graphRect.y + graphRect.height * (1 - (float)i / horizontalGridLines);
                Handles.color = gridLineColor;
                Handles.DrawLine(new Vector3(graphRect.x, y), new Vector3(graphRect.x + graphRect.width, y));
            }

            // Draw vertical grid lines
            for (int i = 0; i <= verticalGridLines; i++)
            {
                float x = graphRect.x + graphRect.width * ((float)i / verticalGridLines);
                Handles.color = gridLineColor;
                Handles.DrawLine(new Vector3(x, graphRect.y), new Vector3(x, graphRect.y + graphRect.height));
            }
        }

        /// <summary>
        /// Draws a line on the graph with the given values and color
        /// </summary>
        /// <param name="graphRect">The rectangle representing the graph area</param>
        /// <param name="values">The values to draw</param>
        /// <param name="color">The color of the line</param>
        /// <param name="minValue">The minimum value in the data</param>
        /// <param name="maxValue">The maximum value in the data</param>
        protected virtual void DrawLine(Rect graphRect, List<float> values, Color color, float minValue, float maxValue)
        {
            if (values.Count < 2)
                return;

            // Apply logarithmic scaling for steep curves
            List<float> scaledValues = new List<float>();
            bool useLogScale = ShouldUseLogScale(values);

            // For extremely steep curves, use a more aggressive log scale
            bool useDoubleLogScale = false;
            if (useLogScale)
            {
                float maxVal = values.Max();
                float minVal = values.Min();
                if (maxVal > minVal * 1000) // For extremely steep curves
                {
                    useDoubleLogScale = true;
                }
            }

            // When showing raw formula values, we still use the original values
            // but we'll apply different scaling to them later
            List<float> displayValues = values;

            // Ensure we have valid values before scaling
            for (int i = 0; i < displayValues.Count; i++)
            {
                // Ensure we don't have zero or negative values that could break logarithmic scaling
                float safeValue = Mathf.Max(0.001f, displayValues[i]);
                float scaledValue;

                // Handle the case when levelUpMultiplier is 0 (turned off)
                // In this case, we still want to show the curve properly
                if (LevelUpMultiplier <= 0f && i > 0 && !ShowRawCurveValues)
                {
                    // When multiplier is turned off, ensure the curve still shows properly
                    // by using a very gentle slope
                    safeValue = displayValues[0] * (1f + (0.05f * i));
                }

                // When showing raw formula values, we want to visualize the formula behavior directly
                // The raw values should already be calculated correctly by the algorithm
                if (ShowRawCurveValues)
                {
                    // For raw formula mode, just use the values directly
                    // They should already represent the multipliers at each level
                    scaledValue = safeValue;
                }
                else if (useDoubleLogScale)
                {
                    // Use double logarithmic scaling for extremely steep curves
                    // This applies log scale twice to flatten the curve even more
                    scaledValue = Mathf.Log10(Mathf.Log10(safeValue + 10) + 1);
                }
                else if (useLogScale)
                {
                    // Use logarithmic scaling for steep curves
                    scaledValue = Mathf.Log10(safeValue + 1);
                }
                else
                {
                    // Use linear scaling for gentle curves
                    scaledValue = safeValue;
                }
                scaledValues.Add(scaledValue);
            }

            // Find the maximum scaled value for normalization
            float maxScaledValue = scaledValues.Max();
            float minScaledValue = scaledValues.Min();

            // Ensure we have a valid range to prevent division by zero
            if (maxScaledValue <= minScaledValue)
            {
                // If all values are the same, create a small artificial range
                maxScaledValue = minScaledValue + 1;
            }

            // For raw formula mode, we want to ensure the graph is scaled appropriately
            if (ShowRawCurveValues)
            {
                // For multiplier values, center around 1.0 if they're close to it
                // This makes the pattern more visible
                if (minScaledValue > 0.8f && minScaledValue < 1.2f && maxScaledValue < 1.5f)
                {
                    float midPoint = 1.0f;
                    float maxDistance = Mathf.Max(Mathf.Abs(maxScaledValue - midPoint), Mathf.Abs(minScaledValue - midPoint));

                    // Ensure we have at least a minimum range for visibility
                    maxDistance = Mathf.Max(maxDistance, 0.1f);

                    // Set symmetric range around the midpoint
                    minScaledValue = midPoint - maxDistance;
                    maxScaledValue = midPoint + maxDistance;
                }
                else
                {
                    // If the range is very small, expand it to make differences more visible
                    if (maxScaledValue - minScaledValue < 0.1f)
                    {
                        float midPoint = (maxScaledValue + minScaledValue) / 2f;
                        minScaledValue = midPoint - 0.05f;
                        maxScaledValue = midPoint + 0.05f;
                    }
                }
            }

            // Add a small buffer to prevent points from being drawn at the very edge
            float buffer = (maxScaledValue - minScaledValue) * 0.05f;
            minScaledValue -= buffer;
            maxScaledValue += buffer;

            // Draw the curve
            Handles.color = color;
            for (int i = 0; i < values.Count - 1; i++)
            {
                // Calculate positions with proper padding to stay within graph bounds
                float x1 = graphRect.x + graphPadding + (graphRect.width - 2 * graphPadding) * ((float)i / (values.Count - 1));
                float y1 = graphRect.y + graphRect.height - graphPadding - (graphRect.height - 2 * graphPadding) * ((scaledValues[i] - minScaledValue) / (maxScaledValue - minScaledValue));

                float x2 = graphRect.x + graphPadding + (graphRect.width - 2 * graphPadding) * ((float)(i + 1) / (values.Count - 1));
                float y2 = graphRect.y + graphRect.height - graphPadding - (graphRect.height - 2 * graphPadding) * ((scaledValues[i + 1] - minScaledValue) / (maxScaledValue - minScaledValue));

                // Ensure the points are within the graph bounds with proper padding
                y1 = Mathf.Clamp(y1, graphRect.y + graphPadding, graphRect.y + graphRect.height - graphPadding);
                y2 = Mathf.Clamp(y2, graphRect.y + graphPadding, graphRect.y + graphRect.height - graphPadding);

                // Ensure x values are also properly clamped
                x1 = Mathf.Clamp(x1, graphRect.x + graphPadding, graphRect.x + graphRect.width - graphPadding);
                x2 = Mathf.Clamp(x2, graphRect.x + graphPadding, graphRect.x + graphRect.width - graphPadding);

                // Draw thicker lines for better visibility
                Handles.DrawAAPolyLine(2.5f, new Vector3(x1, y1), new Vector3(x2, y2));

                // No need to check for points outside bounds as we're already clamping them
            }

            // Draw points on the curve
            for (int i = 0; i < values.Count; i++)
            {
                float x = graphRect.x + graphPadding + (graphRect.width - 2 * graphPadding) * ((float)i / (values.Count - 1));
                float y = graphRect.y + graphRect.height - graphPadding - (graphRect.height - 2 * graphPadding) * ((scaledValues[i] - minScaledValue) / (maxScaledValue - minScaledValue));

                // Ensure the point is within the graph bounds with proper padding
                y = Mathf.Clamp(y, graphRect.y + graphPadding, graphRect.y + graphRect.height - graphPadding);
                x = Mathf.Clamp(x, graphRect.x + graphPadding, graphRect.x + graphRect.width - graphPadding);

                // Draw a circle instead of an "X" character
                Handles.DrawSolidDisc(new Vector3(x, y), Vector3.forward, 3); // Larger points for better visibility
            }
        }

        /// <summary>
        /// Draws the axis labels and value labels for the graph
        /// </summary>
        /// <param name="graphRect">The rectangle representing the graph area</param>
        /// <param name="values">The values being displayed</param>
        /// <param name="minValue">The minimum value in the data</param>
        /// <param name="maxValue">The maximum value in the data</param>
        /// <param name="xAxisLabel">The label for the X axis</param>
        /// <param name="yAxisLabel">The label for the Y axis</param>
        /// <param name="xAxisValues">Optional custom values for the X axis labels</param>
        protected virtual void DrawLabels(Rect graphRect, List<float> values, float minValue, float maxValue,
            string xAxisLabel = "X", string yAxisLabel = "Y", List<string> xAxisValues = null)
        {
            bool useLogScale = ShouldUseLogScale(values);

            // Use custom axis labels if provided
            string actualXAxisLabel = string.IsNullOrEmpty(CustomXAxisLabel) ? xAxisLabel : CustomXAxisLabel;
            string actualYAxisLabel = string.IsNullOrEmpty(CustomYAxisLabel) ? yAxisLabel : CustomYAxisLabel;

            // If showing raw formula values, change the Y-axis label
            if (ShowRawCurveValues)
            {
                actualYAxisLabel = "Level-Up Multiplier";
            }

            // Create improved label styles
            GUIStyle yAxisLabelStyle = new GUIStyle(labelStyle);
            yAxisLabelStyle.normal.textColor = Color.white;
            yAxisLabelStyle.fontSize = 10;
            yAxisLabelStyle.fontStyle = FontStyle.Bold;
            yAxisLabelStyle.alignment = TextAnchor.MiddleRight;

            GUIStyle xAxisLabelStyle = new GUIStyle(labelStyle);
            xAxisLabelStyle.normal.textColor = Color.white;
            xAxisLabelStyle.fontSize = 10;
            xAxisLabelStyle.fontStyle = FontStyle.Bold;
            xAxisLabelStyle.alignment = TextAnchor.MiddleCenter;

            // Draw Y-axis labels (values) with improved visibility
            if (ShowYAxisLabels)
            {
                for (int i = 0; i <= horizontalGridLines; i++)
                {
                    float y = graphRect.y + graphRect.height * (1 - (float)i / horizontalGridLines);

                    string valueLabel;
                    if (ShowRawCurveValues)
                    {
                        // For raw formula mode, show the multiplier value with appropriate precision
                        float value = minValue + (maxValue - minValue) * ((float)i / horizontalGridLines);
                        string format = value < 1.1f ? "F3" : "F2";
                        valueLabel = value.ToString(format) + "x";
                    }
                    else if (useLogScale)
                    {
                        // For log scale, calculate the actual value that corresponds to this position
                        float logMax = Mathf.Log10(maxValue + 1);
                        float logValue = logMax * ((float)i / horizontalGridLines);
                        float actualValue = Mathf.Pow(10, logValue) - 1;
                        valueLabel = FormatLargeNumber(actualValue);
                    }
                    else
                    {
                        // For linear scale, just use the direct proportion
                        float value = minValue + (maxValue - minValue) * ((float)i / horizontalGridLines);
                        valueLabel = FormatLargeNumber(value);
                    }

                    // Draw the label with white text for visibility against dark background
                    // Increased width to accommodate larger numbers
                    Rect labelRect = new Rect(graphRect.x - 65, y - 10, 60, 20);
                    GUI.Label(labelRect, valueLabel, yAxisLabelStyle);
                }

                // Draw the Y-axis label vertically
                DrawYAxisTitle(graphRect, actualYAxisLabel, yAxisLabelStyle);
            }

            // Draw X-axis labels with improved visibility
            if (ShowXAxisLabels)
            {
                // Use a reasonable number of labels to avoid crowding
                int numXLabels = Mathf.Min(verticalGridLines, 10); // Limit to 10 labels max for readability
                for (int i = 0; i <= numXLabels; i++)
                {
                    float x = graphRect.x + graphRect.width * ((float)i / numXLabels);

                    string label;
                    if (xAxisValues != null && xAxisValues.Count > 0)
                    {
                        // Use custom X axis values if provided
                        int index = Mathf.RoundToInt(((float)i / numXLabels) * (xAxisValues.Count - 1));
                        index = Mathf.Clamp(index, 0, xAxisValues.Count - 1);

                        // Make sure we have a valid label
                        if (index < xAxisValues.Count)
                            label = xAxisValues[index];
                        else
                            label = i.ToString();
                    }
                    else
                    {
                        // Otherwise use numeric indices
                        int index = Mathf.RoundToInt(((float)i / numXLabels) * (values.Count - 1));
                        label = index.ToString();
                    }

                    // Calculate label width based on content to avoid overlap
                    float labelWidth = xAxisLabelStyle.CalcSize(new GUIContent(label)).x + 4;

                    // Draw the label with white text for visibility
                    // Center the label under the grid line - adjusted vertical spacing to 10
                    Rect labelRect = new Rect(x - labelWidth/2, graphRect.y + graphRect.height + 10, labelWidth, 20);

                    // Make sure we have a valid label (not too long)
                    if (label.Length > 10)
                    {
                        label = label.Substring(0, 10);
                    }

                    GUI.Label(labelRect, label, xAxisLabelStyle);
                }

                // Draw the X-axis label at the bottom
                DrawXAxisTitle(graphRect, actualXAxisLabel, xAxisLabelStyle);
            }

            // Draw data point values if enabled
            if (ShowDataPointValues)
            {
                DrawDataPointLabels(graphRect, values, minValue, maxValue, useLogScale);
            }

            // Draw scale type directly in the graph area to ensure it's visible
            if (useLogScale)
            {
                DrawScaleIndicator(graphRect);
            }
        }

        /// <summary>
        /// Draws the Y-axis title (positioned at the top of the Y-axis)
        /// </summary>
        protected virtual void DrawYAxisTitle(Rect graphRect, string yAxisLabel, GUIStyle baseLabelStyle)
        {
            // Create style for the Y-axis label
            GUIStyle yAxisTitleStyle = new GUIStyle(baseLabelStyle);
            yAxisTitleStyle.alignment = TextAnchor.MiddleCenter;
            yAxisTitleStyle.fontSize = 11;
            yAxisTitleStyle.fontStyle = FontStyle.Bold;

            // Calculate the width based on the text content
            float estimatedTextWidth = yAxisLabel.Length * 8 + 20; // Approximate width based on character count
            float estimatedTextHeight = 20; // Fixed height for the label

            // Position the label at the top of the Y-axis
            float xPos = graphRect.x - 65; // Position to the left of the Y-axis
            float yPos = graphRect.y - 30; // Position above the graph

            // Draw the label horizontally at the top
            GUI.Label(new Rect(xPos, yPos, estimatedTextWidth, estimatedTextHeight), yAxisLabel, yAxisTitleStyle);
        }

        /// <summary>
        /// Draws the X-axis title
        /// </summary>
        protected virtual void DrawXAxisTitle(Rect graphRect, string xAxisLabel, GUIStyle baseLabelStyle)
        {
            GUIStyle xAxisTitleStyle = new GUIStyle(baseLabelStyle);
            xAxisTitleStyle.fontSize = 11;
            xAxisTitleStyle.alignment = TextAnchor.MiddleCenter;
            xAxisTitleStyle.normal.background = null; // Remove any background

            // Use a simpler approach based on character count
            // Each character is approximately 8-10 pixels wide in this font size
            float estimatedTextWidth = xAxisLabel.Length * 10 + 40; // Add 40px buffer
            float estimatedTextHeight = 25; // Fixed height with buffer

            // Center the label under the X-axis values (increased spacing from 35 to 45)
            Rect xAxisTitleRect = new Rect(
                graphRect.x + graphRect.width/2 - estimatedTextWidth/2,
                graphRect.y + graphRect.height + 25,
                estimatedTextWidth,
                estimatedTextHeight);

            GUI.Label(xAxisTitleRect, xAxisLabel, xAxisTitleStyle);
        }

        /// <summary>
        /// Draws the scale indicator (e.g., "LOG SCALE")
        /// </summary>
        protected virtual void DrawScaleIndicator(Rect graphRect)
        {
            // Create a background box for the log scale indicator
            Rect bgRect = new Rect(graphRect.x + 5, graphRect.y + 5, 70, 20);
            EditorGUI.DrawRect(bgRect, new Color(0.1f, 0.1f, 0.1f, 0.7f));

            // Create a style for the log scale text
            GUIStyle scaleNoticeStyle = new GUIStyle(EditorStyles.miniLabel);
            scaleNoticeStyle.normal.textColor = Color.yellow;
            scaleNoticeStyle.alignment = TextAnchor.MiddleCenter;
            scaleNoticeStyle.fontSize = 9;
            scaleNoticeStyle.fontStyle = FontStyle.Bold;

            // Draw the text centered in the background box
            EditorGUI.LabelField(bgRect, "LOG SCALE", scaleNoticeStyle);
        }

        /// <summary>
        /// Draws a legend for the graph
        /// </summary>
        /// <param name="graphRect">The rectangle representing the graph area</param>
        /// <param name="primaryName">The name of the primary data series</param>
        /// <param name="secondaryName">The name of the secondary data series</param>
        protected virtual void DrawLegend(Rect graphRect, string primaryName, string secondaryName)
        {
            // Create a legend box in the top-right corner
            float legendWidth = 180;
            float legendHeight = 50;
            float padding = 5;

            Rect legendRect = new Rect(
                graphRect.x + graphRect.width - legendWidth - padding,
                graphRect.y + padding,
                legendWidth,
                legendHeight);

            // Draw legend background
            GUI.DrawTexture(legendRect, EditorGUIUtility.whiteTexture, ScaleMode.StretchToFill, true, 0, legendBackgroundColor, 0, 0);

            // Draw legend title
            GUI.Label(new Rect(legendRect.x + 5, legendRect.y + 5, legendRect.width - 10, 20), "Legend", EditorStyles.boldLabel);

            // Draw primary curve line
            Rect mainLineRect = new Rect(legendRect.x + 10, legendRect.y + 25, 20, 2);
            EditorGUI.DrawRect(mainLineRect, primaryCurveColor);
            GUI.Label(new Rect(mainLineRect.x + 25, mainLineRect.y - 8, 150, 20), primaryName, labelStyle);

            // Draw secondary curve line
            Rect compLineRect = new Rect(legendRect.x + 10, legendRect.y + 40, 20, 2);
            EditorGUI.DrawRect(compLineRect, secondaryCurveColor);
            GUI.Label(new Rect(compLineRect.x + 25, compLineRect.y - 8, 150, 20), secondaryName, labelStyle);
        }

        // IsOscillatingPattern and IsCyclicalPattern methods have been removed
        // We now use a simpler approach that just plots the values directly

        /// <summary>
        /// Determines if logarithmic scaling should be used based on the steepness of the curve
        /// </summary>
        /// <param name="values">The values to analyze</param>
        /// <returns>True if logarithmic scaling should be used, false otherwise</returns>
        protected virtual bool ShouldUseLogScale(List<float> values)
        {
            // If graph zooming is disabled, never use log scale
            if (!EnableGraphZooming)
                return false;

            if (values.Count < 2)
                return false;

            // For raw formula mode, we typically don't need log scaling
            // since the values are already normalized as multipliers
            if (ShowRawCurveValues)
                return false;

            // Calculate the ratio between the maximum and minimum values
            float maxValue = values.Max();
            float minValue = values.Min();

            // Prevent division by zero or very small numbers
            if (minValue < 0.001f)
                minValue = 0.001f;

            // Only use log scale for very steep curves
            if (maxValue / minValue > 100)
                return true;

            // If the max value is extremely large, use log scale
            if (maxValue > 10000)
                return true;

            // If the level up multiplier is very high, use log scale
            if (LevelUpMultiplier > 2.0f)
                return true;

            return false;
        }

        /// <summary>
        /// Draws labels for data points on the graph
        /// </summary>
        protected virtual void DrawDataPointLabels(Rect graphRect, List<float> values, float minValue, float maxValue, bool useLogScale)
        {
            if (values == null || values.Count == 0)
                return;

            // Create a style for the data point labels
            GUIStyle dataPointStyle = new GUIStyle(EditorStyles.miniLabel);
            dataPointStyle.normal.textColor = Color.white;
            dataPointStyle.alignment = TextAnchor.MiddleCenter;
            dataPointStyle.fontSize = 9;
            dataPointStyle.fontStyle = FontStyle.Bold;

            // Create a background style for better visibility
            Color bgColor = new Color(0.1f, 0.1f, 0.1f, 0.7f);

            // Determine how many points to show (avoid overcrowding)
            int step = Mathf.Max(1, values.Count / 10); // Show at most ~10 labels

            for (int i = 0; i < values.Count; i += step)
            {
                // Skip some points to avoid overcrowding
                if (i > 0 && i < values.Count - 1 && values.Count > 20)
                    if (i % 2 != 0) continue;

                // Calculate position
                float xRatio = (float)i / (values.Count - 1);
                float x = graphRect.x + graphRect.width * xRatio;

                float yValue = values[i];
                float yRatio;

                if (useLogScale)
                {
                    // For log scale, use logarithmic mapping
                    float logMin = minValue <= 0 ? 0 : Mathf.Log10(minValue + 1);
                    float logMax = Mathf.Log10(maxValue + 1);
                    float logValue = Mathf.Log10(yValue + 1);
                    yRatio = (logValue - logMin) / (logMax - logMin);
                }
                else
                {
                    // For linear scale, use direct proportion
                    yRatio = (yValue - minValue) / (maxValue - minValue);
                }

                float y = graphRect.y + graphRect.height * (1 - yRatio);

                // Format the value
                string valueText;
                if (ShowRawCurveValues)
                {
                    // For raw formula values, show the actual multiplier value
                    // Format with more precision for small values
                    string format = yValue < 1.1f ? "F3" : "F2";
                    valueText = yValue.ToString(format) + "x";
                }
                else
                {
                    // For experience values, use the standard large number format
                    valueText = FormatLargeNumber(yValue);
                }

                // Calculate text size
                Vector2 textSize = dataPointStyle.CalcSize(new GUIContent(valueText));

                // Draw background for better visibility
                Rect bgRect = new Rect(x - textSize.x/2 - 2, y - textSize.y/2 - 2, textSize.x + 4, textSize.y + 4);
                EditorGUI.DrawRect(bgRect, bgColor);

                // Draw the value
                Rect labelRect = new Rect(x - textSize.x/2, y - textSize.y/2, textSize.x, textSize.y);
                GUI.Label(labelRect, valueText, dataPointStyle);
            }
        }

        /// <summary>
        /// Formats large numbers with K, M, B suffixes for better readability
        /// </summary>
        /// <param name="number">The number to format</param>
        /// <returns>A formatted string representation of the number</returns>
        protected virtual string FormatLargeNumber(float number)
        {
            // For very small numbers, show more decimal places
            if (number < 0.01f && number > 0)
            {
                return number.ToString("0.####");
            }
            // For small numbers, show more decimal places
            else if (number < 1)
            {
                return number.ToString("0.###");
            }
            // For billions
            else if (number >= 1000000000)
            {
                return (number / 1000000000f).ToString("0.##") + "B";
            }
            // For millions
            else if (number >= 1000000)
            {
                return (number / 1000000f).ToString("0.##") + "M";
            }
            // For thousands
            else if (number >= 1000)
            {
                return (number / 1000f).ToString("0.##") + "K";
            }
            // For regular numbers
            else
            {
                return number.ToString("0.##");
            }
        }

        #endregion
    }
}
