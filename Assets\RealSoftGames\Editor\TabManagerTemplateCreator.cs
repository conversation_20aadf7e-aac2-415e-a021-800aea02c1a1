using UnityEditor;
using UnityEngine;
using System.IO;
using System.Text;

namespace RealSoftGames.Editor.Templates
{
    public class TabManagerTemplateCreator
    {
        private const string TemplateMenuName = "Assets/Create/RealSoftGames/Tab Manager Script";
        private const string TemplatePath = "Assets/RealSoftGames/Templates/TabManagerTemplate.cs.txt";
        private const string DefaultFileName = "NewTabManager.cs";

        [MenuItem(TemplateMenuName, false, 80)]
        public static void CreateTabManagerScript()
        {
            string defaultPath = AssetDatabase.GetAssetPath(Selection.activeObject);

            if (string.IsNullOrEmpty(defaultPath))
            {
                defaultPath = "Assets";
            }
            else if (!Directory.Exists(defaultPath))
            {
                // If the selected path is a file, use its directory
                defaultPath = Path.GetDirectoryName(defaultPath);
            }

            // Make sure the template exists
            if (!File.Exists(TemplatePath))
            {
                // If the template doesn't exist, create it
                CreateTemplateFileIfNeeded();
            }

            // Create the script using Unity's built-in template system
            ProjectWindowUtil.CreateScriptAssetFromTemplateFile(TemplatePath, DefaultFileName);
        }

        [MenuItem(TemplateMenuName, true)]
        public static bool ValidateCreateTabManagerScript()
        {
            // This will check if the selected item is a folder
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            return AssetDatabase.IsValidFolder(path) || path == "";
        }

        /// <summary>
        /// Creates the template file if it doesn't exist
        /// </summary>
        private static void CreateTemplateFileIfNeeded()
        {
            string templateDirectory = Path.GetDirectoryName(TemplatePath);
            if (!Directory.Exists(templateDirectory))
            {
                Directory.CreateDirectory(templateDirectory);
            }

            // Create a default template if it doesn't exist
            StringBuilder template = new StringBuilder();
            template.AppendLine("using UnityEditor;");
            template.AppendLine("using UnityEngine;");
            template.AppendLine("");
            template.AppendLine("// Tab Manager Template");
            template.AppendLine("// This template creates a new tab manager for editor windows.");
            template.AppendLine("// After creating this script, you need to register it programmatically in your");
            template.AppendLine("// editor window's OnEnable method using the RegisterTabManager method.");
            template.AppendLine("");
            template.AppendLine("namespace RealSoftGames");
            template.AppendLine("{");
            template.AppendLine("    public class #SCRIPTNAME# : ITabManager");
            template.AppendLine("    {");
            template.AppendLine("        private readonly EditorWindowTemplate window;");
            template.AppendLine("        private GUIStyle headerStyle;");
            template.AppendLine("        private Vector2 scrollPosition;");
            template.AppendLine("");
            template.AppendLine("        // Tab properties");
            template.AppendLine("        public string TabName => \"#SCRIPTNAME#\";");
            template.AppendLine("");
            template.AppendLine("        public #SCRIPTNAME#(EditorWindowTemplate window)");
            template.AppendLine("        {");
            template.AppendLine("            this.window = window;");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        public void OnEnable()");
            template.AppendLine("        {");
            template.AppendLine("            InitializeStyles();");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        public void OnDisable()");
            template.AppendLine("        {");
            template.AppendLine("            // No need to track enabled state - managed by the editor window");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        public void OnDestroy()");
            template.AppendLine("        {");
            template.AppendLine("            // Clean up resources if needed");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        public void Update()");
            template.AppendLine("        {");
            template.AppendLine("            // Update logic if needed");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        private void InitializeStyles()");
            template.AppendLine("        {");
            template.AppendLine("            if (headerStyle == null)");
            template.AppendLine("            {");
            template.AppendLine("                headerStyle = new GUIStyle(EditorStyles.boldLabel)");
            template.AppendLine("                {");
            template.AppendLine("                    fontSize = 16,");
            template.AppendLine("                    alignment = TextAnchor.MiddleLeft,");
            template.AppendLine("                    margin = new RectOffset(5, 5, 5, 5)");
            template.AppendLine("                };");
            template.AppendLine("            }");
            template.AppendLine("        }");
            template.AppendLine("");
            template.AppendLine("        public void OnGUI()");
            template.AppendLine("        {");
            template.AppendLine("            InitializeStyles();");
            template.AppendLine("");
            template.AppendLine("            EditorGUILayout.BeginVertical();");
            template.AppendLine("");
            template.AppendLine("            // Header");
            template.AppendLine("            EditorGUILayout.LabelField(\"#SCRIPTNAME#\", headerStyle);");
            template.AppendLine("            EditorGUILayout.Space(10);");
            template.AppendLine("");
            template.AppendLine("            // Main content with scrollview");
            template.AppendLine("            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);");
            template.AppendLine("");
            template.AppendLine("            // TODO: Add your tab content here");
            template.AppendLine("            EditorGUILayout.HelpBox(\"This is a new tab manager. Add your content here.\", MessageType.Info);");
            template.AppendLine("");
            template.AppendLine("            EditorGUILayout.EndScrollView();");
            template.AppendLine("            EditorGUILayout.EndVertical();");
            template.AppendLine("        }");
            template.AppendLine("    }");
            template.AppendLine("}");

            File.WriteAllText(TemplatePath, template.ToString());
            AssetDatabase.Refresh();
        }
    }
}
