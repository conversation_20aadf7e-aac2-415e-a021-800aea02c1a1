#if UNITY_EDITOR
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace RealSoftGames
{
    [InitializeOnLoad]
    public class AddressablesDefine
    {
        private const string DEFINE_SYMBOL = "ADDRESSABLES_PACKAGE";

        static AddressablesDefine()
        {
            // Check if the Addressables package is installed
            bool addressablesInstalled = IsAddressablesInstalled();

            // Get the current define symbols
            string definesString = PlayerSettings.GetScriptingDefineSymbolsForGroup(
                EditorUserBuildSettings.selectedBuildTargetGroup);
            List<string> allDefines = definesString.Split(';').ToList();

            // Add or remove the define symbol based on whether Addressables is installed
            if (addressablesInstalled && !allDefines.Contains(DEFINE_SYMBOL))
            {
                allDefines.Add(DEFINE_SYMBOL);
                string newDefines = string.Join(";", allDefines);
                PlayerSettings.SetScriptingDefineSymbolsForGroup(
                    EditorUserBuildSettings.selectedBuildTargetGroup, newDefines);
                UnityEngine.Debug.Log($"Added {DEFINE_SYMBOL} define symbol as Addressables package was detected.");
            }
            else if (!addressablesInstalled && allDefines.Contains(DEFINE_SYMBOL))
            {
                allDefines.Remove(DEFINE_SYMBOL);
                string newDefines = string.Join(";", allDefines);
                PlayerSettings.SetScriptingDefineSymbolsForGroup(
                    EditorUserBuildSettings.selectedBuildTargetGroup, newDefines);
                UnityEngine.Debug.Log($"Removed {DEFINE_SYMBOL} define symbol as Addressables package was not detected.");
            }
        }

        private static bool IsAddressablesInstalled()
        {
            // Check if the Addressables assembly is available
            var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
            return assemblies.Any(a => a.GetName().Name == "Unity.Addressables");
        }
    }
}
#endif
