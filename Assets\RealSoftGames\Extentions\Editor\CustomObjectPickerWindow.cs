using System;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace RealSoftGames
{
    public class CustomObjectPickerWindow : EditorWindow
    {
        private Action<UnityEngine.Object> onObjectSelected;
        private UnityEngine.Object selectedObject;
        private UnityEngine.Object highlightedObject;
        private Vector2 scrollPosition;
        private UnityEngine.Object[] allSceneObjects;
        private UnityEngine.Object[] allAssets;
        private UnityEngine.Object[] filteredAssets;
        private string searchFilter = "";
        private bool allowSceneObjects;
        private bool isGridView; // Automatically determined based on asset type
        private float iconSize = 64f; // Default icon size, adjustable via slider

        // Tabs variables
        private int selectedTab = 0;
        private string[] tabLabels = { "Assets", "Scene" };

        private const float minWindowWidth = 400;
        private const float minWindowHeight = 500;


        public static CustomObjectPickerWindow Show(Type objectType, UnityEngine.Object currentSelection, bool allowSceneObjects, Action<UnityEngine.Object> onObjectSelected)
        {
            var window = GetWindow<CustomObjectPickerWindow>(true, $"Select {objectType.Name}", true);
            window.selectedObject = currentSelection;
            window.minSize = new Vector2(minWindowWidth, minWindowHeight);
            window.allowSceneObjects = allowSceneObjects;
            window.LoadAllAssets(objectType);
            window.LoadAllSceneObjects(objectType);
            window.onObjectSelected = onObjectSelected;
            window.isGridView = objectType == typeof(Sprite); // Automatically use grid view for Sprites

            return window;
        }

        private void LoadAllAssets(Type objectType)
        {
            string[] assetGuids = AssetDatabase.FindAssets($"t:{objectType.Name}");
            allAssets = assetGuids
                .Select(guid => AssetDatabase.LoadAssetAtPath(AssetDatabase.GUIDToAssetPath(guid), objectType))
                .ToArray();
            filteredAssets = allAssets; // Initially, all assets are shown
        }

        private void LoadAllSceneObjects(Type objectType)
        {
            if (!allowSceneObjects)
            {
                allSceneObjects = Array.Empty<UnityEngine.Object>();
                return;
            }

            allSceneObjects = FindObjectsOfType(objectType)
                .Where(obj => !EditorUtility.IsPersistent(obj)) // Only objects from the current scene, not assets
                .Cast<UnityEngine.Object>()
                .ToArray();
        }

        private void OnGUI()
        {
            DrawTabs();
            DrawSearchField();
            ApplySearchFilter();

            if (selectedTab == 0)
            {
                if (isGridView)
                {
                    DrawSpriteGrid(); // Draw grid view for sprites
                }
                else
                {
                    DrawAssetList(); // Draw list view for non-sprites
                }
            }
            else if (selectedTab == 1)
            {
                DrawSceneList(); // Draw scene objects
            }

            DrawBottomButtons();
            DrawSlider(); // Allow the user to adjust icon size
        }

        private void DrawTabs()
        {
            GUILayout.BeginHorizontal(EditorStyles.toolbar);
            selectedTab = GUILayout.Toolbar(selectedTab, tabLabels, EditorStyles.toolbarButton);
            GUILayout.EndHorizontal();
        }

        private void DrawSearchField()
        {
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label("Search", GUILayout.Width(50));
            searchFilter = EditorGUILayout.TextField(searchFilter, EditorStyles.toolbarSearchField);
            EditorGUILayout.EndHorizontal();
        }

        private void ApplySearchFilter()
        {
            if (string.IsNullOrEmpty(searchFilter))
            {
                filteredAssets = allAssets;
            }
            else
            {
                filteredAssets = allAssets
                    .Where(asset => asset.name.IndexOf(searchFilter, StringComparison.OrdinalIgnoreCase) >= 0)
                    .ToArray();
            }
        }

        private void DrawAssetList()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            if (DrawAssetItem(null, "(None)")) // Select "None" with a single click
            {
                selectedObject = null;
            }

            foreach (var asset in filteredAssets)
            {
                if (DrawAssetItem(asset, asset.name))
                {
                    selectedObject = asset; // Select the asset with a single click          
                }
            }

            EditorGUILayout.EndScrollView();
        }

        private void DrawSpriteGrid()
        {
            float gridSize = iconSize + (iconSize * 0.25f);
            int gridColumns = Mathf.Max(1, Mathf.FloorToInt(position.width / (gridSize + 20))); // Dynamic number of columns based on window size

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            EditorGUILayout.BeginHorizontal();

            for (int i = 0; i < filteredAssets.Length; i++)
            {
                Sprite sprite = filteredAssets[i] as Sprite;

                if (sprite != null)
                {
                    DrawSpriteCell(sprite, gridSize);

                    // Move to the next row after filling a column
                    if ((i + 1) % gridColumns == 0)
                    {
                        EditorGUILayout.EndHorizontal();
                        EditorGUILayout.BeginHorizontal();
                    }
                }
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndScrollView();
        }

        private void DrawSpriteCell(Sprite sprite, float spriteSize)
        {
            float padding = 5f; // Padding between icons
            float gridSize = spriteSize * 1.25f; // Grid size 25% larger than sprite size

            // Calculate the rect for the grid cell, making it 25% larger than the sprite size
            Rect paddedRect = GUILayoutUtility.GetRect(gridSize, gridSize);

            // Create a padded rectangle for the highlight background
            Rect highlightRect = new Rect(paddedRect.x - padding, paddedRect.y - padding, paddedRect.width + 2 * padding, paddedRect.height + 2 * padding);

            // Draw the background highlight based on selection state
            if (sprite == selectedObject)
            {
                EditorGUI.DrawRect(highlightRect, new Color(0.24f, 0.49f, 0.90f)); // Highlight area including padding
            }

            // Draw the sprite texture within the grid, centered inside the larger grid cell
            if (sprite != null)
            {
                // Calculate the rect for the sprite, centered in the grid cell
                Rect spriteRect = new Rect(
                    paddedRect.x + (gridSize - spriteSize) / 2,
                    paddedRect.y + (gridSize - spriteSize) / 2,
                    spriteSize,
                    spriteSize
                );

                GUI.DrawTexture(spriteRect, sprite.texture, ScaleMode.ScaleToFit);
            }

            // Handle sprite selection on click
            if (Event.current.type == EventType.MouseDown && paddedRect.Contains(Event.current.mousePosition))
            {
                if (Event.current.clickCount == 2) // Double-click to confirm
                {
                    selectedObject = sprite;
                    onObjectSelected?.Invoke(selectedObject);
                    Close();
                }
                else
                {
                    // Single-click to select
                    selectedObject = sprite;
                    GUI.changed = true;
                }
            }
        }



        private void DrawSceneList()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            if (DrawAssetItem(null, "(None)")) // Select "None" with a single click
            {
                selectedObject = null;
            }

            foreach (var sceneObj in allSceneObjects)
            {
                if (DrawAssetItem(sceneObj, sceneObj.name))
                {
                    selectedObject = sceneObj;
                }
            }

            EditorGUILayout.EndScrollView();
        }

        private bool DrawAssetItem(UnityEngine.Object asset, string displayName)
        {
            bool clicked = false;
            Rect buttonRect = GUILayoutUtility.GetRect(new GUIContent(displayName), EditorStyles.label);

            // Draw background color based on selection state
            if (asset == selectedObject)
            {
                EditorGUI.DrawRect(buttonRect, new Color(0.24f, 0.49f, 0.90f)); // Updated color for selection
            }
            else if (asset == highlightedObject)
            {
                EditorGUI.DrawRect(buttonRect, new Color(0.5f, 0.5f, 0.5f));
            }

            Texture assetPreview = AssetPreview.GetMiniThumbnail(asset);
            GUIContent content = new GUIContent(displayName, assetPreview);

            // Detect click or double-click
            Event e = Event.current;
            if (buttonRect.Contains(e.mousePosition))
            {
                if (e.type == EventType.MouseMove)
                {
                    if (highlightedObject != asset)
                    {
                        highlightedObject = asset;
                        Repaint();
                    }
                }
                else if (e.type == EventType.MouseDown)
                {
                    if (e.clickCount == 2) // Double-click confirms the selection
                    {
                        selectedObject = asset;
                        onObjectSelected?.Invoke(selectedObject); // Trigger the callback here
                        Close();
                    }
                    else
                    {
                        selectedObject = asset;
                        GUI.changed = true;
                        Repaint();
                    }
                }
            }

            if (GUI.Button(buttonRect, content, EditorStyles.label))
            {
                clicked = true;
            }

            return clicked;
        }

        private void DrawBottomButtons()
        {
            GUILayout.FlexibleSpace();

            EditorGUILayout.BeginVertical(GUI.skin.box);

            // Custom GUIStyle for the path label
            GUIStyle pathStyle = new GUIStyle(GUI.skin.label)
            {
                wordWrap = true,
                fontSize = 10,
                fontStyle = FontStyle.Bold,
            };

            // Path label with a larger text size and wrapping
            if (selectedObject != null)
            {
                string assetPath = AssetDatabase.GetAssetPath(selectedObject);
                GUILayout.Label($"Path: {assetPath}", pathStyle);
            }
            else
            {
                GUILayout.Label("Path: (None)", pathStyle);
            }

            GUILayout.Space(5);

            GUILayout.BeginHorizontal();

            // Increase the size of the buttons and apply a darker background
            GUIStyle buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fixedHeight = 30,
            };

            if (GUILayout.Button("Cancel", buttonStyle))
            {
                Close();
            }

            if (GUILayout.Button("Confirm", buttonStyle))
            {
                onObjectSelected?.Invoke(selectedObject);
                Close();
            }

            GUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        private void DrawSlider()
        {
            GUILayout.BeginHorizontal();
            GUILayout.Label("Icon Size", GUILayout.Width(60));
            iconSize = GUILayout.HorizontalSlider(iconSize, 32, 128);
            GUILayout.EndHorizontal();
        }
    }
}
