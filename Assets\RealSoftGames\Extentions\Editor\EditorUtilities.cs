using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace RealSoftGames
{
    public struct ErrorWithButton
    {
        public string message;
        public int itemID;

        public ErrorWithButton(string message, int itemID)
        {
            this.message = message;
            this.itemID = itemID;
        }
    }

    public static class EditorUtilities
    {
        private static CustomObjectPickerWindow pickerWindow;

        // Generic Object Picker Field for Unity objects
        public static void ObjectPickerField<T>(string label, T selectedObject, bool allowSceneObjects = true, Action<T> onChanged = null) where T : UnityEngine.Object
        {
            EditorGUILayout.BeginHorizontal();

            // Dynamically calculate label width based on the label's content size
            float calculatedLabelWidth = Mathf.Max(GUI.skin.label.CalcSize(new GUIContent(label)).x + 5, 180);
            EditorGUIUtility.labelWidth = calculatedLabelWidth;

            // Use PrefixLabel to automatically handle label placement
            EditorGUILayout.PrefixLabel(label);

            // Handle object field display
            Rect fieldRect = GUILayoutUtility.GetRect(GUIContent.none, EditorStyles.objectField, GUILayout.ExpandWidth(true));

            // Determine content based on whether the object is a sprite or another asset
            string labelContent = selectedObject != null ? selectedObject.name : $"None ({typeof(T).Name})";
            Texture2D objectIcon;

            // Special case for Sprite: display the actual sprite or the default Unity icon if no sprite is selected
            if (selectedObject is Sprite spriteObject)
            {
                objectIcon = spriteObject != null ? spriteObject.texture : AssetPreview.GetMiniThumbnail(EditorGUIUtility.IconContent("sv_icon_dot6_pix16_gizmo").image);
            }
            else
            {
                objectIcon = selectedObject != null
                    ? AssetPreview.GetMiniThumbnail(selectedObject)
                    : EditorGUIUtility.IconContent("sv_icon_dot6_pix16_gizmo").image as Texture2D; // Default Unity icon if no sprite assigned
            }

            // Display the object field with the calculated content
            GUI.Label(fieldRect, new GUIContent(labelContent, objectIcon), EditorStyles.objectField);

            // Custom button style for the object picker button
            GUIStyle buttonStyle = new GUIStyle(GUI.skin.GetStyle("ObjectFieldButton"))
            {
                padding = new RectOffset(1, 0, 0, 0),
                margin = new RectOffset(1, 0, 0, 0),
                alignment = TextAnchor.MiddleCenter
            };

            // Draw the object picker button
            Rect buttonRect = new Rect(fieldRect.xMax - 19, fieldRect.y + (fieldRect.height / 2 - 6.5f), 18, 14);
            if (GUI.Button(buttonRect, "", buttonStyle))
            {
                OpenPickerWindow(typeof(T), selectedObject, allowSceneObjects, newObj =>
                {
                    selectedObject = newObj;
                    onChanged?.Invoke(newObj);
                    GUI.changed = true;
                });
            }

            // End the horizontal group and reset label width
            EditorGUILayout.EndHorizontal();
            EditorGUIUtility.labelWidth = 0;
        }

        // Opens the object picker window with grid or list view depending on asset type
        private static void OpenPickerWindow<T>(Type objectType, T currentSelection, bool allowSceneObjects, Action<T> onChanged) where T : UnityEngine.Object
        {
            if (pickerWindow != null)
            {
                pickerWindow.Close();
                pickerWindow = null; // Clear the reference
            }

            // Show picker window with instance-based callback
            pickerWindow = CustomObjectPickerWindow.Show(objectType, currentSelection, allowSceneObjects, obj =>
            {
                if (obj == null)
                {
                    onChanged?.Invoke(null); // Pass null to the callback
                }
                else
                {
                    T selectedObj = obj as T;
                    if (selectedObj != null)
                    {
                        onChanged?.Invoke(selectedObj);

                        // Use delayed call to ensure GUI updates in sync with the editor window
                        EditorApplication.delayCall += () =>
                        {
                            if (EditorWindow.focusedWindow != null)
                                EditorWindow.focusedWindow.Repaint();
                        };
                    }
                    else
                    {
                        Debug.LogError($"Picker returned an object that could not be cast to {typeof(T)}.");
                    }
                }
            }); // Automatically use grid view for Sprite assets
        }

    /// <summary>
        /// Draws a toolbar at the top of the main body area
        /// </summary>
        /// <param name="rect">The rect to draw the toolbar in</param>
        /// <param name="options">The toolbar options to display</param>
        /// <param name="selectedOption">The currently selected option</param>
        /// <param name="onOptionSelected">Callback when an option is selected</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawToolbar<T>(
            Rect rect,
            IEnumerable<T> options,
            T selectedOption,
            Action<T> onOptionSelected,
            float height = 40f) where T : IConvertible
        {
            // Create a rect for the toolbar at the top of the main body
            Rect toolbarRect = new Rect(
                rect.x,
                rect.y,
                rect.width,
                height);

            // Draw a background for the toolbar
            EditorGUI.DrawRect(toolbarRect, new Color(0.2f, 0.2f, 0.2f, 1f));

            // Create a GUIStyle for the buttons
            GUIStyle buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontStyle = FontStyle.Bold,
                fontSize = 12,
                fixedHeight = height - 4, // Slightly smaller than the toolbar height
                alignment = TextAnchor.MiddleCenter,
                margin = new RectOffset(2, 2, 2, 2)
            };

            // Calculate the remaining rect for the main content
            Rect contentRect = new Rect(
                rect.x,
                rect.y + height,
                rect.width,
                rect.height - height);

            // Begin a horizontal layout for the toolbar
            GUILayout.BeginArea(toolbarRect);
            GUILayout.BeginHorizontal();

            // Store the original background color
            Color originalColor = GUI.backgroundColor;

            // Display buttons for each option
            foreach (T option in options)
            {
                // Set the button color based on whether it's selected
                GUI.backgroundColor = EqualityComparer<T>.Default.Equals(option, selectedOption)
                    ? new Color(0.3f, 0.5f, 0.85f) // Selected color
                    : Color.white; // Deselected color

                // Create the button
                if (GUILayout.Button(option.ToString(), buttonStyle, GUILayout.ExpandWidth(true)))
                {
                    // Call the callback when an option is selected
                    onOptionSelected?.Invoke(option);
                }
            }

            // Restore the original background color
            GUI.backgroundColor = originalColor;

            GUILayout.EndHorizontal();
            GUILayout.EndArea();

            return contentRect;
        }

        /// <summary>
        /// Draws a toolbar at the top of the main body area of an EditorWindowTemplate
        /// </summary>
        /// <param name="options">The toolbar options to display</param>
        /// <param name="selectedOption">The currently selected option</param>
        /// <param name="onOptionSelected">Callback when an option is selected</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawEditorWindowToolbar<T>(
            IEnumerable<T> options,
            T selectedOption,
            Action<T> onOptionSelected,
            float height = 40f) where T : IConvertible
        {
            return DrawToolbar(EditorWindowTemplate.MainBodyRect, options, selectedOption, onOptionSelected, height);
        }

        /// <summary>
        /// Draws a toolbar at the top of the main body area with custom button content
        /// </summary>
        /// <param name="rect">The rect to draw the toolbar in</param>
        /// <param name="drawToolbarContent">Action to draw the toolbar content</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawCustomToolbar(
            Rect rect,
            Action drawToolbarContent,
            float height = 40f)
        {
            // Create a rect for the toolbar at the top of the main body
            Rect toolbarRect = new Rect(
                rect.x,
                rect.y,
                rect.width,
                height);

            // Draw a background for the toolbar
            EditorGUI.DrawRect(toolbarRect, new Color(0.2f, 0.2f, 0.2f, 1f));

            // Calculate the remaining rect for the main content
            Rect contentRect = new Rect(
                rect.x,
                rect.y + height,
                rect.width,
                rect.height - height);

            // Begin a horizontal layout for the toolbar
            GUILayout.BeginArea(toolbarRect);

            // Draw the custom toolbar content
            drawToolbarContent?.Invoke();

            GUILayout.EndArea();

            return contentRect;
        }

        /// <summary>
        /// Draws a toolbar at the top of the main body area of an EditorWindowTemplate with custom button content
        /// </summary>
        /// <param name="drawToolbarContent">Action to draw the toolbar content</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawEditorWindowCustomToolbar(
            Action drawToolbarContent,
            float height = 40f)
        {
            return DrawCustomToolbar(EditorWindowTemplate.MainBodyRect, drawToolbarContent, height);
        }

        /// <summary>
        /// Draws a standard pattern creator toolbar with New, Save, and Reset buttons
        /// </summary>
        /// <param name="rect">The rect to draw the toolbar in</param>
        /// <param name="onNewClicked">Action to perform when New button is clicked</param>
        /// <param name="onSaveClicked">Action to perform when Save button is clicked</param>
        /// <param name="onResetClicked">Action to perform when Reset button is clicked</param>
        /// <param name="onHelpClicked">Action to perform when Help button is clicked</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawStandardToolbar(
            Rect rect,
            Action onNewClicked,
            Action onSaveClicked,
            Action onResetClicked,
            Action onHelpClicked = null,
            float height = 40f)
        {
            return DrawCustomToolbar(rect, () => {
                GUILayout.BeginHorizontal();

                GUIStyle buttonStyle = new GUIStyle(EditorStyles.miniButton)
                {
                    padding = new RectOffset(8, 8, 2, 2),
                    margin = new RectOffset(2, 2, 0, 0),
                    fixedHeight = 24
                };

                if (GUILayout.Button("New Pattern", buttonStyle, GUILayout.Width(100)))
                {
                    onNewClicked?.Invoke();
                }

                if (GUILayout.Button("Save Pattern", buttonStyle, GUILayout.Width(100)))
                {
                    onSaveClicked?.Invoke();
                }

                if (GUILayout.Button("Reset Points", buttonStyle, GUILayout.Width(100)))
                {
                    onResetClicked?.Invoke();
                }

                GUILayout.FlexibleSpace();

                // Display help button if a callback is provided
                if (onHelpClicked != null && GUILayout.Button("?", buttonStyle, GUILayout.Width(20), GUILayout.Height(20)))
                {
                    onHelpClicked.Invoke();
                }

                GUILayout.EndHorizontal();
            }, height);
        }

        /// <summary>
        /// Draws a standard pattern creator toolbar with New, Save, and Reset buttons at the top of an EditorWindowTemplate
        /// </summary>
        /// <param name="onNewClicked">Action to perform when New button is clicked</param>
        /// <param name="onSaveClicked">Action to perform when Save button is clicked</param>
        /// <param name="onResetClicked">Action to perform when Reset button is clicked</param>
        /// <param name="onHelpClicked">Action to perform when Help button is clicked</param>
        /// <param name="height">Height of the toolbar (default: 40)</param>
        /// <returns>The remaining rect for the main content</returns>
        public static Rect DrawEditorWindowStandardToolbar(
            Action onNewClicked,
            Action onSaveClicked,
            Action onResetClicked,
            Action onHelpClicked = null,
            float height = 40f)
        {
            return DrawStandardToolbar(EditorWindowTemplate.MainBodyRect, onNewClicked, onSaveClicked, onResetClicked, onHelpClicked, height);
        }
    }
}
