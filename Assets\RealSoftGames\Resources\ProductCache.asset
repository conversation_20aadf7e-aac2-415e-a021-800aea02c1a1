%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 57ea5d14c0dc44847a4d5fd0db1fdc84, type: 3}
  m_Name: ProductCache
  m_EditorClassIdentifier: 
  cachedProducts:
  - id: 67079e78abaa83f5a6f32544
    Name: Advanced Achievement System
    Description: '<p><strong>Advanced Achievement System</strong>&nbsp;is the ultimate
      drag-and-drop solution for integrating achievements into your game with just
      a few clicks. Designed for ease of use and flexibility, this comprehensive
      system brings a host of powerful features to elevate your game''s achievement
      mechanics.</p><h3><br></h3><h3>Features:</h3><ul><li><strong>Effortless Integration:</strong>
      Achievements can be integrated into your game with a single line of code.</li><li><strong>Seamless
      Compatibility:</strong> Built on top of Unity''s native UGUI and TextMesh Pro.</li><li><strong>Custom
      Editor</strong>: A fully featured custom editor window for creating and managing
      achievements and categories.</li><li><strong>User-Friendly</strong>: Simple
      to use and easy to extend, perfect for developers of all levels.</li><li><strong>Robust
      Save/Load System</strong>: Easily integrate with your current save/load system
      using JSON. Save data to a database or encrypt as needed.</li><li><strong>Advanced
      Search Functionality</strong>: Allow players to search through their achievements
      effortlessly.</li><li><strong>Modern UI Design</strong>: Sleek and contemporary
      interface to match the aesthetic of your game.</li><li><strong>Achievement
      Completion Cue</strong>: Avoid screen clutter by managing the display of multiple
      completed achievements.</li><li><strong>Hidden Achievements</strong>: Surprise
      players with hidden achievements that reveal themselves upon completion.</li><li><strong>Dependency
      Management</strong>: Require the completion of specific achievements before
      starting progress on others.</li><li><strong>Theme Template Generation</strong>:
      Simplified theme creation through the enhanced editor interface.</li><li><strong>Pre-Packaged
      Themes</strong>: Includes three theme packs ready for one-click installation.</li><li><strong>Reward
      System</strong>: Grant players rewards upon achievement completion, enhancing
      engagement and player satisfaction.</li></ul>'
    Url: https://simmer.io/@Krazor/advanced-achievement-system
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/gui/advanced-achievement-system-138806
    Price: 30
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/67079e78abaa83f5a6f32544/image_1728552525681_1200x630.jpg
    Category: Asset
    IsFeatured: 1
  - id: 6707a078abaa83f5a6f32555
    Name: Advanced Leveling System
    Description: <p>Unlock unparalleled progression dynamics in your game with our
      Advanced Leveling System, meticulously tailored for Unity. Integrate this robust
      system effortlessly and watch as your players engage with a depth of progression
      that keeps them coming back for more. Choose from an array of algorithms to
      define how they level up and experience the journey of growth within your game
      world.</p><p><br></p><p><br></p><h3><strong>Seamless Integration</strong></h3><ul><li><strong>Drag-and-Drop
      Prefabs:</strong>&nbsp;Swiftly add our system into your scene with a simple
      prefab.</li><li><strong>Adaptive Settings:</strong>&nbsp;Tailor the progression
      experience to suit your game's unique feel and design.</li><li><strong>One-Line
      Code Integration:</strong>&nbsp;Integrate our advanced system into your project
      with a single line of code for a hassle-free setup.</li><li><strong>Optimized
      Floating Text Object Pool:</strong>&nbsp;Elevate your UI with an efficient
      and optimized object pool, ensuring smooth and responsive in-game text effects.</li></ul><p><br></p><h3><strong>Rich
      Experience Management</strong></h3><ul><li><strong>Power-Up Demo:</strong>&nbsp;Discover
      the power of progression with our new demo showcasing how to reward players
      with selective power-ups upon leveling up.</li><li><strong>Code Snippets for
      XP Gains:</strong>&nbsp;Integrate experience rewards into your gameplay with
      minimal coding, thanks to our easy-to-use snippets.</li><li><strong>Save and
      Load with Ease:</strong>&nbsp;Maintain player progress with our intuitive save/load
      functionality.</li></ul><p><br></p><h3><strong>Dynamic Event-Driven Feedback</strong></h3><ul><li><strong>Real-Time
      Response:</strong>&nbsp;Events such as&nbsp;<strong>OnExpModified</strong>&nbsp;and&nbsp;<strong>OnLevelUp</strong>&nbsp;keep
      the feedback loop engaging.</li><li><strong>Customizable Events:</strong>&nbsp;Leverage
      events for nuanced interactions and player recognition.</li></ul><p><br></p><h3><strong>Leveling
      Algorithms Galore</strong></h3><ul><li><strong>Linear:</strong>&nbsp;Predictable
      and steady growth.</li><li><strong>Sine Wave:</strong>&nbsp;Cyclical progression
      for varied pacing.</li><li><strong>Exponential:</strong>&nbsp;Accelerated growth
      for quick advancements.</li><li><strong>Quadratic:</strong>&nbsp;A moderate
      curve that intensifies over time.</li><li><strong>Logarithmic:</strong>&nbsp;Rapid
      early progression that slows at higher levels.</li><li><strong>Square Root:</strong>&nbsp;Gradual
      difficulty increase for a balanced climb.</li><li><strong>Fibonacci Sequence:</strong>&nbsp;A
      natural and organic growth pattern.</li><li><strong>Geometric Progression:</strong>&nbsp;Multiplicative
      growth for steep progression.</li><li><strong>Cubic:</strong>&nbsp;A substantial
      curve for deep, engaging leveling.</li><li><strong>Custom:</strong>&nbsp;Craft
      your own unique formula to match your game's philosophy.</li></ul><p><br></p><h3><strong>Immersive
      Audiovisual Experience</strong></h3><ul><li><strong>Celebratory Sounds:</strong>&nbsp;Mark
      leveling milestones with impactful audio.</li><li><strong>Transition Control:</strong>&nbsp;Fine-tune
      feedback timing for a tailored player experience.</li></ul><p><br></p><h3><strong>Robust
      UI Toolkit</strong></h3><ul><li><strong>LevelingSystemManager.cs:</strong>&nbsp;A
      comprehensive UI manager for all your leveling display needs.</li><li><strong>FloatingText.cs
      for Visuals:</strong>&nbsp;Customize the feedback with adjustable visual parameters
      for text effects.</li><li><br></li></ul><h3><strong>Demo Scenario for Quick
      Learning</strong></h3><ul><li><strong>Real-World Application:</strong>&nbsp;Explore
      our system's potential in an AI scenario, complete with practical event implementations,
      emphasizing events like OnHealthChange and OnDeath while intelligently preventing
      redundant calls at maximum levels.</li></ul><p><br></p>
    Url: https://simmer.io/@Krazor/advanced-leveling-system
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/gui/advanced-leveling-system-274373
    Price: 15
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/6707a078abaa83f5a6f32555/image_1728553071158_2400-1600-max.jpg
    Category: Asset
    IsFeatured: 1
  - id: 6707a181abaa83f5a6f3259c
    Name: Icon Architect Studio
    Description: "<h3><strong>Icon Architect Studio: Transform Your 3D Models into
      Stunning 2D Icons on Unity Asset Store</strong></h3><p><br></p><p>Unlock the
      full potential of your 3D models with Icon Architect Studio, the ultimate Unity
      tool for seamlessly converting 3D models into eye-catching 2D icons. Ideal
      for game developers, graphic designers, and digital artists, Icon Architect
      Studio offers a comprehensive suite of features to elevate your projects and
      streamline your workflow.</p><p><br></p><h2>Core Features</h2><p><br></p><ul><li><strong>Versatile
      Editing Controls</strong>:</li><li class=\"ql-indent-1\">Effortlessly rotate,
      move, and zoom models with intuitive mouse controls, offering a natural and
      responsive user interface.</li><li><strong>Intuitive Preview Window</strong>:</li><li
      class=\"ql-indent-1\">Make real-time adjustments and see immediate results,
      ensuring your icons look perfect before final rendering.</li><li><strong>Dynamic
      Background Options</strong>:</li><li class=\"ql-indent-1\">Create visually
      striking icons with customizable background colors, tints, and images. Use
      unique shapes like circles and triangles, with options for transparency.</li><li><strong>Advanced
      Overlay and Border Customization</strong>:</li><li class=\"ql-indent-1\">Enhance
      your icons with overlays that can be precisely positioned and scaled, perfect
      for creating custom borders and adding stylistic flair.</li><li><strong>Animation
      and Pose Control</strong>:</li><li class=\"ql-indent-1\">Select and apply specific
      keyframes from animation clips, bringing dynamism to your icons and creating
      detailed renders for wallpapers or loading screens.</li><li><strong>Advanced
      Lighting Controls</strong>:</li><li class=\"ql-indent-1\">Adjust lighting settings,
      including mode, angle, color, and intensity, to highlight the best features
      of your models.</li><li><strong>High-Resolution Rendering</strong>:</li><li
      class=\"ql-indent-1\">Render icons from 64x64 to an impressive 8k resolution,
      ensuring sharp and professional icons on all devices.</li><li><strong>Sub-Model
      Management</strong>:</li><li class=\"ql-indent-1\">Enable or disable sub-models
      within a prefab for cleaner and more relevant icon outcomes.</li><li><strong>Axis
      Constraints for Rotation and Dragging</strong>:</li><li class=\"ql-indent-1\">Utilize
      constraints for X, Y, and Z axes when moving the model or camera based on key
      press combinations.</li><li><strong>Finer Adjustment Controls</strong>:</li><li
      class=\"ql-indent-1\">Enhanced control for large objects like buildings and
      complex prefabs.</li><li><strong>Center Model Button</strong>:</li><li class=\"ql-indent-1\">Easily
      center models with a single click, replacing auto-centering.</li><li><strong>Camera
      &amp; Model Controls</strong>:</li><li class=\"ql-indent-1\">Seamlessly control
      the position and rotation of either the model or the camera.</li><li><strong>Changeable
      Hotkeys</strong>:</li><li class=\"ql-indent-1\">Customize hotkeys in the global
      settings tab for a personalized workflow.</li><li><strong>Camera Clear Flags</strong>:</li><li
      class=\"ql-indent-1\">Added clear flags for rendering larger models.</li><li><strong>Skybox
      Options</strong>:</li><li class=\"ql-indent-1\">Include skyboxes for rendering
      expansive complex prefabs.</li><li><strong>Preview Window Label</strong>:</li><li
      class=\"ql-indent-1\">A new label indicates the key combination and action
      being taken above the preview window.</li><li><strong>Global Settings Save</strong>:</li><li
      class=\"ql-indent-1\">Save global settings to editor preferences, including
      sensitivity preferences, for a consistent experience.</li><li><strong>Undo
      and Redo Feature:</strong>&nbsp;</li><li class=\"ql-indent-1\">Undo and Redo
      functionality, allowing you to effortlessly revert and apply changes made to
      models, camera, global settings, and overlays. Maintain full control over your
      editing process and ensure precision in every step.</li></ul><p><br></p><p><br></p><h2>Why
      Icon Architect Studio?</h2><p><br></p><p>Icon Architect Studio is more than
      just a conversion tool\u2014it's a powerful asset that saves time and boosts
      creativity. Its user-friendly interface and robust feature set make it essential
      for anyone looking to elevate their visual assets. Produce high-quality, bespoke
      icons that capture the essence of your project and enhance user engagement
      with Icon Architect Studio.</p>"
    Url: 
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/utilities/icon-architect-studio-281744
    Price: 30
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/6707a181abaa83f5a6f3259c/image_1728553263512_1950-1300-max.jpg
    Category: Asset
    IsFeatured: 1
  - id: 670784e1271877bd78608fa2
    Name: Advanced Loading Screen
    Description: "<h2><strong>Advanced Loading Screen for Unity</strong></h2><p><br></p><p>Transform
      your game's loading experience with the Advanced Loading Screen, the ultimate
      drag-and-drop solution designed for Unity developers. Whether you're working
      on a simple project or a complex game, this asset provides everything you need
      to create captivating and efficient loading screens with minimal effort.</p><p><br></p><h3><strong>Seamless
      Integration and Customization</strong></h3><p><br></p><ul><li><strong>Easy
      Setup</strong>: With just one line of code, integrate ready-made prefabs or
      use our intuitive menus to design custom loading screens tailored to your game.</li><li><strong>Flexible
      Loading Types</strong></li><li><strong>Asynchronous Loading</strong>: Enhance
      performance and user experience by loading scenes asynchronously.</li><li><strong>Fake
      Loading System</strong>: Use customizable animation curves for a simulated
      loading effect.</li><li><strong>Additive &amp; Additive Merge Loading</strong>:
      Unique to our asset, seamlessly load multiple scenes together.</li></ul><p><br></p><h3><strong>Comprehensive
      Feature Set</strong></h3><p><br></p><ul><li><strong>Platform Support</strong>:
      Compatible with Windows, Mac, Linux, and Mobile platforms, ensuring broad usability.</li><li><strong>Built
      on UGUI</strong>: Leverage Unity\u2019s native UGUI for robust and flexible
      UI development.</li><li><strong>TextMesh Pro Support</strong>: Achieve crisp
      and clear text with full TextMesh Pro integration.</li><li><strong>Custom Editors
      and Source Code</strong>: Modify and extend functionality with included source
      code and custom editors.</li><li><strong>Developer Logs</strong>: Simplify
      debugging with comprehensive developer logs.</li></ul><p><br></p><h3><strong>Advanced
      Functionality</strong></h3><p><br></p><ul><li><strong>Custom Audio Controls</strong>:
      Tailor your loading screen's audio experience with our custom-coded audio controls.</li><li><strong>Tip
      Display System</strong>: Keep players engaged with random or timed game tips
      during loading.</li><li><strong>Dynamic Loading Indicators</strong>: Choose
      from loading bars, wheels, and text systems.</li><li><strong>Visual Effects</strong>:
      Implement fade and transition effects, background systems, and spinners.</li><li><strong>Responsive
      Input Handling</strong>: Supports input for both PC and mobile devices.</li><li><strong>Pulsing
      Text and Overlays</strong>: Add visual interest with pulsing text and included
      overlay effects.</li><li><strong>Extensive Templates</strong>: Over 10 drag-and-drop
      templates to jumpstart your loading screen design.</li></ul><p><br></p><h3><strong>Fine-Tune
      Every Detail</strong></h3><p><br></p><ul><li><strong>Drag-and-Drop Templates</strong>:
      Quickly create beautiful loading screens using our pre-designed templates.</li><li><strong>Complete
      Control</strong>: Adjust and fine-tune each element to perfectly match your
      game\u2019s aesthetics and requirements.</li></ul><p><br></p><h3><strong>Dedicated
      Support</strong></h3><p><br></p><ul><li>Have questions or need assistance?
      We\u2019re here to help!</li></ul><p><br></p><p><br></p><p>Enhance your game's
      user experience with the Advanced Loading Screen \u2013 the most versatile
      and feature-rich loading screen solution available on the Unity Asset Store.
      Create, customize, and captivate with ease!</p>"
    Url: https://simmer.io/@realsoftgames/advanced-loading-screen
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/gui/advanced-loading-screen-135956
    Price: 15
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/670784e1271877bd78608fa2/image_1728545986445_1950x1300.png
    Category: Asset
    IsFeatured: 0
  - id: 67079effabaa83f5a6f3254d
    Name: Spawner Advanced & Pooling
    Description: '<p>This comprehensive system is engineered to streamline the spawning
      process for a multitude of game genres, including FPS, RPG, RTS, and more.
      The possibilities are boundless. If you''re seeking additional functionalities
      or have ideas for improvement, we warmly invite you to share your suggestions
      in our Discord community''s suggestions tab for potential inclusion in future
      updates.</p><p><br></p><h3>Features</h3><ul><li><strong>Enhanced Pooling System</strong>:
      Our newly introduced pooling system optimizes performance by efficiently reusing
      game objects, making it an essential tool for resource management across all
      types of games.</li><li><strong>Versatile Spawn Methods</strong>: Benefit from
      an expanded arsenal of spawn methods, allowing for both random and precision-based
      object spawning to suit any scenario or gameplay mechanic.</li><li><strong>Configurable
      Wave Spawning</strong>: Tailor the initial spawn delay on a per-wave basis
      with our configurable settings, perfect for designing precise spawn sequences
      or simulating instant respawn times for objects.</li><li><strong>Interface-Driven
      Pool Configuration</strong>: Utilize the interface for our pooling system to
      customize object pools to your project''s specific needs, ensuring optimal
      performance and gameplay flow.</li><li><strong>State Reset via OnSpawn Event</strong>:
      Automatically reset your prefab''s state upon reuse from the pool, maintaining
      consistency and quality in the gameplay experience.</li><li><strong>Simplified
      Wave Setup</strong>: Quickly configure waves to spawn in a randomized manner
      or according to preset arrangements, offering both simplicity and complexity
      in your spawning logic.</li><li><strong>Infinite Spawning Capabilities</strong>:
      Easily establish an endless spawner for continuous gameplay dynamics, complete
      with full C# code annotations for clarity.</li><li><strong>Dynamic Enemy Spawning</strong>:
      Spawn adversaries at designated points, randomly, or even introduce a boss
      character that summons additional enemies, enriching the player''s challenge.</li><li><strong>Spawn
      Management</strong>: Monitor active units per sub-spawner, halt enemy spawns
      until the current pool is cleared, and leverage our pause/resume feature for
      granular control over the spawning process.</li><li><strong>Spawn Timing and
      Activation</strong>: Customize the timing between spawns and activate spawners
      with a delay or through in-game interactions, accompanied by callbacks for
      completed spawners.</li></ul><p><br></p><p>Designed to cater to both 2D and
      3D gaming environments, this package offers a spectrum of features from basic
      to advanced. Some programming knowledge may be beneficial to fully leverage
      the extensive capabilities of this system. Should you require assistance or
      have specific inquiries, do not hesitate to reach out. Your satisfaction and
      project success are paramount to us.</p><p><br></p><h3><strong>Unsure About
      Compatibility?</strong></h3><p><br></p><p>For any queries or feature requests,
      feel free to contact us via email or join our vibrant Discord community. Your
      feedback and needs are instrumental in shaping the continuous evolution of
      our offerings.</p>'
    Url: 
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/behavior-ai/spawner-advanced-pooling-35287
    Price: 15
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/67079effabaa83f5a6f3254d/image_1728935165016_1000055684.jpg
    Category: Asset
    IsFeatured: 0
  - id: 67079f4babaa83f5a6f32551
    Name: Unit Selection
    Description: '<p>Upgrade your game with our Simple Unit Selection package, perfect
      for RTS-style selection. Easily select single or multiple units without hassle.
      The code is concise and well-documented, making integration a breeze. No coding
      required - it''s ready to use out of the box!</p><p><br></p><p>Included is
      a 3D example scene and demo, complete with models to help you quickly prototype
      your gameplay.</p><p><br></p><p>Experience our powerful click and drag features:
      clicking on a unit selects it, while clicking elsewhere deselects all units.
      Dragging the mouse selects units in the designated area based on their unit
      ID, perfect for handling factions and selectable units in your game.</p><p>Take
      advantage of the Shift hotkey features: Shift+drag to add units within the
      selection area to the current selection, and Shift+click to toggle unit selection.</p><p><br></p><p>Enhance
      your game''s selection system and elevate your gameplay. Get started today
      with our Simple Unit Selection package!</p><p><br></p><h3><strong>Features:</strong></h3><ul><li><strong>click
      on unit will select it and clicking on no unit will deselect all units</strong></li><li><strong>drag
      mouse will select all units in selection area based on their unit ID (Think
      of this as having other factions or selectable units in the game)</strong></li><li><strong>shift+drag
      mouse will add all units in selection area to current selection</strong></li><li><strong>shift+click
      on unit will toggle unit selection (select/deselect)</strong></li><li><strong>1x
      example scene included 3D</strong></li><li><strong>enables single or multiple
      unit selection</strong></li><li><strong>Drag &amp; Drop Prefabs</strong></li><li><strong>Customizable
      Highlight box colors</strong></li></ul>'
    Url: https://simmer.io/@realsoftgames/unit-selection
    AssetStoreUrl: https://assetstore.unity.com/packages/tools/input-management/unit-selection-259889
    Price: 10
    IconUrl: https://minioapi.realsoftgames.com/realsoftgames/products/67079f4babaa83f5a6f32551/image_1728552772529_UnitSelection.jpg
    Category: Asset
    IsFeatured: 0
