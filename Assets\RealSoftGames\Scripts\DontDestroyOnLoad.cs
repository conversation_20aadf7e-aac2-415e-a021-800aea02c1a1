using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames
{
    public class DontDestroyOnLoad : MonoBehaviour
    {
        // Static list to store all persisted components (not GameObjects)
        private static List<Component> persistentComponents = new List<Component>();

        // Optional: A flag to determine if this specific object should persist
        [SerializeField] private bool persistAcrossScenes = true;

        // A serialized field that will allow you to specify which component type should be checked for persistence.
        [SerializeField, Tooltip("Specify which component should be checked for persistence (e.g., Notification, AchievementDisplay).")]
        private Component componentToPersist;


        public bool PersistAcrossScenes { get => persistAcrossScenes; set => persistAcrossScenes = value; }


        private void Awake()
        {
            if (persistAcrossScenes)
            {
                HandlePersistence();
            }
        }

        private void OnDestroy()
        {
            // Remove the component from the list if it's being destroyed
            if (componentToPersist != null && persistentComponents.Contains(componentToPersist))
            {
                persistentComponents.Remove(componentToPersist);
            }
        }

        /// <summary>
        /// Handles persistence and prevents multiple instances of the same component type.
        /// </summary>
        private void HandlePersistence()
        {
            // Check if a component of the same type already exists in the persistent components list
            foreach (var component in persistentComponents)
            {
                if (component != null && component.GetType() == componentToPersist.GetType())
                {
                    // If a component of the same type already exists, destroy the new instance
                    //Debug.LogWarning($"Duplicate persistent component '{componentToPersist.GetType().Name}' detected. Destroying the new instance and keeping the existing one.");
                    Destroy(this.gameObject);
                    return; // Exit early as the new object is being destroyed
                }
            }

            // If the component doesn't exist in the list, add it and make the GameObject persistent
            DontDestroyOnLoad(this.gameObject);
            persistentComponents.Add(componentToPersist);
            //Debug.Log($"{componentToPersist.GetType().Name} is now persistent and added to the list.");
        }

        /// <summary>
        /// Utility function to clear the list manually if needed.
        /// </summary>
        public static void ClearPersistentComponents()
        {
            persistentComponents.Clear();
        }

        /// <summary>
        /// Optionally check if a specific component is persistent.
        /// </summary>
        public static bool IsComponentPersistent(Component component)
        {
            return persistentComponents.Contains(component);
        }
    }
}
