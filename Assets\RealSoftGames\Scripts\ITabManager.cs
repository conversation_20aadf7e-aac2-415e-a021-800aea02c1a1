namespace RealSoftGames
{
    /// <summary>
    /// Interface for tab managers used in editor windows
    /// </summary>
    public interface ITabManager
    {
        /// <summary>
        /// Gets the display name of the tab
        /// </summary>
        string TabName { get; }

        /// <summary>
        /// Called when the tab manager is enabled
        /// </summary>
        void OnEnable();

        /// <summary>
        /// Called when the tab manager is disabled
        /// </summary>
        void OnDisable();

        /// <summary>
        /// Called when the tab manager is destroyed
        /// </summary>
        void OnDestroy();

        /// <summary>
        /// Called every frame to update the tab manager
        /// </summary>
        void Update();

        /// <summary>
        /// Called to draw the tab manager's GUI
        /// </summary>
        void OnGUI();
    }
}
