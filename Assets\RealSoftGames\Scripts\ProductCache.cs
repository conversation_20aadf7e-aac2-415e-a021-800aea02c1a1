using UnityEngine;
using System;
using System.Collections.Generic;

namespace RealSoftGames
{
    /// <summary>
    /// ScriptableObject to store cached products for the About tab
    /// </summary>
    public class ProductCache : ScriptableObject
    {
        // Using the same ProductInfo class as defined in AboutTabManager
        // This ensures compatibility with the existing code
        [Serializable]
        public class ProductInfo
        {
            public string id;
            public string Name;
            public string Description;
            public string Url; // Regular URL
            public string AssetStoreUrl; // Unity Asset Store URL
            public float Price;
            public string IconUrl;
            public string Category;
            public bool IsFeatured;
            public DateTime LastUpdated;
            
            // Non-serialized fields for runtime use
            [NonSerialized] public Texture2D Icon;
            [NonSerialized] public bool IsLoadingIcon;
            [NonSerialized] public Dictionary<string, Vector2> scrollPositions = new Dictionary<string, Vector2>();
        }

        public List<ProductInfo> cachedProducts = new List<ProductInfo>();
        public DateTime lastFetchTime;
    }
}
