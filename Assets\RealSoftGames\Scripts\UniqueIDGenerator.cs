using System.Collections.Generic;
using System;
using System.Linq;

namespace RealSoftGames
{
    public static class UniqueIDManager
    {
        private static Dictionary<Type, HashSet<int>> usedIDs = new Dictionary<Type, HashSet<int>>();
        private static Dictionary<Type, Queue<int>> recycledIDs = new Dictionary<Type, Queue<int>>();

        // Method to generate IDs for both assets and in-memory objects
        public static int GenerateID<T>(IEnumerable<T> existingItems = null) where T : IHasID
        {
            var type = typeof(T);

            // Initialize tracking if not already done
            if (!usedIDs.ContainsKey(type))
            {
                usedIDs[type] = new HashSet<int>();
                recycledIDs[type] = new Queue<int>();
            }

            // If existingItems is provided, initialize from list
            if (existingItems != null)
            {
                InitializeFromList(existingItems.ToList());
            }

            // Recycle old IDs if available
            if (recycledIDs[type].Count > 0)
            {
                var recycledID = recycledIDs[type].Dequeue();
                usedIDs[type].Add(recycledID);
                return recycledID;
            }

            // Otherwise, assign the next available ID
            int nextID = usedIDs[type].Count > 0 ? usedIDs[type].Max() + 1 : 0;
            usedIDs[type].Add(nextID);

            return nextID;
        }

        // Automatically fill in recycled IDs from the provided list
        public static void InitializeFromList<T>(List<T> items) where T : IHasID
        {
            var type = typeof(T);

            // Clear previous records
            usedIDs[type].Clear();
            recycledIDs[type].Clear();

            // Add existing IDs to the used list
            foreach (var item in items)
            {
                usedIDs[type].Add(item.ID);
            }

            // Identify gaps in IDs and add to recycle queue
            int expectedID = 0;
            for (int id = 0; id <= usedIDs[type].Max(); id++)
            {
                if (!usedIDs[type].Contains(id))
                {
                    recycledIDs[type].Enqueue(id);
                }
            }
        }

        // Extension method to generate a new ID for copy-pasted items
        public static void GenerateNewID<T>(this IHasID item, IEnumerable<T> existingItems = null) where T : IHasID
        {
            var newID = GenerateID(existingItems);
            typeof(T).GetProperty("ID")?.SetValue(item, newID);
        }

    }

    // Ensure all scriptable objects that use IDs implement this interface
    public interface IHasID
    {
        int ID { get; }
    }
}
