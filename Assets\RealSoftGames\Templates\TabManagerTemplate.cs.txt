using UnityEditor;
using UnityEngine;

// Tab Manager Template
// This template creates a new tab manager for editor windows.
// After creating this script, you need to register it programmatically in your
// editor window's OnEnable method using the RegisterTabManager method.

namespace RealSoftGames
{
    public class #SCRIPTNAME# : ITabManager
    {
        private readonly EditorWindowTemplate window;
        private GUIStyle headerStyle;
        private Vector2 scrollPosition;

        // Tab properties
        public string TabName => "#SCRIPTNAME#";

        public #SCRIPTNAME#(EditorWindowTemplate window)
        {
            this.window = window;
        }

        public void OnEnable()
        {
            InitializeStyles();
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
            // Clean up resources if needed
        }

        public void Update()
        {
            // Update logic if needed
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }
        }

        public void OnGUI()
        {
            InitializeStyles();

            // Example of using the toolbar - uncomment and customize as needed
            /*
            // Use the standard toolbar from EditorUtilities
            Rect contentRect = EditorUtilities.DrawEditorWindowStandardToolbar(
                // New button
                () => {
                    Debug.Log("New button clicked");
                },
                // Save button
                () => {
                    Debug.Log("Save button clicked");
                },
                // Reset button
                () => {
                    Debug.Log("Reset button clicked");
                },
                // Help button
                () => {
                    Debug.Log("Help button clicked");
                }
            );

            // Begin the main content area
            GUILayout.BeginArea(contentRect);
            */

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.LabelField("#SCRIPTNAME#", headerStyle);
            EditorGUILayout.Space(10);

            // Main content with scrollview
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // TODO: Add your tab content here
            EditorGUILayout.HelpBox("This is a new tab manager. Add your content here.", MessageType.Info);

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();

            // If using the toolbar, uncomment this line
            // GUILayout.EndArea();
        }
    }
}
