{"keyValuePairs": [{"Key": "Level", "ValueType": "System.Int32", "Value": "2"}, {"Key": "CurExp", "ValueType": "System.Int32", "Value": "55"}, {"Key": "ReqExp", "ValueType": "System.Int32", "Value": "287"}, {"Key": "MaxLevel", "ValueType": "System.Int32", "Value": "30"}, {"Key": "LevelUpMultiplier", "ValueType": "System.Single", "Value": "4.2"}, {"Key": "LevelingDifficulty", "ValueType": "System.String", "Value": "SineWave"}, {"Key": "PlayerLevelUpAudio", "ValueType": "System.Boolean", "Value": "True"}]}