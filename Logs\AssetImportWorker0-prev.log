Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.62f1 (4af31df58517) revision 4911901'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 32560 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.62f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Projects/levelingsystem
-logFile
Logs/AssetImportWorker0.log
-srvPort
55854
Successfully changed project path to: D:/Projects/levelingsystem
D:/Projects/levelingsystem
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23188]  Target information:

Player connection [23188]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1882769754 [EditorId] 1882769754 [Version] 1048832 [Id] WindowsEditor(7,Krazor) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23188] Host joined multi-casting on [***********:54997]...
Player connection [23188] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 6.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Projects/levelingsystem/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 Ti Laptop GPU (ID=0x2420)
    Vendor:   NVIDIA
    VRAM:     16175 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56988
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.012480 seconds.
- Loaded All Assemblies, in  0.400 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 218 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.547 seconds
Domain Reload Profiling: 946ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (176ms)
		LoadAssemblies (116ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (170ms)
				TypeCache.ScanAssembly (150ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (548ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (343ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (103ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.629 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.711 seconds
Domain Reload Profiling: 1339ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (404ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (153ms)
				TypeCache.ScanAssembly (137ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 4.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3725 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (55.6 KB). Loaded Objects now: 4195.
Memory consumption went from 138.1 MB to 138.1 MB.
Total: 5.085200 ms (FindLiveObjects: 0.322300 ms CreateObjectMapping: 0.244800 ms MarkObjects: 4.359100 ms  DeleteObjects: 0.157200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 71437.393985 seconds.
  path: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Extensions/AsymptoticAlgorithmExtension.cs
  artifactKey: Guid(14aaf87ff8e133a4894a2a18cc4429da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Extensions/AsymptoticAlgorithmExtension.cs using Guid(14aaf87ff8e133a4894a2a18cc4429da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '599db56442619dcc578eeeb183e8639f') in 0.005241 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.484 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.947 seconds
Domain Reload Profiling: 1430ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (46ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 3.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3660 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4200.
Memory consumption went from 133.1 MB to 133.1 MB.
Total: 5.037600 ms (FindLiveObjects: 0.333300 ms CreateObjectMapping: 0.359300 ms MarkObjects: 4.253400 ms  DeleteObjects: 0.090200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.466 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.047 seconds
Domain Reload Profiling: 1512ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (238ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (23ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1047ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (53ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 3.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4211.
Memory consumption went from 133.4 MB to 133.3 MB.
Total: 5.273800 ms (FindLiveObjects: 0.338300 ms CreateObjectMapping: 0.272200 ms MarkObjects: 4.512900 ms  DeleteObjects: 0.149000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.503 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.949 seconds
Domain Reload Profiling: 1451ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (266ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (513ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (49ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4222.
Memory consumption went from 133.4 MB to 133.4 MB.
Total: 4.947400 ms (FindLiveObjects: 0.350000 ms CreateObjectMapping: 0.285000 ms MarkObjects: 4.244700 ms  DeleteObjects: 0.066500 ms)

Prepare: number of updated asset objects reloaded= 20
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.444 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in 17.435 seconds
Domain Reload Profiling: 20879ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (193ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (3063ms)
		LoadAssemblies (3122ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (17436ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (38ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4233.
Memory consumption went from 133.4 MB to 133.4 MB.
Total: 3.318300 ms (FindLiveObjects: 0.202100 ms CreateObjectMapping: 0.119900 ms MarkObjects: 2.950200 ms  DeleteObjects: 0.045100 ms)

Prepare: number of updated asset objects reloaded= 44
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.709 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.314 seconds
Domain Reload Profiling: 2020ms
	BeginReloadAssembly (205ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (395ms)
		LoadAssemblies (480ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1314ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (678ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (59ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 3.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4244.
Memory consumption went from 133.4 MB to 133.4 MB.
Total: 5.860600 ms (FindLiveObjects: 0.434800 ms CreateObjectMapping: 0.189700 ms MarkObjects: 5.151600 ms  DeleteObjects: 0.082800 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 31d2927f71f8786c370e5b2610844fae -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.498 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.003 seconds
Domain Reload Profiling: 1500ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (269ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1003ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 3.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4255.
Memory consumption went from 133.4 MB to 133.4 MB.
Total: 5.302100 ms (FindLiveObjects: 0.365400 ms CreateObjectMapping: 0.238000 ms MarkObjects: 4.620600 ms  DeleteObjects: 0.076300 ms)

Prepare: number of updated asset objects reloaded= 43
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 31d2927f71f8786c370e5b2610844fae -> 892a5ebdfeba90f0e3ecd19484569cc8
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 50e3f0ae074e769280cb5239584b1949 -> 3cea4e3f00cc39ba50dd051a8d1d6748
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.484 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.101 seconds
Domain Reload Profiling: 1584ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (266ms)
		LoadAssemblies (313ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1102ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (390ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 3.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (30.0 KB). Loaded Objects now: 4266.
Memory consumption went from 133.5 MB to 133.4 MB.
Total: 5.885800 ms (FindLiveObjects: 0.334100 ms CreateObjectMapping: 0.333600 ms MarkObjects: 5.131000 ms  DeleteObjects: 0.085100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 31d2927f71f8786c370e5b2610844fae -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.503 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.006 seconds
Domain Reload Profiling: 1508ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (326ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1007ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (526ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 4.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4278.
Memory consumption went from 133.5 MB to 133.5 MB.
Total: 5.294800 ms (FindLiveObjects: 0.391000 ms CreateObjectMapping: 0.228400 ms MarkObjects: 4.593300 ms  DeleteObjects: 0.080800 ms)

Prepare: number of updated asset objects reloaded= 5
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 8b7b9f602d4083671f6d7e1b0d897019 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.577 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.098 seconds
Domain Reload Profiling: 1674ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (311ms)
		LoadAssemblies (379ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1098ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (393ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 3.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4289.
Memory consumption went from 133.5 MB to 133.5 MB.
Total: 6.152100 ms (FindLiveObjects: 0.393100 ms CreateObjectMapping: 0.308700 ms MarkObjects: 5.371100 ms  DeleteObjects: 0.077400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 8b7b9f602d4083671f6d7e1b0d897019 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.656 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.212 seconds
Domain Reload Profiling: 1864ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (337ms)
		LoadAssemblies (416ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1212ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (630ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (53ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 4.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (30.0 KB). Loaded Objects now: 4300.
Memory consumption went from 133.5 MB to 133.5 MB.
Total: 5.779800 ms (FindLiveObjects: 0.393100 ms CreateObjectMapping: 0.276600 ms MarkObjects: 4.405900 ms  DeleteObjects: 0.702300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: ec9a21e3abba0fee7df04997250c2731 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.518 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.079 seconds
Domain Reload Profiling: 1595ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (271ms)
		LoadAssemblies (327ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1079ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 4.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4311.
Memory consumption went from 133.5 MB to 133.5 MB.
Total: 5.722800 ms (FindLiveObjects: 0.437400 ms CreateObjectMapping: 0.287700 ms MarkObjects: 4.894100 ms  DeleteObjects: 0.102100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: ec9a21e3abba0fee7df04997250c2731 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.531 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.055 seconds
Domain Reload Profiling: 1586ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (289ms)
		LoadAssemblies (345ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1055ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (553ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (46ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4322.
Memory consumption went from 133.5 MB to 133.5 MB.
Total: 6.873300 ms (FindLiveObjects: 0.555300 ms CreateObjectMapping: 0.388600 ms MarkObjects: 5.767400 ms  DeleteObjects: 0.160300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: ec9a21e3abba0fee7df04997250c2731 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.632 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.927 seconds
Domain Reload Profiling: 1557ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (330ms)
		LoadAssemblies (410ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (928ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 3.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4333.
Memory consumption went from 133.6 MB to 133.5 MB.
Total: 5.556600 ms (FindLiveObjects: 0.336700 ms CreateObjectMapping: 0.235000 ms MarkObjects: 4.900700 ms  DeleteObjects: 0.082800 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 8b7b9f602d4083671f6d7e1b0d897019 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.627 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.057 seconds
Domain Reload Profiling: 1682ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (322ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1058ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (557ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (386ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 3.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4344.
Memory consumption went from 133.6 MB to 133.5 MB.
Total: 5.898500 ms (FindLiveObjects: 0.590700 ms CreateObjectMapping: 0.431800 ms MarkObjects: 4.797100 ms  DeleteObjects: 0.077300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 8b7b9f602d4083671f6d7e1b0d897019 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.604 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.182 seconds
Domain Reload Profiling: 1785ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (335ms)
		LoadAssemblies (406ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1184ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (695ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 5.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4355.
Memory consumption went from 133.6 MB to 133.6 MB.
Total: 22.082700 ms (FindLiveObjects: 0.386200 ms CreateObjectMapping: 0.374600 ms MarkObjects: 21.171500 ms  DeleteObjects: 0.148400 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 04cdd4d205084457039c2607b9557639 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.595 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.134 seconds
Domain Reload Profiling: 1728ms
	BeginReloadAssembly (182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (330ms)
		LoadAssemblies (384ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1134ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (620ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (52ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (430ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 5.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4366.
Memory consumption went from 133.7 MB to 133.6 MB.
Total: 6.179600 ms (FindLiveObjects: 0.399400 ms CreateObjectMapping: 0.273400 ms MarkObjects: 5.415600 ms  DeleteObjects: 0.089400 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 1204e76ae568ecb6fcb83080e0dbf4be -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.537 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.029 seconds
Domain Reload Profiling: 1564ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (278ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1029ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (16ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 3.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4377.
Memory consumption went from 133.7 MB to 133.6 MB.
Total: 6.144300 ms (FindLiveObjects: 0.376200 ms CreateObjectMapping: 0.241500 ms MarkObjects: 5.436500 ms  DeleteObjects: 0.088300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 1204e76ae568ecb6fcb83080e0dbf4be -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.520 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.145 seconds
Domain Reload Profiling: 1664ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (269ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1146ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (382ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (48ms)
Refreshing native plugins compatible for Editor in 5.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4388.
Memory consumption went from 133.7 MB to 133.7 MB.
Total: 6.112600 ms (FindLiveObjects: 0.393700 ms CreateObjectMapping: 0.242400 ms MarkObjects: 5.364200 ms  DeleteObjects: 0.110700 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 1204e76ae568ecb6fcb83080e0dbf4be -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.751 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.143 seconds
Domain Reload Profiling: 1890ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (417ms)
		LoadAssemblies (498ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (47ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 5.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (30.0 KB). Loaded Objects now: 4399.
Memory consumption went from 133.7 MB to 133.7 MB.
Total: 5.655900 ms (FindLiveObjects: 0.402100 ms CreateObjectMapping: 0.258700 ms MarkObjects: 4.906800 ms  DeleteObjects: 0.086600 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 1204e76ae568ecb6fcb83080e0dbf4be -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.514 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.978 seconds
Domain Reload Profiling: 1490ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (272ms)
		LoadAssemblies (326ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (43ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 3.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4410.
Memory consumption went from 133.7 MB to 133.7 MB.
Total: 5.557000 ms (FindLiveObjects: 0.286600 ms CreateObjectMapping: 0.120400 ms MarkObjects: 5.071300 ms  DeleteObjects: 0.077300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 1204e76ae568ecb6fcb83080e0dbf4be -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.594 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.945 seconds
Domain Reload Profiling: 1537ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (324ms)
		LoadAssemblies (395ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (945ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (38ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 3.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4421.
Memory consumption went from 133.7 MB to 133.7 MB.
Total: 5.616300 ms (FindLiveObjects: 0.322100 ms CreateObjectMapping: 0.228700 ms MarkObjects: 4.988500 ms  DeleteObjects: 0.075500 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 6ec21972e6a752a75e31bbb09b9aab4a -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.615 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.042 seconds
Domain Reload Profiling: 1654ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (309ms)
		LoadAssemblies (400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (24ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1043ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (516ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 3.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4432.
Memory consumption went from 133.8 MB to 133.7 MB.
Total: 5.974400 ms (FindLiveObjects: 0.488600 ms CreateObjectMapping: 0.259300 ms MarkObjects: 5.140500 ms  DeleteObjects: 0.084000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 6ec21972e6a752a75e31bbb09b9aab4a -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.689 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.060 seconds
Domain Reload Profiling: 1746ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (460ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1061ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (46ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 3.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4443.
Memory consumption went from 133.8 MB to 133.7 MB.
Total: 5.931600 ms (FindLiveObjects: 0.477400 ms CreateObjectMapping: 0.261300 ms MarkObjects: 5.122500 ms  DeleteObjects: 0.069500 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.754 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.197 seconds
Domain Reload Profiling: 1948ms
	BeginReloadAssembly (236ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (402ms)
		LoadAssemblies (491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (43ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1197ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (631ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (54ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (426ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4454.
Memory consumption went from 133.8 MB to 133.8 MB.
Total: 6.834300 ms (FindLiveObjects: 0.656900 ms CreateObjectMapping: 0.416500 ms MarkObjects: 5.655600 ms  DeleteObjects: 0.103100 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.767 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.110 seconds
Domain Reload Profiling: 1875ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (437ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (43ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1111ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (19ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 3.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (30.0 KB). Loaded Objects now: 4465.
Memory consumption went from 133.8 MB to 133.8 MB.
Total: 5.582400 ms (FindLiveObjects: 0.432700 ms CreateObjectMapping: 0.276600 ms MarkObjects: 4.766500 ms  DeleteObjects: 0.104300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.766 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.100 seconds
Domain Reload Profiling: 1864ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (508ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (40ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1101ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (46ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 3.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4476.
Memory consumption went from 133.8 MB to 133.8 MB.
Total: 5.686100 ms (FindLiveObjects: 0.513500 ms CreateObjectMapping: 0.301600 ms MarkObjects: 4.797800 ms  DeleteObjects: 0.071900 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.533 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.075 seconds
Domain Reload Profiling: 1606ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1076ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (334ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 3.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4487.
Memory consumption went from 133.9 MB to 133.8 MB.
Total: 6.021800 ms (FindLiveObjects: 0.425200 ms CreateObjectMapping: 0.340700 ms MarkObjects: 5.168600 ms  DeleteObjects: 0.085800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.718 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.992 seconds
Domain Reload Profiling: 1708ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (993ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (534ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (37ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (20ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 3.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4498.
Memory consumption went from 133.9 MB to 133.8 MB.
Total: 5.818000 ms (FindLiveObjects: 0.430800 ms CreateObjectMapping: 0.234500 ms MarkObjects: 5.053600 ms  DeleteObjects: 0.097100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.619 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:607)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:598)
RealSoftGames.AdvancedLevelingSystem.SawtoothAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/SawtoothAlgorithm.cs:44)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.992 seconds
Domain Reload Profiling: 1610ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (423ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (992ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 3.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3662 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4509.
Memory consumption went from 133.9 MB to 133.9 MB.
Total: 6.172000 ms (FindLiveObjects: 0.412300 ms CreateObjectMapping: 0.229900 ms MarkObjects: 5.437400 ms  DeleteObjects: 0.090300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/ProgressionTabManager.cs: be5f9e1758f38442c850fe0faff5aef1 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 5316fa61b7f9a66e28f319c8398c1206 -> 892a5ebdfeba90f0e3ecd19484569cc8
