Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.62f1 (4af31df58517) revision 4911901'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 32560 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.62f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/Projects/levelingsystem
-logFile
Logs/AssetImportWorker4.log
-srvPort
55412
Successfully changed project path to: D:/Projects/levelingsystem
D:/Projects/levelingsystem
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19692]  Target information:

Player connection [19692]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3494862750 [EditorId] 3494862750 [Version] 1048832 [Id] WindowsEditor(7,Krazor) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19692] Host joined multi-casting on [***********:54997]...
Player connection [19692] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 9.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Projects/levelingsystem/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 Ti Laptop GPU (ID=0x2420)
    Vendor:   NVIDIA
    VRAM:     16175 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56420
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.023065 seconds.
- Loaded All Assemblies, in  0.541 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 365 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.032 seconds
Domain Reload Profiling: 1572ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (230ms)
		LoadAssemblies (163ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (222ms)
				TypeCache.ScanAssembly (203ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1033ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (963ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (554ms)
			SetLoadedEditorAssemblies (21ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (9ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.820 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

ScriptableAlgorithmRegistry: Already registering an algorithm, skipping recursive call
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:443)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:123)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)
UnityEngine.ResourcesAPI:Load (string,System.Type)
UnityEngine.Resources:Load (string,System.Type)
UnityEngine.Resources:Load<RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabase> (string)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:29)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 443)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.073 seconds
Domain Reload Profiling: 1891ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (553ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (266ms)
			TypeCache.Refresh (228ms)
				TypeCache.ScanAssembly (202ms)
			ScanForSourceGeneratedMonoScriptInfo (28ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1073ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (862ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (58ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (636ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 5.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (56.1 KB). Loaded Objects now: 4196.
Memory consumption went from 138.1 MB to 138.1 MB.
Total: 5.634000 ms (FindLiveObjects: 0.376000 ms CreateObjectMapping: 0.180500 ms MarkObjects: 4.919800 ms  DeleteObjects: 0.155800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 349233.117305 seconds.
  path: Assets/RealSoftGames/AdvancedLevelingSystem/Demo/Scenes/ProjectSetup_Prefabs.unity
  artifactKey: Guid(660517c2a028b394c99b16fa28481d93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealSoftGames/AdvancedLevelingSystem/Demo/Scenes/ProjectSetup_Prefabs.unity using Guid(660517c2a028b394c99b16fa28481d93) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '0425585a92136fa4da44152459599a4f') in 0.002371 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.654 seconds
Native extension for UWP target not found
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Failed to load: C:/Program Files/Unity/Hub/Editor/2022.3.62f1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:74)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs line 74]

Error creating difficulty rating database: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/DifficultyRatingDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager.CreateDatabase () [0x0007e] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\DifficultyRatingDatabaseManager.cs:74 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:96)
RealSoftGames.AdvancedLevelingSystem.DifficultyRatingDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/DifficultyRatingDatabaseManager.cs Line: 96)

CreateSerializedAsset is not supported while importing out-of-process
UnityEngine.StackTraceUtility:ExtractStackTrace ()
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:CreateDatabase () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:65)
RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager:.cctor () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs:33)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Editor/AlgorithmDatabaseManager.cs line 65]

UnityEngine.UnityException: Creating asset at path Assets/RealSoftGames/AdvancedLevelingSystem/Resources/AlgorithmDatabase.asset failed.
  at (wrapper managed-to-native) UnityEditor.AssetDatabase.CreateAsset(UnityEngine.Object,string)
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager.CreateDatabase () [0x00069] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:65 
  at RealSoftGames.AdvancedLevelingSystem.AlgorithmDatabaseManager..cctor () [0x00018] in D:\Projects\levelingsystem\Assets\RealSoftGames\AdvancedLevelingSystem\Scripts\Editor\AlgorithmDatabaseManager.cs:33 
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

ScriptableAlgorithmRegistry: AlgorithmDatabase not found in Resources folder. Please create it first.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize (bool) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:88)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:Initialize () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:41)
RealSoftGames.AdvancedLevelingSystem.ScriptableAlgorithmRegistry:RegisterAlgorithm (RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase) (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs:460)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:RegisterWithRegistry () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:650)
RealSoftGames.AdvancedLevelingSystem.LevelingAlgorithmBase:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Core/LevelingAlgorithmBase.cs:641)
RealSoftGames.AdvancedLevelingSystem.AdaptiveAlgorithm:OnEnable () (at Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Implementations/AdaptiveAlgorithm.cs:48)

(Filename: Assets/RealSoftGames/AdvancedLevelingSystem/Scripts/Algorithms/Utilities/ScriptableAlgorithmRegistry.cs Line: 88)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.513 seconds
Domain Reload Profiling: 2164ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (335ms)
		LoadAssemblies (426ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1514ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (811ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (73ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (557ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (53ms)
Refreshing native plugins compatible for Editor in 6.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3661 Unused Serialized files (Serialized files now loaded: 0)
Unloading 19 unused Assets / (29.9 KB). Loaded Objects now: 4201.
Memory consumption went from 133.1 MB to 133.1 MB.
Total: 9.288700 ms (FindLiveObjects: 0.383800 ms CreateObjectMapping: 0.265500 ms MarkObjects: 8.533300 ms  DeleteObjects: 0.104600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
